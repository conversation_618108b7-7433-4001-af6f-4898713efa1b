import { getShiftReportDaily, submitShiftHandover, getShiftReportById } from '../../api/employeeShift';
import type { ShiftReportDailyResponse, ShiftReportDaily } from '../../entity/EmployeeShiftEntity';
import { ElMessage } from 'element-plus';
import { postApiPrintRecordCheckoutPayBillIds, postApiPrintRecordCheckoutCreate } from '@/api/autoGenerated';
import type { GetCheckoutPrintRecordsByPayBillIdsReqDto, CreateCheckoutPrintRecordReqDto, CheckoutPrintRecordVO } from '@/api/autoGenerated';
import { useVenueStore } from '@/stores/venueStore';

/**
 * 员工交班业务交互层
 */
export class EmployeeShiftInteractor {
  /**
   * 获取交班报告数据
   * @param date 日期（时间戳）
   * @param employeeId 员工ID
   * @param venueId 场地ID
   * @returns 交班报告响应
   */
  static async getShiftReport(date: number, employeeId: string, venueId: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportDaily({
      date,
      employeeId,
      venueId
    });
  }

  /**
   * 通过交班单ID获取交班报告数据
   * @param shiftId 交班单ID
   * @param handNo 交班单号（可选）
   * @returns 交班报告响应
   */
  static async getShiftReportById(shiftId: string, handNo?: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportById(shiftId, handNo);
  }

  /**
   * 提交交班上报
   * @param date 日期
   * @param employeeId 员工ID
   * @param venueId 场馆ID
   * @param shiftReportData 交班数据
   * @returns 上报结果
   */
  static async submitShiftHandover(
    date: number,
    employeeId: string,
    venueId: string,
    shiftReportData: ShiftReportDaily[]
  ): Promise<any> {
    return await submitShiftHandover({
      date,
      employeeId,
      venueId,
      shiftReportDaily: shiftReportData
    });
  }

  /**
   * 打印交班报告
   * 这里可以实现具体的打印业务逻辑
   */
  static printShiftReport(reportData: any): void {
    // 打印业务逻辑实现
    // 可能需要调用打印服务API或使用浏览器打印功能
    console.log('打印交班报告:', reportData);
  }

  /**
   * 导出交班报告
   * 这里可以实现具体的导出业务逻辑
   */
  static exportShiftReport(reportData: any): void {
    // 导出业务逻辑实现
    // 可能需要生成Excel或CSV文件并触发下载
    console.log('导出交班报告:', reportData);
  }

  /**
   * 打印结账单 - 使用批量查询接口优化流程
   * @param billIds 账单ID列表
   * @param sessionId 会话ID
   * @returns 打印结果
   */
  static async printCheckoutBills(billIds: string[], sessionId: string): Promise<void> {
    if (!billIds || billIds.length === 0) {
      throw new Error('没有可打印的账单');
    }

    if (!sessionId) {
      throw new Error('会话ID不能为空');
    }

    console.log('[EmployeeShiftInteractor] 准备打印账单:', {
      billIds,
      sessionId,
      count: billIds.length
    });

    // 获取场馆ID
    const venueStore = useVenueStore();
    const venueId = venueStore.venueId;

    if (!venueId) {
      throw new Error('无法获取场馆ID');
    }

    try {
      // 1. 批量查询所有账单的打印记录
      const existingRecords = await this.batchQueryPrintRecords(billIds, venueId);

      // 2. 分析哪些账单需要创建打印记录
      const { needCreateBillIds } = this.analyzePrintRecords(billIds, existingRecords);

      // 3. 批量创建缺失的打印记录
      const newRecords = await this.batchCreatePrintRecords(needCreateBillIds, sessionId, venueId);

      // 4. 合并所有打印记录
      const allRecords = [...existingRecords, ...newRecords];

      // 5. 执行批量打印
      const results = await this.batchExecutePrint(allRecords);

      // 6. 统计并显示结果
      this.showPrintResults(results);

    } catch (error) {
      console.error('[EmployeeShiftInteractor] 批量打印失败:', error);
      throw error;
    }
  }

  /**
   * 批量查询打印记录
   * @param billIds 账单ID列表
   * @param venueId 场馆ID
   * @returns 已有的打印记录列表
   */
  private static async batchQueryPrintRecords(billIds: string[], venueId: string): Promise<CheckoutPrintRecordVO[]> {
    try {
      const params: GetCheckoutPrintRecordsByPayBillIdsReqDto = {
        payBillIds: billIds,
        venueId
      };

      console.log('[EmployeeShiftInteractor] 批量查询打印记录:', params);
      const response = await postApiPrintRecordCheckoutPayBillIds(params);

      if (response && response.data) {
        console.log(`[EmployeeShiftInteractor] 查询到 ${response.data.length} 条已有打印记录`);
        return response.data;
      } else {
        console.log('[EmployeeShiftInteractor] 未查询到已有打印记录');
        return [];
      }
    } catch (error) {
      console.error('[EmployeeShiftInteractor] 批量查询打印记录失败:', error);
      return [];
    }
  }

  /**
   * 分析打印记录，确定哪些账单需要创建新记录
   * @param billIds 所有账单ID
   * @param existingRecords 已有的打印记录
   * @returns 分析结果
   */
  private static analyzePrintRecords(billIds: string[], existingRecords: CheckoutPrintRecordVO[]): {
    needCreateBillIds: string[];
    existingRecordsMap: Map<string, CheckoutPrintRecordVO>;
  } {
    // 创建已有记录的映射
    const existingRecordsMap = new Map<string, CheckoutPrintRecordVO>();
    existingRecords.forEach(record => {
      if (record.payBillId) {
        existingRecordsMap.set(record.payBillId, record);
      }
    });

    // 找出需要创建记录的账单ID
    const needCreateBillIds = billIds.filter(billId => !existingRecordsMap.has(billId));

    console.log('[EmployeeShiftInteractor] 打印记录分析:', {
      totalBills: billIds.length,
      existingRecords: existingRecords.length,
      needCreate: needCreateBillIds.length,
      needCreateBillIds
    });

    return { needCreateBillIds, existingRecordsMap };
  }

  /**
   * 批量创建打印记录
   * @param billIds 需要创建记录的账单ID列表
   * @param sessionId 会话ID
   * @param venueId 场馆ID
   * @returns 新创建的打印记录列表
   */
  private static async batchCreatePrintRecords(billIds: string[], sessionId: string, venueId: string): Promise<CheckoutPrintRecordVO[]> {
    if (billIds.length === 0) {
      console.log('[EmployeeShiftInteractor] 无需创建新的打印记录');
      return [];
    }

    console.log(`[EmployeeShiftInteractor] 开始批量创建 ${billIds.length} 条打印记录`);

    // 并行创建所有需要的打印记录
    const createPromises = billIds.map(async (billId) => {
      try {
        const params: CreateCheckoutPrintRecordReqDto = {
          payBillId: billId,
          sessionId,
          venueId,
          orderNos: []
        };

        console.log(`[EmployeeShiftInteractor] 创建账单 ${billId} 的打印记录`);
        const response = await postApiPrintRecordCheckoutCreate(params);

        if (response && response.data) {
          console.log(`[EmployeeShiftInteractor] 账单 ${billId} 创建打印记录成功:`, response.data.id);
          return response.data;
        } else {
          throw new Error(`创建打印记录失败: ${response?.statusText || '未知错误'}`);
        }
      } catch (error) {
        console.error(`[EmployeeShiftInteractor] 创建账单 ${billId} 打印记录失败:`, error);
        throw error;
      }
    });

    const newRecords = await Promise.all(createPromises);
    console.log(`[EmployeeShiftInteractor] 成功创建 ${newRecords.length} 条打印记录`);

    return newRecords;
  }

  /**
   * 批量执行打印
   * @param records 所有打印记录
   * @returns 打印结果列表
   */
  private static async batchExecutePrint(records: CheckoutPrintRecordVO[]): Promise<Array<{ billId: string; success: boolean; error?: string }>> {
    console.log(`[EmployeeShiftInteractor] 开始批量执行 ${records.length} 条打印任务`);

    const printPromises = records.map(async (record) => {
      try {
        const billId = record.payBillId || 'unknown';

        if (!record.checkoutBillData) {
          console.warn(`[EmployeeShiftInteractor] 账单 ${billId} 打印记录缺少账单数据`);
          return { billId, success: false, error: '缺少账单数据' };
        }

        console.log(`[EmployeeShiftInteractor] 执行打印账单: ${billId}，记录ID: ${record.id}`);

        // 执行实际打印逻辑
        // 这里可以根据需要调用具体的打印服务
        // 例如：调用打印机打印、生成PDF、发送到打印队列等

        // 目前模拟打印成功
        console.log(`[EmployeeShiftInteractor] 账单 ${billId} 打印成功`);
        return { billId, success: true };

      } catch (error) {
        const billId = record.payBillId || 'unknown';
        console.error(`[EmployeeShiftInteractor] 账单 ${billId} 打印异常:`, error);
        return {
          billId,
          success: false,
          error: error instanceof Error ? error.message : '打印异常'
        };
      }
    });

    return await Promise.all(printPromises);
  }

  /**
   * 显示打印结果
   * @param results 打印结果列表
   */
  private static showPrintResults(results: Array<{ billId: string; success: boolean; error?: string }>): void {
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    console.log('[EmployeeShiftInteractor] 打印结果统计:', {
      total: results.length,
      success: successCount,
      fail: failCount,
      results
    });

    // 显示打印结果
    if (failCount === 0) {
      ElMessage.success(`成功打印 ${successCount} 张账单`);
    } else if (successCount === 0) {
      throw new Error(`所有账单打印失败`);
    } else {
      ElMessage.warning(`成功打印 ${successCount} 张账单，失败 ${failCount} 张`);
    }
  }
}