<template>
  <AppDialog v-model="dialogVisible" title="账单还原" :destroy-on-close="true" class="bill-restore-confirm-dialog !h-[72%]" @close="handleDialogClose">
    <div class="p-[12px]">
      <!-- 支付方式选择区域 -->
      <div>
        <div
          v-for="(payment, index) in refundablePayments"
          :key="index"
          class="bg-gray-100 rounded-[6px] p-[12px] mb-[12px] flex justify-between items-center cursor-pointer">
          <div class="flex items-center">
            <div class="text-gray-600">{{ payment.name }} 可退: {{ (payment.amount / 100).toFixed(2) }}</div>
          </div>
          <div class="text-lg">¥ {{ (payment.amount / 100).toFixed(2) }}</div>
        </div>
      </div>

      <!-- 还原原因 -->
      <div class="mt-4">
        <div class="text-gray-600 mb-[12px]">还原原因</div>
        <div class="mb-4">
          <el-input
            v-model="restoreReason"
            type="textarea"
            :rows="3"
            placeholder="请输入还原原因，不得超过200个字"
            maxlength="200"
            show-word-limit
            class="p-[12px]" />
        </div>
        <div class="flex flex-wrap gap-2">
          <el-button
            v-for="reason in restoreReasons"
            :key="reason"
            round
            :type="selectedReason === reason ? 'primary' : 'default'"
            @click="handleReasonClick(reason)">
            {{ reason }}
          </el-button>
          <el-button round :type="selectedReason === '其他' ? 'primary' : 'default'" @click="handleReasonClick('其他')"> 其他 </el-button>
        </div>
      </div>

      <!-- 授权人选择 -->
      <div class="mt-[24px]">
        <div class="text-gray-600 mb-[12px]">授权人</div>
        <el-select v-model="authorizer" placeholder="请选择" class="customer-select">
          <el-option v-for="item in authorizerOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center items-center">
        <div>
          <button class="btn-default" @click="handleConfirm" :loading="loading" :disabled="!isFormValid">确认选择</button>
        </div>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { AppDialog } from '@/components/Dialog';
import { useOrderApi } from '@/modules/order/api/index';
import { ElMessage } from 'element-plus';
import type { BaseResponse } from '@/types/baseResponse';
import type { PayRecordVO } from '@/api/autoGenerated/shared/types';
import { payTypeMap, PayType } from '@/utils/constant/payTyps';
import { useUserStore } from '@/stores/userStore';

// 使用用户存储
const userStore = useUserStore();

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  amount: {
    type: Number,
    default: 0
  },
  billIds: {
    type: Array as () => string[],
    default: () => []
  },
  sessionId: {
    type: String,
    required: true
  },
  // 添加支付记录数据属性
  payRecords: {
    type: Array as () => PayRecordVO[],
    default: () => []
  },
  // 添加是否为退款操作的标识
  isRefundOperation: {
    type: Boolean,
    default: false
  }
});

// 定义emit
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

// 组件状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val)
});
const loading = ref(false);
const restoreReason = ref('');
const selectedPayment = ref('');
const authorizer = ref('');

// API服务
const { billApi } = useOrderApi();

// 授权人信息（当前登录用户）
const currentUser = computed(() => {
  const employee = userStore.userInfo.employee;
  return {
    id: employee?.id || '',
    name: employee?.name || userStore.userInfo.name || ''
  };
});

// 授权人选项 - 使用当前登录用户
const authorizerOptions = computed(() => {
  if (!currentUser.value.id) return [{ label: '请先登录', value: '' }];

  return [{ label: currentUser.value.name, value: currentUser.value.id }];
});

// 计算可退款的支付方式和已退金额
const refundablePayments = computed(() => {
  // 按支付方式分组，计算每种支付方式的可退金额
  const paymentMap = new Map<string, { type: string; name: string; amount: number; refundedAmount: number }>();

  // 先获取所有支付记录
  const allRecords = [...props.payRecords];

  // 处理支付和退款记录
  allRecords.forEach(record => {
    if (record.payType) {
      const payType = record.payType;
      const payName = getPayTypeText(payType);
      const amount = record.totalFee || 0;
      const isRefund = !!record.payPid;

      if (paymentMap.has(payType)) {
        const current = paymentMap.get(payType)!;
        if (isRefund) {
          // 退款记录，增加已退金额
          paymentMap.set(payType, {
            ...current,
            refundedAmount: current.refundedAmount + amount
          });
        } else {
          // 支付记录，增加可退金额
          paymentMap.set(payType, {
            ...current,
            amount: current.amount + amount
          });
        }
      } else {
        paymentMap.set(payType, {
          type: payType,
          name: payName,
          amount: isRefund ? 0 : amount,
          refundedAmount: isRefund ? amount : 0
        });
      }
    }
  });

  // 计算实际可退金额（总金额减去已退金额）
  return Array.from(paymentMap.values())
    .filter(payment => payment.amount > payment.refundedAmount)
    .map(payment => ({
      ...payment,
      // 实际可退金额 = 原始金额 - 已退金额
      amount: payment.amount - payment.refundedAmount
    }));
});

// 表单验证
const isFormValid = computed(() => {
  return restoreReason.value.trim() !== '' && authorizer.value !== '';
});

// 获取支付方式文本
const getPayTypeText = (payType: string) => {
  return payTypeMap[payType as PayType] || payType;
};

// 预设的还原原因列表
const restoreReasons = ['操作错误', '客户投诉', '系统故障'];
const selectedReason = ref('');

// 处理原因按钮点击
const handleReasonClick = (reason: string) => {
  selectedReason.value = reason;
  if (reason !== '其他') {
    if (restoreReason.value) {
      restoreReason.value += `、${reason}`;
    } else {
      restoreReason.value = reason;
    }
  }
};

// 监听对话框状态变更
watch(dialogVisible, val => {
  if (!val) {
    // 重置表单
    restoreReason.value = '';
    selectedReason.value = '';
    selectedPayment.value = '';
    authorizer.value = '';
  } else {
    // 默认选择当前用户作为授权人
    if (currentUser.value.id) {
      authorizer.value = currentUser.value.id;
    }

    // 默认选择第一个支付方式
    if (refundablePayments.value.length > 0) {
      selectedPayment.value = refundablePayments.value[0].type;
    }
  }
});

// 对话框关闭处理
const handleDialogClose = () => {
  emit('update:modelValue', false);
  emit('cancel');
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
  dialogVisible.value = false;
};

// 确认操作
const handleConfirm = async () => {
  if (!isFormValid.value) return;

  loading.value = true;
  try {
    // 调用账单还原API，添加sessionId参数
    const response = await billApi.billBack({
      billIds: props.billIds,
      sessionId: props.sessionId
    });

    if (response.code === 0) {
      ElMessage.success('账单还原成功');
      emit('confirm');
      dialogVisible.value = false;
    } else {
      ElMessage.error(response.message || '账单还原失败');
    }
  } catch (error) {
    console.error('账单还原异常:', error);
    ElMessage.error('系统异常，请稍后重试');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.bill-restore-confirm-dialog :deep(.el-textarea__inner) {
  min-height: 80px;
}

.text-primary {
  color: var(--el-color-primary);
}

.border-primary {
  border-color: var(--el-color-primary);
}

.border-red-500 {
  border-color: #ef4444;
}

.bg-primary-50 {
  background-color: var(--el-color-primary-light-9);
}
</style>
