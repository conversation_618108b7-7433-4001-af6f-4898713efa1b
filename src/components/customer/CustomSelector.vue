<template>
  <div class="custom-selector-wrapper">
    <ElSelect
      v-model="innerValue"
      class="custom-selector"
      :placeholder="placeholder"
      :popper-class="uniquePopperClass"
      @change="handleChange"
      :disabled="disabled">
      <template #prefix>
        <div class="custom-label">{{ label }}</div>
      </template>
      <ElOption v-for="option in options" :key="option.value" :label="option.label" :value="option.value" />
    </ElSelect>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElSelect, ElOption } from 'element-plus';

interface Option {
  label: string;
  value: string | number;
}

// 生成唯一ID用于下拉菜单样式隔离
const uniqueId = ref(`select-${Date.now()}-${Math.floor(Math.random() * 10000)}`);
const uniquePopperClass = computed(() => `custom-dropdown-${uniqueId.value}`);

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '可选择'
  },
  options: {
    type: Array as () => Option[],
    default: () => []
  },
  width: {
    type: Number,
    default: 154
  },
  height: {
    type: Number,
    default: 60
  },
  borderRadius: {
    type: Number,
    default: 8
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const innerValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val)
});

const handleChange = (value: string | number) => {
  emit('change', value);
};
</script>

<style scoped>
.custom-selector-wrapper {
  position: relative;
}

.custom-selector {
  width: 100%;
}

.custom-label {
  position: absolute;
  top: 12px;
  left: 18px;
  font-size: 12px;
  color: #000;
  font-weight: normal;
  line-height: 1;
  pointer-events: none;
  z-index: 2;
  font-weight: 500;
}

:deep(.custom-selector .el-select__wrapper) {
  width: 160px !important;
  height: 60px !important;
  border-radius: 8px !important;
}

:deep(.custom-selector .el-select__selection) {
  margin-top: 16px !important;
  color: #000 !important;
}

:deep(.custom-selector .el-select__selection span) {
  font-size: 16px !important;
  color: #333 !important;
}
</style>
