import { getShiftReportDaily, submitShiftHandover, getShiftReportById } from '../../api/employeeShift';
import type { ShiftReportDailyResponse, ShiftReportDaily } from '../../entity/EmployeeShiftEntity';
import { AxiosResponse } from 'axios';
import { PrintingService } from '@/application/printingService';
import { ElMessage } from 'element-plus';

/**
 * 员工交班业务交互层
 */
export class EmployeeShiftInteractor {
  private static printingService = new PrintingService();
  /**
   * 获取交班报告数据
   * @param date 日期（时间戳）
   * @param employeeId 员工ID
   * @param venueId 场地ID
   * @returns 交班报告响应
   */
  static async getShiftReport(date: number, employeeId: string, venueId: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportDaily({
      date,
      employeeId,
      venueId
    });
  }

  /**
   * 通过交班单ID获取交班报告数据
   * @param shiftId 交班单ID
   * @param handNo 交班单号（可选）
   * @returns 交班报告响应
   */
  static async getShiftReportById(shiftId: string, handNo?: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportById(shiftId, handNo);
  }

  /**
   * 提交交班上报
   * @param date 日期
   * @param employeeId 员工ID
   * @param venueId 场馆ID
   * @param shiftReportData 交班数据
   * @returns 上报结果
   */
  static async submitShiftHandover(
    date: number,
    employeeId: string,
    venueId: string,
    shiftReportData: ShiftReportDaily[]
  ): Promise<any> {
    return await submitShiftHandover({
      date,
      employeeId,
      venueId,
      shiftReportDaily: shiftReportData
    });
  }

  /**
   * 打印交班报告
   * 这里可以实现具体的打印业务逻辑
   */
  static printShiftReport(reportData: any): void {
    // 打印业务逻辑实现
    // 可能需要调用打印服务API或使用浏览器打印功能
    console.log('打印交班报告:', reportData);
  }

  /**
   * 导出交班报告
   * 这里可以实现具体的导出业务逻辑
   */
  static exportShiftReport(reportData: any): void {
    // 导出业务逻辑实现
    // 可能需要生成Excel或CSV文件并触发下载
    console.log('导出交班报告:', reportData);
  }

  /**
   * 打印结账单
   * @param billIds 账单ID列表
   * @param sessionId 会话ID
   * @returns 打印结果
   */
  static async printCheckoutBills(billIds: string[], sessionId: string): Promise<void> {
    if (!billIds || billIds.length === 0) {
      throw new Error('没有可打印的账单');
    }

    if (!sessionId) {
      throw new Error('会话ID不能为空');
    }

    console.log('[EmployeeShiftInteractor] 准备打印账单:', {
      billIds,
      sessionId,
      count: billIds.length
    });

    // 为每个账单ID调用打印服务
    const printPromises = billIds.map(async (billId) => {
      try {
        console.log(`[EmployeeShiftInteractor] 开始打印账单: ${billId}`);

        // 调用打印服务的结账单打印方法
        // payBillId 就是 billId（结账单号）
        const success = await this.printingService.printCheckoutBillByPayBillId(
          billId,     // payBillId - 结账单号
          sessionId,  // sessionId - 会话ID
          []         // orderNos - 订单号数组（可选，这里传空数组）
        );

        if (success) {
          console.log(`[EmployeeShiftInteractor] 账单 ${billId} 打印成功`);
          return { billId, success: true };
        } else {
          console.warn(`[EmployeeShiftInteractor] 账单 ${billId} 打印失败`);
          return { billId, success: false, error: '打印失败' };
        }
      } catch (error) {
        console.error(`[EmployeeShiftInteractor] 账单 ${billId} 打印异常:`, error);
        return {
          billId,
          success: false,
          error: error instanceof Error ? error.message : '打印异常'
        };
      }
    });

    // 等待所有打印任务完成
    const results = await Promise.all(printPromises);

    // 统计打印结果
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    console.log('[EmployeeShiftInteractor] 打印结果统计:', {
      total: results.length,
      success: successCount,
      fail: failCount,
      results
    });

    // 显示打印结果
    if (failCount === 0) {
      ElMessage.success(`成功打印 ${successCount} 张账单`);
    } else if (successCount === 0) {
      throw new Error(`所有账单打印失败`);
    } else {
      ElMessage.warning(`成功打印 ${successCount} 张账单，失败 ${failCount} 张`);
    }
  }
}