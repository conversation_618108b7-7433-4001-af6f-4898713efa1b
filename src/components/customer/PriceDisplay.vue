<!-- PriceDisplay.vue -->
<template>
  <div class="price-display flex items-baseline">
    <span class="price-unit text-sm text-gray-500">{{ currencySymbol }}</span>
    <span class="price-integer text-lg text-black">{{ integerPart }}</span>
    <span class="price-decimal text-sm text-gray-500">{{ decimalPart }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 以分为单位的价格（优先级高于 amountInYuan）
  amountInFen: {
    type: [Number, null, undefined],
    default: null
  },
  // 以元为单位的价格
  amountInYuan: {
    type: [Number, null, undefined],
    default: 0
  },
  // 兼容旧版用法，等同于 amountInYuan
  amount: {
    type: [Number, null, undefined],
    default: null
  },
  currencySymbol: {
    type: String,
    default: '¥'
  }
});

// 计算最终的分值
const amountInFenValue = computed(() => {
  // 优先使用 amountInFen
  if (props.amountInFen !== null && props.amountInFen !== undefined && !isNaN(props.amountInFen)) {
    return Number(props.amountInFen);
  }

  // 其次使用 amount（如果提供）
  if (props.amount !== null && props.amount !== undefined && !isNaN(props.amount)) {
    return Math.round(Number(props.amount) * 100);
  }

  // 最后使用 amountInYuan
  if (props.amountInYuan !== null && props.amountInYuan !== undefined && !isNaN(props.amountInYuan)) {
    return Math.round(Number(props.amountInYuan) * 100);
  }

  // 都没有提供，返回0
  return 0;
});

const integerPart = computed(() => {
  return Math.floor(amountInFenValue.value / 100);
});

const decimalPart = computed(() => {
  const decimal = String((amountInFenValue.value / 100).toFixed(2)).split('.')[1];
  return '.' + (decimal || '00');
});

// 添加默认导出
defineExpose({
  amountInFenValue,
  integerPart,
  decimalPart
});
</script>

<style scoped>
/* 默认样式，可以被外部CSS覆盖 */
.price-display {
  display: flex;
  align-items: baseline;
}

.price-unit {
  /* 默认单位样式 */
  font-size: 0.875rem;
  /* 对应text-sm */
  color: #6b7280;
  /* 对应text-gray-500 */
}

.price-integer {
  /* 默认整数部分样式 */
  font-size: 1.125rem;
  /* 对应text-lg */
  color: #000000;
  /* 对应text-black */
}

.price-decimal {
  /* 默认小数部分样式 */
  font-size: 0.875rem;
  /* 对应text-sm */
  color: #6b7280;
  /* 对应text-gray-500 */
}

.price-display-56 .price-unit {
  font-size: 24px !important;
}

.price-display-56 .price-integer {
  font-size: 56px !important;
  font-weight: bold !important;
}

.price-display-56 .price-decimal {
  font-size: 24px !important;
}

.price-display-large .price-unit {
  font-size: 1rem !important;
  margin-right: 4px;
}

.price-display-large .price-integer {
  font-size: 36px !important;
  font-weight: bold !important;
}

.price-display-large .price-decimal {
  font-size: 1rem !important;
}

.price-display-normal .price-unit {
  font-size: 14px;
}

.price-display-normal .price-integer {
  font-size: 24px;
  font-weight: bold;
}

.price-display-normal .price-decimal {
  font-size: 14px;
}

/** 小号 */
.price-display-small .price-unit {
  font-size: 12px !important;
}

.price-display-small .price-integer {
  font-size: 16px !important;
}

.price-display-small .price-decimal {
  font-size: 12px !important;
}

/* 主色调价格 */
.price-display-primary .price-unit,
.price-display-primary .price-integer,
.price-display-primary .price-decimal {
  color: #000 !important;
}

/* 红色价格 */
.price-display-red .price-unit,
.price-display-red .price-integer,
.price-display-red .price-decimal {
  color: #f56c6c !important;
}

/* 右对齐价格样式 */
.price-display-right-align {
  justify-content: flex-end !important;
  width: 100% !important;
  text-align: right !important;
}

/* 居中价格 */
.price-display-center-align {
  justify-content: center !important;
  width: 100% !important;
  text-align: center !important;
}
</style>
