<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, watch, nextTick } from 'vue';
import { RouterView, useRouter, useRoute } from 'vue-router';
import NProgress from 'nprogress';
import DialogContainer from '@/components/Dialog/core/DialogContainer.vue';
import { useVersionCheck } from '@/composables/useVersionCheck';
import dayjs from 'dayjs';

// Imports from main App.vue
import NatsService from '@/services/nats-service';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore, type DeviceInfo } from '@/stores/deviceStore'; // Added DeviceInfo type
import NatsListenerService from '@/application/natsListenerService';

const route = useRoute(); // Added
const router = useRouter();

// Initialize services and stores (from main App.vue)
const nats = NatsService.getInstance();
const userStore = useUserStore();
const deviceStore = useDeviceStore();
let deviceInfo = ref<DeviceInfo | null>(null); // Changed to ref

// 使用版本检测 composable，获取更多版本信息
const { isUpdateDetected, currentVersion, currentReleaseTimestamp, latestVersion, latestReleaseTimestamp } = useVersionCheck({
  onUpdateDetected: (currentVersion, newVersion, currentTimestamp, newTimestamp) => {
    console.log(`[Client Pad] 检测到新版本！当前版本: ${currentVersion} (${currentTimestamp || 'N/A'}), 最新版本: ${newVersion} (${newTimestamp || 'N/A'})`);
  },
  checkInterval: 15 * 60 * 1000,
  checkInDev: false
});

// 修复路径大小写问题
const checkPathCaseSensitivity = () => {
  // 统一路径处理 - 确保所有路径使用一致的大小写
  try {
    // 强制刷新客户端存储的模块缓存
    // 这是一个权宜之计，理想情况下应该通过统一路径命名规范解决
    console.log('[Client Pad] 检查路径大小写敏感性问题...');

    // 处理localStorage，确保没有混合大小写的键
    const checkStorage = () => {
      const authKeys = Object.keys(localStorage).filter(key => key.toLowerCase().includes('auth') || key.toLowerCase().includes('login'));

      console.log('[Client Pad] 检查到的本地存储键:', authKeys);
    };

    checkStorage();

    // 添加全局错误处理，专门捕获模块引用错误
    window.addEventListener(
      'error',
      event => {
        const errorText = event.message || '';
        const errorStack = event.error?.stack || '';

        // 特别处理loginLoading相关错误
        if (errorText.includes('loginLoading') || errorStack.includes('loginLoading')) {
          console.error('[Client Pad] 检测到loginLoading错误，尝试修复...');

          // 如果在登录页面，尝试重定向回授权页
          if (window.location.pathname.includes('/login')) {
            console.log('[Client Pad] 在登录页检测到错误，重定向到授权页');
            router.push('/auth');
            return true; // 防止错误继续传播
          }
        }

        return false;
      },
      true
    );
  } catch (error) {
    console.error('[Client Pad] 路径大小写检查失败:', error);
  }
};

// 格式化时间戳
const formattedCurrentTimestamp = computed(() => {
  if (currentReleaseTimestamp.value && currentReleaseTimestamp.value !== '') {
    try {
      return dayjs(currentReleaseTimestamp.value).format('YYYY-MM-DD HH:mm:ss');
    } catch (e) {
      return currentReleaseTimestamp.value;
    }
  }
  return 'N/A';
});

// 监听按键事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 如果按下 ESC 键
  if (event.key === 'Escape') {
    console.log('[Client Pad] ESC key pressed');
    // 这里可以添加处理逻辑，如显示退出确认对话框
  }
};

// === Logic from main App.vue - Adapted for Pad ===
const checkAuthStatus = async () => {
  const token = userStore.token;
  // Pad might not use venueId directly in the same way, adjust if needed.
  // For now, let's assume deviceStore has relevant properties for pad auth.
  // Example: Check for a specific device grant or property.
  const isDeviceAuthorized = deviceStore.deviceId && deviceStore.ipAddress; // Simplified check for pad

  if (!isDeviceAuthorized) {
    console.log('[Client Pad] Device not authorized, redirecting to auth page');
    // Ensure router is available and initialized
    if (router) await router.push('/auth');
    return false;
  }

  if (!token) {
    console.log('[Client Pad] No token found, redirecting to login page');
    if (router) await router.push('/login');
    return false;
  }
  return true;
};

const initApp = async () => {
  console.log('[Client Pad] Starting app initialization...');
  NProgress.start();

  // deviceInfo is now a ref
  if (deviceInfo.value && deviceInfo.value.deviceId && deviceInfo.value.macAddress && deviceInfo.value.ipAddress) {
    console.log('[Client Pad] Valid device info, continuing initialization...', deviceInfo.value);
    try {
      await userStore.initUserState();
      console.log('[Client Pad] User state initialized');
      await checkAuthStatus(); // Auth check for pad
    } catch (error) {
      console.error('[Client Pad] Error during app initialization:', error);
    } finally {
      NProgress.done();
    }
  } else {
    console.log('[Client Pad] Invalid or missing device info, skipping full initialization. DeviceInfo:', deviceInfo.value);
    // Attempt to redirect to auth if device info is fundamentally missing
    if (!deviceStore.deviceId && router) {
      console.log('[Client Pad] Device ID missing, redirecting to auth for setup.');
      await router.push('/auth');
    }
    NProgress.done();
  }
};

const handleUrlParams = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.has('deviceId') && urlParams.has('macAddress') && urlParams.has('ipAddress')) {
    console.log('[Client Pad] Found params in URL, initializing device store...');
    const params = {
      deviceId: urlParams.get('deviceId') as string,
      macAddress: urlParams.get('macAddress') as string,
      ipAddress: urlParams.get('ipAddress') as string,
      location: urlParams.get('location') as string, // Optional
      deviceType: urlParams.get('deviceType') as string, // Optional
      appVersion: urlParams.get('appVersion') as string // Optional
    };
    // @ts-ignore
    deviceStore.initDeviceInfo(params);
    deviceInfo.value = params; // Update local ref
    await initApp();
    return true;
  }
  return false;
};

watch(
  () => route.query,
  async newQuery => {
    await nextTick();
    console.log('[Client Pad] Route query changed:', newQuery);
    if (newQuery && typeof newQuery === 'object' && 'deviceId' in newQuery && 'macAddress' in newQuery && 'ipAddress' in newQuery) {
      console.log('[Client Pad] Valid query params detected, initializing app from watch...');
      const newDeviceInfo = {
        deviceId: newQuery.deviceId as string,
        macAddress: newQuery.macAddress as string,
        ipAddress: newQuery.ipAddress as string,
        location: newQuery.location as string, // Optional
        deviceType: newQuery.deviceType as string, // Optional
        appVersion: newQuery.appVersion as string // Optional
      };
      // @ts-ignore
      deviceStore.initDeviceInfo(newDeviceInfo);
      deviceInfo.value = newDeviceInfo; // Update local ref
      await initApp();
    } else {
      console.log('[Client Pad] Waiting for valid query params in watch...');
      // If no query params, try to handle from direct URL (e.g. on fresh load not yet routed)
      // or rely on existing deviceStore state if already populated.
      if (!deviceInfo.value?.deviceId) {
        // Only if not already set
        const handled = await handleUrlParams();
        if (!handled && !deviceStore.deviceId && router) {
          // If still no deviceId, redirect
          console.log('[Client Pad] No device info from query or URL, redirecting to auth.');
          await router.push('/auth');
        } else if (deviceStore.deviceId && !deviceInfo.value?.deviceId) {
          // If deviceStore has info but local ref doesn't, sync it and init
          deviceInfo.value = { ...deviceStore } as DeviceInfo;
          await initApp();
        }
      }
    }
  },
  { immediate: true, deep: true }
);
// === End of logic from main App.vue ===

onMounted(async () => {
  // Existing onMounted logic
  console.log(`[Client Pad] Current version: ${currentVersion.value}, Release Timestamp: ${formattedCurrentTimestamp.value}`);
  if (isUpdateDetected.value && latestVersion.value) {
    const formattedLatestTimestamp = latestReleaseTimestamp.value ? dayjs(latestReleaseTimestamp.value).format('YYYY-MM-DD HH:mm:ss') : 'N/A';
    console.log(`[Client Pad] New version detected: ${latestVersion.value}, Release Timestamp: ${formattedLatestTimestamp}`);
  }
  window.addEventListener('keydown', handleKeyDown);
  checkPathCaseSensitivity();
  console.log('[Client Pad] App.vue mounted, existing initializations complete.');

  // Added from main App.vue
  NProgress.start(); // Ensure progress bar starts early
  console.log('[Client Pad] App component mounted, initial route:', {
    fullPath: route.fullPath,
    query: route.query,
    params: route.params
  });

  await nextTick(); // Wait for DOM and route to be fully ready

  // Initialize based on deviceStore or URL params
  if (deviceStore.deviceId && deviceStore.ipAddress) {
    console.log('[Client Pad] Device info already in store, initializing app...');
    deviceInfo.value = {
      // Sync local ref from store
      deviceId: deviceStore.deviceId,
      macAddress: deviceStore.macAddress,
      ipAddress: deviceStore.ipAddress,
      location: deviceStore.location,
      deviceType: deviceStore.deviceType,
      appVersion: deviceStore.appVersion
    } as DeviceInfo;
    await initApp();
  } else if (route.query && Object.keys(route.query).length > 0 && 'deviceId' in route.query) {
    // Watcher will handle this due to immediate:true, but good to log
    console.log('[Client Pad] Query params available on mount, watcher will initialize app.');
  } else {
    const handledByUrl = await handleUrlParams();
    if (!handledByUrl && !deviceStore.deviceId && router) {
      console.log('[Client Pad] No device info on mount, redirecting to /auth.');
      await router.push('/auth');
    }
  }

  NatsListenerService.startListening();
  console.log('[Client Pad] NATS Listener Service started.');
  // @ts-ignore
  window.userStore = userStore; // Expose userStore

  NProgress.done(); // Ensure progress bar finishes
});

onUnmounted(() => {
  // Existing onUnmounted logic
  window.removeEventListener('keydown', handleKeyDown);
  console.log('[Client Pad] App.vue unmounted, existing cleanup complete.');

  // Added from main App.vue
  console.log('[Client Pad] Cleaning up NATS service...');
  nats.cleanup();
  // @ts-ignore
  deviceStore.clearDeviceInfo(); // Clear device info
  // @ts-ignore
  window.userStore = undefined; // Remove userStore from window
});

// Declare webViewBridge and userStore for TypeScript (from main App.vue)
declare global {
  interface Window {
    webViewBridge?: {
      reloadWebView: () => void;
      exitApp: () => void;
    };
    userStore?: any; // Consider defining a more specific type for userStore if possible
  }
}
</script>

<template>
  <!-- 全局对话框容器 -->
  <DialogContainer />

  <RouterView />
</template>
