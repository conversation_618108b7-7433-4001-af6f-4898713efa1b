<template>
  <app-dialog
    v-model="vm.state.visible"
    title="结账详情"
    :close-on-click-modal="false"
    @confirm="vm.actions.handlePrint"
    @close="vm.actions.handleClose"
    confirm-text="打印"
    cancel-text="关闭">
    <div class="pay-detail-content">
      <!-- 左侧应收金额部分 -->
      <div class="left-section">
        <!-- 支付方式金额 -->
        <div class="payment-section">
          <div class="section-subtitle">支付方式</div>
          <template v-for="(payment, index) in vm.computed.paymentsByType.value" :key="'payment-' + index">
            <div class="payment-item">
              <span class="label">{{ payment.displayName }}</span>
              <span class="value">￥ {{ payment.amount.toFixed(2) }}</span>
            </div>
          </template>
          <div class="payment-item total-payment">
            <span class="label">总计</span>
            <span class="value">￥ {{ vm.computed.totalActualFee.value.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="order-list">
      <div class="order-header items-center">
        <div class="checkbox-col">
          <el-checkbox disabled></el-checkbox>
        </div>
        <div class="order-id-col">订单信息</div>
        <div class="action-col">操作</div>
        <div class="amount-col">金额</div>
      </div>

      <!-- 订单行 -->
      <div v-for="(bill, index) in vm.state.payBillVOs" :key="bill.id" class="order-item">
        <div class="checkbox-col">
          <el-checkbox disabled></el-checkbox>
        </div>
        <div class="order-id-col">
          <div>{{ bill.billId }}</div>
          <div class="order-time">{{ formatTime(bill.ctime) }}</div>
          <div v-if="bill.isBack" class="refund-tag">退</div>
        </div>
        <div class="action-col">
          <el-button type="primary" text size="small">打印</el-button>
        </div>
        <div class="amount-col">
          <div class="amount-detail">
            <div class="actual-amount">实收: ￥ {{ (bill.totalFee / 100).toFixed(2) }}</div>
            <div class="discount-text">优惠: ￥ {{ ((bill.originalFee - bill.shouldFee) / 100).toFixed(2) }}</div>
          </div>
        </div>
      </div>
    </div>
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { usePayDetail } from './presenter';
import type { PayBillVO, PayRecordVO, IPayDetailViewModel } from './viewmodel';

const props = defineProps({
  payBillVOs: {
    type: Array as () => PayBillVO[],
    default: () => []
  },
  payRecordVOs: {
    type: Array as () => PayRecordVO[],
    default: () => []
  },
  visible: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['confirm', 'close', 'cancel']);

// 初始化视图模型
const vm: IPayDetailViewModel = usePayDetail(props.payBillVOs, props.payRecordVOs);

// 格式化时间戳为可读格式
const formatTime = (timestamp: number) => {
  if (!timestamp) return '';
  const date = new Date(timestamp * 1000);
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
};

// 数字前补零
const padZero = (num: number) => {
  return num < 10 ? `0${num}` : num;
};
</script>

<style scoped>
.pay-detail-content {
  display: flex;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.left-section,
.right-section {
  flex: 1;
  margin: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0 8px 0;
  color: #333;
  padding-top: 12px;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.discount-item,
.payment-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #666;
}

.payment-section {
  margin-top: 16px;
}

.total-payment {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
}

.total-payment .label,
.total-payment .value {
  font-weight: 600;
  color: #333;
}

.label {
  color: #666;
}

.value {
  font-weight: 500;
  color: #333;
}

.discount-value {
  color: #ff4d4f;
}

.order-list {
  margin-top: 20px;
}

.order-header {
  display: flex;
  background-color: #f5f5f5;
  padding: 10px 16px;
  font-weight: 500;
}

.order-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.checkbox-col {
  width: 40px;
}

.order-id-col {
  flex: 2;
  position: relative;
}

.operator-col {
  flex: 1;
}

.action-col {
  flex: 1;
}

.amount-col {
  flex: 2;
}

.order-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.refund-tag {
  position: absolute;
  top: 0;
  right: 10px;
  background-color: #ff4d4f;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.amount-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.discount-text {
  color: #ff4d4f;
}

.actual-amount {
  font-weight: 500;
}

.payment-method {
  font-size: 12px;
  color: #666;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.print-btn {
  width: 170px;
  height: 40px;
}
</style>
