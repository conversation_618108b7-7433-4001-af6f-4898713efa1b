/**
 * 套餐选择侧边栏组件的转换器
 */
import { IProductItem, IOptionalGroup } from './viewmodel';
import { getProductImageUrl } from '@/utils/imageUtils';

export class PackageDialogConverter {
  /**
   * 将套餐数据转换为固定商品列表
   * @param packageData 套餐数据
   * @returns 固定商品列表
   */
  static convertToFixedProducts(packageData: any): IProductItem[] {
    console.log('Converting fixed products from package data:', packageData);

    if (!packageData) {
      console.error('No package data provided');
      return [];
    }

    // 先检查packageData.packageDetail
    const detail = packageData.packageDetail || packageData;

    // 检查直接属性
    let fixedProductsList = null;

    // 按优先级检查各种可能的字段名
    if (detail.packageProductsPricePlanUnionProductVOs && detail.packageProductsPricePlanUnionProductVOs.length) {
      console.log('Using packageProductsPricePlanUnionProductVOs');
      fixedProductsList = detail.packageProductsPricePlanUnionProductVOs;
    } else if (detail.packageProducts) {
      // 如果是字符串，尝试解析
      if (typeof detail.packageProducts === 'string') {
        try {
          console.log('Parsing packageProducts string');
          fixedProductsList = JSON.parse(detail.packageProducts);
        } catch (error) {
          console.error('Failed to parse packageProducts string:', error);
        }
      } else if (Array.isArray(detail.packageProducts)) {
        console.log('Using packageProducts array');
        fixedProductsList = detail.packageProducts;
      }
    }

    // 如果没有找到固定商品列表，返回空数组
    if (!fixedProductsList || !Array.isArray(fixedProductsList)) {
      console.warn('No valid fixed products list found');
      return [];
    }

    console.log('Fixed products raw data:', fixedProductsList);

    // 构建产品映射
    const productVOMap = new Map();
    const productVOList = detail.productVOList || [];

    if (Array.isArray(productVOList) && productVOList.length > 0) {
      console.log('Building product map from productVOList:', productVOList.length);
      productVOList.forEach((product: any) => {
        if (product && product.id) {
          productVOMap.set(product.id, product);
        }
      });
    }

    // 生成固定商品列表
    const fixedProducts: IProductItem[] = [];

    fixedProductsList.forEach((item: any) => {
      const id = item.id;
      const count = item.count || 1;

      // 从产品映射中获取详细信息
      const productVO = productVOMap.get(id);

      if (productVO) {
        fixedProducts.push({
          id: id,
          name: productVO.name || '未知商品',
          imageUrl: getProductImageUrl(productVO.image || ''),
          price: productVO.currentPrice || 0, // 保持价格以分为单位
          quantity: count,
          unit: productVO.unit || '份'
        });
      } else {
        // 如果没有找到产品详情，创建一个基本项
        fixedProducts.push({
          id: id,
          name: '商品-' + id.substring(0, 8),
          imageUrl: '',
          price: 0,
          quantity: count,
          unit: '份'
        });
      }
    });

    console.log('Converted fixed products:', fixedProducts);
    return fixedProducts;
  }

  /**
   * 将套餐数据转换为可选商品组
   * @param packageData 套餐数据
   * @returns 可选商品组列表
   */
  static convertToOptionalGroups(packageData: any): IOptionalGroup[] {
    console.log('Converting optional groups from package data:', packageData);

    if (!packageData) {
      console.error('No package data provided');
      return [];
    }

    // 先检查packageData.packageDetail
    const detail = packageData.packageDetail || packageData;

    // 检查直接属性
    let optionalGroupsList = null;

    // 按优先级检查各种可能的字段名
    if (detail.optionalGroupsPricePlanUnionVOs && detail.optionalGroupsPricePlanUnionVOs.length) {
      console.log('Using optionalGroupsPricePlanUnionVOs');
      optionalGroupsList = detail.optionalGroupsPricePlanUnionVOs;
    } else if (detail.optionalGroups) {
      // 如果是字符串，尝试解析
      if (typeof detail.optionalGroups === 'string') {
        try {
          console.log('Parsing optionalGroups string');
          optionalGroupsList = JSON.parse(detail.optionalGroups);
        } catch (error) {
          console.error('Failed to parse optionalGroups string:', error);
        }
      } else if (Array.isArray(detail.optionalGroups)) {
        console.log('Using optionalGroups array');
        optionalGroupsList = detail.optionalGroups;
      }
    }

    // 如果没有找到可选组列表，返回空数组
    if (!optionalGroupsList || !Array.isArray(optionalGroupsList)) {
      console.warn('No valid optional groups list found');
      return [];
    }

    console.log('Optional groups raw data:', optionalGroupsList);

    // 构建产品映射
    const productVOMap = new Map();
    const productVOList = detail.productVOList || [];

    if (Array.isArray(productVOList) && productVOList.length > 0) {
      console.log('Building product map from productVOList:', productVOList.length);
      productVOList.forEach((product: any) => {
        if (product && product.id) {
          productVOMap.set(product.id, product);
        }
      });
    }

    // 生成可选组列表
    const optionalGroups: IOptionalGroup[] = [];

    optionalGroupsList.forEach((group: any) => {
      if (!group) return;

      const groupName = group.name || '可选组';
      const optionType = group.optionType || 'by_count';
      const optionCount = group.optionCount || 1;
      const billType = group.billType || 'paid';

      // 获取组内产品
      let groupProducts = group.products || [];

      // 如果是字符串，尝试解析
      if (typeof groupProducts === 'string') {
        try {
          groupProducts = JSON.parse(groupProducts);
        } catch (error) {
          console.error('Failed to parse group products string:', error);
          groupProducts = [];
        }
      }

      if (!Array.isArray(groupProducts)) {
        console.warn('Invalid group products format:', groupProducts);
        groupProducts = [];
      }

      // 处理组内商品
      const products: IProductItem[] = [];

      groupProducts.forEach((item: any) => {
        const id = typeof item === 'string' ? item : item.id;
        const count = typeof item === 'object' && item.count ? item.count : 1;

        if (!id) return;

        // 从产品映射中获取详细信息
        const productVO = productVOMap.get(id);

        if (productVO) {
          products.push({
            id: id,
            name: productVO.name || '未知商品',
            imageUrl: getProductImageUrl(productVO.image || ''),
            price: productVO.currentPrice || 0, // 保持价格以分为单位
            quantity: 0, // 初始数量为0
            unit: productVO.unit || '份'
          });
        } else {
          // 如果没有找到产品详情，创建一个基本项
          products.push({
            id: id,
            name: '可选商品-' + id.substring(0, 8),
            imageUrl: '',
            price: 0,
            quantity: 0, // 初始数量为0
            unit: '份'
          });
        }
      });

      if (products.length > 0) {
        optionalGroups.push({
          name: groupName,
          optionType: optionType,
          optionCount: optionCount,
          selectedCount: 0, // 初始已选择数量为0
          products: products,
          billType: billType
        });
      }
    });

    console.log('Converted optional groups:', optionalGroups);
    return optionalGroups;
  }

  /**
   * 生成已选商品的文本描述
   * @param fixedProducts 固定商品列表
   * @param optionalGroups 可选商品组列表
   * @returns 已选商品的文本描述
   */
  static generateSelectedItemsText(fixedProducts: IProductItem[], optionalGroups: IOptionalGroup[]): string {
    const selectedItems: string[] = [];

    // 添加固定商品
    fixedProducts.forEach(product => {
      selectedItems.push(`${product.name} x${product.quantity}`);
    });

    // 添加可选商品
    optionalGroups.forEach(group => {
      group.products.forEach(product => {
        if (product.quantity > 0) {
          selectedItems.push(`${product.name} x${product.quantity}`);
        }
      });
    });

    return selectedItems.join(' / ');
  }

  /**
   * 计算套餐总价
   * @param packageData 套餐数据
   * @param packageQuantity 套餐数量
   * @returns 套餐总价（分）
   */
  static calculateTotalPrice(packageData: any, packageQuantity: number): number {
    if (!packageData) {
      return 0;
    }

    // 获取价格，先从packageData直接获取，如果没有再从packageDetail获取
    let price = packageData.price || 0;
    if (!price && packageData.packageDetail && packageData.packageDetail.currentPrice) {
      price = packageData.packageDetail.currentPrice;
    }

    // 确保价格是数字
    if (typeof price === 'string') {
      price = parseFloat(price);
    }

    // 计算总价（保持以分为单位）
    return price * packageQuantity;
  }

  /**
   * 检查是否满足选择条件
   * @param optionalGroups 可选商品组列表
   * @returns 是否满足选择条件
   */
  static checkCanConfirm(optionalGroups: IOptionalGroup[]): boolean {
    // 如果没有可选组，可以直接确认
    if (!optionalGroups || optionalGroups.length === 0) {
      return true;
    }

    // 检查每个组是否满足选择条件
    for (const group of optionalGroups) {
      // 根据选择类型判断
      if (group.optionType === 'by_count' || group.optionType === 'by_plan') {
        // 按数量选择，检查已选择数量是否达到要求
        if (group.selectedCount < group.optionCount) {
          console.log(`Group ${group.name} requires ${group.optionCount} selections, but only has ${group.selectedCount}`);
          return false;
        }
      }
    }

    console.log('All groups satisfy selection requirements');
    return true;
  }
}
