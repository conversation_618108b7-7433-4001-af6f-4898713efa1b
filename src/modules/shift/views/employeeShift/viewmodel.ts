import { ComputedRef } from 'vue';
import type { AggregatedShiftReport, ShiftReportDaily } from '../../entity/EmployeeShiftEntity';

// 交班页面UI状态
export interface IEmployeeShiftState {
  // 日期选择器相关状态
  date: number; // 当前选择的日期

  // 员工数据
  employeeId: string; // 当前员工ID
  employeeName: string; // 当前员工名称

  // 场馆数据
  venueId: string; // 当前场馆ID

  // 加载状态
  loading: boolean; // 是否正在加载数据

  // 交班数据
  shiftReportData: ShiftReportDaily[] | null; // 原始交班报告数据
  aggregatedShiftReport: AggregatedShiftReport | null; // 聚合后的交班报告数据

  // 交班单ID
  shiftId: string; // 用于通过ID查询交班报告

  // 交班单号
  handNo: string; // 用于通过交班单号查询交班报告
}

// 交班页面UI计算属性
export interface IEmployeeShiftComputed {
  // 显示的开始和结束时间
  shiftTimeRange: ComputedRef<string>; // 交班时间范围显示

  // 财务数据显示
  totalIncome: ComputedRef<number>; // 总收入
  cashIncome: ComputedRef<number>; // 现金收入
  onlineIncome: ComputedRef<number>; // 线上收入(支付宝+微信+美团+口碑)
  bankCardIncome: ComputedRef<number>; // 银行卡收入
  otherIncome: ComputedRef<number>; // 其他收入

  // 折扣数据
  totalDiscount: ComputedRef<number>; // 总折扣

  // 业务数据
  tableCount: ComputedRef<number>; // 开台数
  orderCount: ComputedRef<number>; // 订单数
  paidBillCount: ComputedRef<number>; // 已结单数
  unpaidOrderCount: ComputedRef<number>; // 待结单数

  // 是否有数据
  hasData: ComputedRef<boolean>; // 是否有数据可显示

  // 支付方式柱状图相关数据
  maxNormalPayment: ComputedRef<number>; // 最大普通支付金额（用于计算柱状图宽度）
  totalNormalPayment: ComputedRef<number>; // 普通支付总额

  // 会员支付相关数据
  maxMemberPayment: ComputedRef<number>; // 最大会员支付金额（用于计算柱状图宽度）
  totalMemberPayment: ComputedRef<number>; // 会员支付总额

  // 支付类型数据（用于动态生成支付方式图表）
  normalPaymentItems: ComputedRef<Record<string, number>>; // 普通支付项目
  memberPaymentItems: ComputedRef<Array<{ name: string; value: number }>>; // 会员支付项目

  // 会员充值数据
  memberRechargeAmount: ComputedRef<number>; // 会员充值金额
}

// 退款账单信息接口
export interface IRefundBillInfo {
  id: string; // 退款账单ID
  pid: string; // 原始账单ID
  direction: string; // 方向（refund）
  isBack?: boolean; // 是否为账单还原
  amount: number; // 退款金额
}

// 账单信息接口 - 与billList计算逻辑完全一致
export interface IBillInfo {
  // 基本信息
  id: string; // 账单ID（billId）
  sessionId: string; // 会话ID
  room: string; // 房间名称
  roomId: string; // 房间ID

  // 时间信息
  timestamp: number; // 时间戳（finishTime || ctime）
  date: string; // 格式化日期 YYYY-MM-DD
  time: string; // 格式化时间 HH:mm:ss

  // 金额信息
  amount: number; // 显示金额（原金额 - 退款金额）
  refundAmount: number; // 退款金额

  // 状态信息
  status: number; // 账单状态
  direction: string; // 方向（normal/refund）
  isBack?: boolean; // 是否为账单还原
  isRefunded: boolean; // 是否已退款

  // 退款相关
  pid?: string; // 原始账单ID（仅退款账单有）
  refundBills: IRefundBillInfo[]; // 关联的退款账单列表
}

// 交班页面UI动作
export interface IEmployeeShiftActions {
  // 查询动作
  queryShiftReport: () => Promise<void>; // 查询交班报告

  // 日期选择动作
  changeDate: (date: number) => void; // 更改日期

  // 员工选择动作
  changeEmployee: (employeeId: string, employeeName: string) => void; // 更改员工

  // 场馆选择动作
  changeVenue: (venueId: string) => void; // 更改场馆

  // 打印交班报告
  printShiftReport: () => void; // 打印交班报告

  // 导出交班报告
  exportShiftReport: () => void; // 导出交班报告

  // 提交交班上报
  submitHandover: () => Promise<void>; // 提交交班上报

  // 查看账单详情
  checkBill: (bill: IBillInfo) => void; // 查看账单详情

  // 打印账单
  printBills: (billIds: string[]) => Promise<void>; // 打印账单
}

// 交班页面ViewModel
export interface IEmployeeShiftViewModel {
  state: IEmployeeShiftState;
  computed: IEmployeeShiftComputed;
  actions: IEmployeeShiftActions;
}
