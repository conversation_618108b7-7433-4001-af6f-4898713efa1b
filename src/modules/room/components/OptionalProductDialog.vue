<template>
  <!-- 可选组商品编辑对话框 -->
  <AppDialog
    v-model="dialogVisible"
    :title="optionalGroup?.isFree ? '编辑可选赠品' : '编辑可选商品'"
    class="!w-[700px]"
    :destroy-on-close="false"
    :level="DialogLevel.PRIMARY"
    uiType="card"
    @closed="handleDialogClosed">
    <div class="dialog-content">
      <!-- 固定头部区域 -->
      <div class="fixed-header" style="position: sticky; top: 0; z-index: 10; background-color: white">
        <!-- 选择模式信息栏 -->
        <div class="selection-mode mb-2 py-2">
          <div class="flex items-center">
            <el-icon class="text-blue-500 mr-1"><InfoFilled /></el-icon>
            <span class="text-[14px] text-blue-600">
              {{ isOptionalByCount(optionalGroup) ? '数量选择模式' : '方案选择模式' }}
            </span>
            <span class="ml-2 text-gray-500 text-[14px]">
              {{
                isOptionalByCount(optionalGroup)
                  ? `需选择 ${getOptionCount()} 件商品`
                  : `从 ${optionalGroupProducts.length} 个方案中选择 ${getOptionCount()} 个`
              }}
            </span>
          </div>
        </div>

        <!-- 已选择信息 - 蓝色背景区域 -->
        <div class="selection-info bg-blue-50 rounded p-3 mb-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <el-icon class="text-blue-500 mr-2"><InfoFilled /></el-icon>
              <span class="text-[14px]">
                已选择 <span class="font-medium">{{ currentSelectionCount }}</span> / {{ getOptionCount() }}
                {{ isOptionalByCount(optionalGroup) ? '件' : '种' }}
                <span v-if="isCurrentSelectionValid" class="text-green-600 ml-1 font-medium">(已满足选择要求)</span>
                <span v-else class="text-orange-500 ml-1 font-medium">(未满足选择要求)</span>
              </span>
            </div>
            <span v-if="!optionalGroup?.isFree && totalSelectedAmount > 0" class="text-[14px] font-medium">
              商品总额:
              <span class="text-red-600"
                >¥<span class="text-[16px]">{{ formatPrice(totalSelectedAmount).integer }}</span
                >.{{ formatPrice(totalSelectedAmount).decimal }}</span
              >
            </span>
          </div>
        </div>
      </div>

      <!-- 可滚动的商品列表容器 -->
      <div class="flex-1">
        <!-- by_plan类型使用卡片式布局 -->
        <div v-if="isOptionalByPlan(optionalGroup)" class="space-y-3">
          <div
            v-for="(product, index) in optionalGroupProducts"
            :key="product.id"
            @click="handleCardClick(product)"
            :class="[
              'w-full px-8 py-6 rounded-[10px] cursor-pointer flex flex-col justify-start items-start gap-2.5',
              product.tempQuantity > 0 ? 'bg-rose-600' : 'bg-white border-2 border-gray-200 hover:border-gray-300',
              shouldDisableProduct(product) && product.tempQuantity === 0 ? 'opacity-50 cursor-not-allowed' : ''
            ]">
            <!-- 主要内容区域 -->
            <div class="self-stretch flex justify-start items-center gap-6">
              <!-- 选中状态指示器 -->
              <div class="relative">
                <svg v-if="product.tempQuantity > 0" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="1" y="1.5" width="22" height="22" rx="3" fill="white" />
                  <rect x="1" y="1.5" width="22" height="22" rx="3" stroke="white" stroke-width="2" />
                  <path d="M5.5 12.5L10.5 17.5L18 8" stroke="#E9223A" stroke-width="3" />
                </svg>
                <div v-else class="w-6 h-6 border-2 border-gray-300 rounded bg-white"></div>
              </div>

              <!-- 商品信息区域 -->
              <div class="flex-1 flex flex-col justify-start items-start">
                <!-- 商品名称 -->
                <div :class="['self-stretch justify-start font-medium', product.tempQuantity > 0 ? 'text-white text-xl' : 'text-gray-900 text-lg']">
                  {{ product.name || product.productName }}
                </div>

                <!-- 价格信息 -->
                <div class="flex justify-start items-center gap-[3px]">
                  <div :class="['pt-[3px] flex flex-col justify-center items-center gap-2.5', product.tempQuantity > 0 ? 'text-white' : 'text-gray-600']">
                    <div class="text-sm font-normal">¥</div>
                  </div>
                  <div class="flex justify-start items-center">
                    <div :class="['justify-start font-medium', product.tempQuantity > 0 ? 'text-white text-base' : 'text-gray-900 text-base']">
                      {{ Math.floor((product.currentPrice || 0) / 100) }}
                    </div>
                    <div class="pt-[3px] flex justify-center items-center gap-2.5">
                      <div :class="['justify-start text-sm font-normal', product.tempQuantity > 0 ? 'text-white' : 'text-gray-600']">
                        .{{ String((product.currentPrice || 0) % 100).padStart(2, '0') }}
                      </div>
                    </div>
                  </div>
                  <!-- 如果是赠品，显示删除线 -->
                  <div
                    v-if="optionalGroup?.isFree || product.price === 0"
                    :class="['ml-2 line-through text-sm', product.tempQuantity > 0 ? 'text-white opacity-70' : 'text-gray-500']">
                    免费
                  </div>
                </div>

                <!-- 口味信息 -->
                <div v-if="product.flavors" :class="['text-sm mt-1', product.tempQuantity > 0 ? 'text-white opacity-80' : 'text-gray-500']">
                  {{ product.flavors }}
                </div>
              </div>

              <!-- 数量标识 -->
              <div class="flex flex-col justify-start items-start">
                <div class="self-stretch justify-start">
                  <span :class="['text-sm font-semibold', product.tempQuantity > 0 ? 'text-white' : 'text-gray-700']">X</span>
                  <span :class="['text-sm font-medium', product.tempQuantity > 0 ? 'text-white' : 'text-gray-700']"> </span>
                  <span :class="['text-3xl font-normal', product.tempQuantity > 0 ? 'text-white' : 'text-gray-900']">{{ product.count || 1 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- by_count类型继续使用表格布局 -->
        <el-table v-else :data="optionalGroupProducts" class="w-full mb-3" stripe>
          <!-- 选择列 - 放到第一列 -->
          <el-table-column label="数量" align="center" width="120">
            <template #default="scope">
              <div class="number-input-wrapper flex items-center justify-center py-2">
                <ErpInputNumber
                  v-model="scope.row.tempQuantity"
                  :min="0"
                  :max="getMaxAllowedQuantity(scope.row)"
                  type="primary"
                  size="small"
                  :disabled="shouldDisableProduct(scope.row)"
                  @change="handleQuantityChange(scope.row, $event)" />
              </div>
            </template>
          </el-table-column>
          <!-- 商品名称列 -->
          <el-table-column prop="productName" label="商品名称" align="left">
            <template #default="scope">
              <div class="font-medium text-[14px]">
                {{ scope.row.name || scope.row.productName }}
              </div>
              <div v-if="scope.row.flavors" class="text-xs text-gray-500 mt-1">{{ scope.row.flavors }}</div>
            </template>
          </el-table-column>
          <!-- 单价列 -->
          <el-table-column prop="price" label="单价" align="center">
            <template #default="scope">
              <div class="flex flex-col items-center justify-center">
                <!-- 显示原价（使用删除线样式表示赠品价格） -->
                <div :class="{ 'line-through text-gray-500': optionalGroup?.isFree || scope.row.price === 0 }">
                  <PriceDisplay :amountInFen="scope.row.currentPrice || 0" />
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 金额列 -->
          <el-table-column label="金额" align="right">
            <template #default="scope">
              <div class="flex items-end justify-end">
                <PriceDisplay
                  :amountInFen="
                    optionalGroup?.isFree || scope.row.price === 0
                      ? 0
                      : (scope.row.currentPrice !== undefined ? scope.row.currentPrice : scope.row.price) * (scope.row.tempQuantity || 0)
                  " />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center items-center">
        <AppButton
          type="black"
          @click="confirm"
          :disabled="!isCurrentSelectionValid"
          class="w-[120px] h-[48px]"
          :class="{ 'opacity-50 cursor-not-allowed': !isCurrentSelectionValid }">
          确认
        </AppButton>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, nextTick } from 'vue';
import { ElButton, ElIcon, ElMessage, ElTable, ElTableColumn, ElCheckbox } from 'element-plus';
import { InfoFilled, Plus, Minus } from '@element-plus/icons-vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import AppButton from '@/components/Button/AppButton.vue';
import { DialogLevel } from '@/types/dialog';
// 引入可选组工具函数
import { applyDefaultSelections, isOptionalByCount, isOptionalByPlan, OptionalGroup, PackageProductItem } from '@/utils/productPackageUtils';
import { getProductOriginalPrice } from '@/modules/room/views/OpenTable/billUtils';

// 控制是否启用自动确认默认选择的配置
const AUTO_CONFIRM_DEFAULT_SELECTIONS = true;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  optionalGroup: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['update:visible', 'confirm', 'close']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

// 格式化价格，分离整数和小数部分
const formatPrice = (priceInFen: number) => {
  const priceInYuan = (priceInFen / 100).toFixed(2);
  const [integer, decimal] = priceInYuan.split('.');
  return { integer, decimal };
};

// 获取可选组商品列表
const optionalGroupProducts = computed(() => {
  if (!props.optionalGroup || !props.optionalGroup.optionalProducts) {
    return [];
  }

  // 获取选择模式
  const isCountMode = isOptionalByCount(props.optionalGroup);

  // 继承现有的quantity值作为tempQuantity的初始值
  return props.optionalGroup.optionalProducts.map((product: any) => {
    // 创建处理后的产品对象
    const processedProduct = { ...product };

    // 使用已有的quantity值初始化tempQuantity
    if (processedProduct.tempQuantity === undefined) {
      processedProduct.tempQuantity = processedProduct.quantity || 0;
    }

    // 确保currentPrice有值，使用getProductOriginalPrice获取原价
    if (processedProduct.currentPrice === undefined || processedProduct.currentPrice === null || processedProduct.currentPrice === 0) {
      // 记录处理前的价格信息
      console.log(
        `[价格追踪] 处理前 "${processedProduct.name || processedProduct.productName}": price=${processedProduct.price}, currentPrice=${processedProduct.currentPrice}`
      );

      // 使用我们的原价获取函数
      processedProduct.currentPrice = getProductOriginalPrice(processedProduct);

      // 调试日志
      if (processedProduct.currentPrice > 0) {
        console.log(`[价格追踪] 已为商品 "${processedProduct.name || processedProduct.productName}" 设置原价(currentPrice): ${processedProduct.currentPrice}`);
      } else {
        console.log(`[价格追踪] 警告: 未能获取商品 "${processedProduct.name || processedProduct.productName}" 的原价，使用0`);
      }
    }

    // 判断是否是赠品
    const isFree = processedProduct.isFree || props.optionalGroup?.isFree;

    // 如果是赠品，确保price=0但保留currentPrice显示原价
    if (isFree) {
      // 记录修改前的价格
      const originalPrice = processedProduct.price;

      // 设置实际价格为0(因为是赠品)
      processedProduct.price = 0;

      console.log(
        `[价格追踪] 赠品 "${processedProduct.name || processedProduct.productName}": 将实际价格(price)从 ${originalPrice} 设为 0，保留原价(currentPrice)=${processedProduct.currentPrice}`
      );
    }

    // 调试日志，帮助排查问题
    console.log(`商品 ${processedProduct.name || processedProduct.productName}: count=${processedProduct.count}, isFree=${processedProduct.isFree}`);

    return processedProduct;
  });
});

// 修改当前已选择的数量或种类数计算逻辑，使用统一的大写形式
const currentSelectionCount = computed(() => {
  if (!optionalGroupProducts.value || optionalGroupProducts.value.length === 0) {
    return 0;
  }

  // 如果是按方案选择模式，返回已选择的商品种类数
  if (isOptionalByPlan(props.optionalGroup)) {
    return optionalGroupProducts.value.filter((product: any) => (product.tempQuantity || 0) > 0).length;
  }

  // 否则返回已选择商品的总数量（ByCount模式）
  return optionalGroupProducts.value.reduce((sum: number, product: any) => sum + (product.tempQuantity || 0), 0);
});

// 获取选项数量的辅助函数
const getOptionCount = () => {
  if (!props.optionalGroup) return 0;

  // 尝试从多个可能的字段获取选项数量
  const count = props.optionalGroup.count || props.optionalGroup.optionCount || (props.optionalGroup.optionalProducts?.length > 0 ? 1 : 0);

  // 确保返回值始终为正整数
  return Math.max(1, count);
};

// 修改是否有效选择的计算属性
const isCurrentSelectionValid = computed(() => {
  if (!props.optionalGroup) return false;

  // 使用getOptionCount函数获取目标数量
  const targetCount = getOptionCount();

  // 使用currentSelectionCount计算属性，它已经根据optionType处理了不同的计数方式
  if (isOptionalByPlan(props.optionalGroup)) {
    // 方案模式：需要选择指定种类数的商品
    return currentSelectionCount.value >= targetCount;
  } else {
    // 数量模式：需要选择指定数量的商品
    return currentSelectionCount.value >= targetCount;
  }
});

// 当前选中商品的总金额
const totalSelectedAmount = computed(() => {
  if (!props.optionalGroup || !props.optionalGroup.optionalProducts) {
    return 0;
  }

  return props.optionalGroup.optionalProducts.reduce((sum: number, product: any) => {
    const quantity = product.tempQuantity || 0;
    // 优先使用currentPrice，如果未定义则使用price
    const price = product.currentPrice !== undefined ? product.currentPrice : product.price;
    return sum + quantity * price;
  }, 0);
});

// 修改shouldDisableProduct函数，使用统一的大写形式
const shouldDisableProduct = (product: any) => {
  if (!props.optionalGroup || !props.optionalGroup.optionalProducts) {
    return false;
  }

  // 获取目标数量
  const targetCount = getOptionCount();

  // 如果当前商品已经被选择（数量>0），则不禁用
  if ((product.tempQuantity || 0) > 0) {
    return false;
  }

  // 根据不同的选择模式进行处理
  if (isOptionalByPlan(props.optionalGroup)) {
    // 按方案模式：计算已选择的不同商品种类数
    const selectedProductTypes = props.optionalGroup.optionalProducts.filter((p: any) => (p.tempQuantity || 0) > 0).length;

    // 如果已经达到可选种类上限，且当前商品未被选择，则禁用
    return selectedProductTypes >= targetCount;
  } else {
    // 按数量模式：计算已选择的商品总数量
    const totalSelectedQuantity = props.optionalGroup.optionalProducts.reduce((sum: number, p: any) => sum + (p.tempQuantity || 0), 0);

    // 如果已经达到可选数量上限，且当前商品未被选择，则禁用
    return totalSelectedQuantity >= targetCount;
  }
};

// 添加获取每个商品允许的最大数量的函数
const getMaxAllowedQuantity = (product: any) => {
  if (!props.optionalGroup) return 999;

  // 按方案模式下，每个商品只能选择0或1
  if (isOptionalByPlan(props.optionalGroup)) {
    return 1; // 方案模式下每个商品最多选择1个
  }

  // 按数量模式下，需要严格限制总数量
  // 获取目标数量
  const targetCount = getOptionCount();

  // 计算当前已选总数（不包括当前商品）
  const currentTotal = props.optionalGroup.optionalProducts.reduce((sum: number, p: any) => (p.id === product.id ? sum : sum + (p.tempQuantity || 0)), 0);

  // 计算当前商品已选数量
  const currentProductQuantity = product.tempQuantity || 0;

  // 计算剩余可选数量
  const remainingAllowed = Math.max(0, targetCount - currentTotal);

  // 当前已选总数加上当前商品数量
  const totalWithThisProduct = currentTotal + currentProductQuantity;

  // 如果总数已经达到或超过上限，则不允许再增加
  if (totalWithThisProduct >= targetCount) {
    return currentProductQuantity; // 只允许保持当前数量或减少
  }

  // 返回当前商品已选数量 + 剩余可选数量，确保不会超过限制
  return currentProductQuantity + remainingAllowed;
};

// 添加验证所有商品总数量的函数
const validateTotalQuantity = () => {
  if (!props.optionalGroup || !props.optionalGroup.optionalProducts) {
    return false;
  }

  // 获取目标数量
  const targetCount = getOptionCount();

  // 如果是按数量模式，则检查总数量
  if (isOptionalByCount(props.optionalGroup)) {
    const totalQuantity = props.optionalGroup.optionalProducts.reduce((sum: number, p: any) => sum + (p.tempQuantity || 0), 0);

    // 如果总数超过限制，需要调整数量
    if (totalQuantity > targetCount) {
      console.warn(`总数量 ${totalQuantity} 超过限制 ${targetCount}，需要调整`);

      // 使用一种策略来调整数量，比如保留先选择的商品
      const excessQuantity = totalQuantity - targetCount;
      let remainingExcess = excessQuantity;

      // 从后往前调整商品数量
      for (let i = props.optionalGroup.optionalProducts.length - 1; i >= 0 && remainingExcess > 0; i--) {
        const product = props.optionalGroup.optionalProducts[i];
        const currentQuantity = product.tempQuantity || 0;

        // 计算这个商品需要减少的数量
        const reduceBy = Math.min(currentQuantity, remainingExcess);

        if (reduceBy > 0) {
          product.tempQuantity = currentQuantity - reduceBy;
          product.quantity = product.tempQuantity; // 同步更新quantity
          remainingExcess -= reduceBy;
        }
      }

      return false;
    }
  }

  return true;
};

// 处理方案选择（by_plan类型）
const handlePlanSelection = (product: any, checked: string | number | boolean) => {
  // by_plan类型：勾选框模式，选中为1，未选中为0
  const selectedCount = checked ? 1 : 0;

  // 获取目标数量
  const targetCount = getOptionCount();

  // 如果是选中操作，需要检查是否超过限制
  if (selectedCount > 0) {
    // 计算当前已选择的不同商品种类数（不包括当前商品）
    const currentSelectedTypes = props.optionalGroup.optionalProducts.filter((p: any) => p.id !== product.id && (p.tempQuantity || 0) > 0).length;

    // 如果已经达到选择上限，则不允许再选择
    if (currentSelectedTypes >= targetCount) {
      ElMessage.warning(`最多只能选择 ${targetCount} 种商品`);
      return;
    }
  }

  // 设置商品的选择状态
  product.tempQuantity = selectedCount;
  product.quantity = selectedCount;

  // 手动触发数据更新，确保计算属性重新计算
  if (props.optionalGroup && props.optionalGroup.optionalProducts) {
    // 强制视图刷新 - 创建全新数组
    const updatedProducts = [...props.optionalGroup.optionalProducts];
    const index = updatedProducts.findIndex((p: any) => p.id === product.id);
    if (index >= 0) {
      // 替换整个对象触发响应式更新
      updatedProducts[index] = { ...product };
      // 更新整个数组
      props.optionalGroup.optionalProducts = [...updatedProducts];
    }

    // 手动打印日志调试
    console.log('方案选择更新：', product.name, selectedCount > 0 ? '已选择' : '已取消');
    console.log('当前选择种类数：', props.optionalGroup.optionalProducts.filter((p: any) => (p.tempQuantity || 0) > 0).length);
  }
};

// 处理卡片点击（by_plan类型专用）
const handleCardClick = (product: any) => {
  // 如果商品被禁用且未选中，则不响应点击
  if (shouldDisableProduct(product) && product.tempQuantity === 0) {
    return;
  }

  // 切换选择状态
  const newState = product.tempQuantity > 0 ? false : true;
  handlePlanSelection(product, newState);
};

// 添加商品数量变化的处理函数，支持增加和减少操作
const handleQuantityChange = (product: any, newValue: number) => {
  // 确保传入的newValue是数字，并且不能小于0
  const validValue = Math.max(0, newValue || 0);

  // 获取目标数量和当前总数
  const targetCount = getOptionCount();
  const currentTotalWithoutThisProduct = props.optionalGroup.optionalProducts.reduce(
    (sum: number, p: any) => (p.id === product.id ? sum : sum + (p.tempQuantity || 0)),
    0
  );

  // 计算允许的最大值
  const maxAllowed = getMaxAllowedQuantity(product);

  // 计算新的总数
  const newTotal = currentTotalWithoutThisProduct + validValue;

  // 如果是按数量模式且新总数会超过限制
  if (isOptionalByCount(props.optionalGroup) && newTotal > targetCount) {
    // 计算可接受的值
    const acceptableValue = Math.max(0, targetCount - currentTotalWithoutThisProduct);
    // 设置调整后的值
    product.tempQuantity = acceptableValue;
    product.quantity = acceptableValue;

    // 显示提示信息（可选）
    if (validValue > acceptableValue) {
      console.warn(`将数量从 ${validValue} 调整为 ${acceptableValue}，以符合总数 ${targetCount} 的限制`);
    }
  } else {
    // 正常情况下，设置新值但不超过最大允许值
    const finalValue = Math.min(validValue, maxAllowed);
    product.tempQuantity = finalValue;
    product.quantity = finalValue;
  }

  // 手动触发数据更新，确保计算属性重新计算
  if (props.optionalGroup && props.optionalGroup.optionalProducts) {
    // 强制视图刷新 - 创建全新数组
    const updatedProducts = [...props.optionalGroup.optionalProducts];
    const index = updatedProducts.findIndex((p: any) => p.id === product.id);
    if (index >= 0) {
      // 替换整个对象触发响应式更新
      updatedProducts[index] = { ...product };
      // 更新整个数组
      props.optionalGroup.optionalProducts = [...updatedProducts];
    }

    // 再次验证总数量
    validateTotalQuantity();

    // 手动打印日志调试
    console.log('产品选择数量更新：', product.name, product.tempQuantity, product.quantity);
    console.log(
      '当前选择总数：',
      props.optionalGroup.optionalProducts.reduce((sum: number, p: any) => sum + (p.tempQuantity || 0), 0)
    );
  }
};

// 确认选择
const confirm = () => {
  // 再次验证总数是否符合要求
  validateTotalQuantity();

  // 验证是否满足选择要求
  if (!isCurrentSelectionValid.value) {
    const targetCount = getOptionCount();

    // 显示更具体的提示信息
    if (isOptionalByPlan(props.optionalGroup)) {
      ElMessage.warning(`请选择至少 ${targetCount} 种商品，当前已选 ${currentSelectionCount.value} 种`);
    } else {
      ElMessage.warning(`请选择 ${targetCount} 件商品，当前已选 ${currentSelectionCount.value} 件`);
    }
    return;
  }

  // 创建深拷贝以避免直接修改props
  const updatedGroup = JSON.parse(JSON.stringify(props.optionalGroup));

  // 确保count字段正确设置
  updatedGroup.count = getOptionCount();

  // 获取最终选择的数量
  let selectionCount;

  // 根据选择模式计算最终选择数量
  if (isOptionalByPlan(updatedGroup)) {
    // 方案模式：计算已选商品的种类数
    selectionCount = updatedGroup.optionalProducts.filter((p: any) => (p.tempQuantity || 0) > 0).length;
  } else {
    // 数量模式：计算所有商品的总数量
    selectionCount = updatedGroup.optionalProducts.reduce((sum: number, product: any) => sum + (product.tempQuantity || 0), 0);
  }

  // 确保数量不超过限制
  selectionCount = Math.min(selectionCount, getOptionCount());

  // 设置选中数量
  updatedGroup.quantity = selectionCount;
  updatedGroup.selectedQuantity = selectionCount;

  // 重要：同步每个产品的tempQuantity到quantity
  if (updatedGroup.optionalProducts && updatedGroup.optionalProducts.length > 0) {
    updatedGroup.optionalProducts = updatedGroup.optionalProducts.map((product: any) => {
      // 记录每个商品的价格信息
      console.log(
        `[价格确认] "${product.name}": price(实际价格)=${product.price}, currentPrice(原价)=${product.currentPrice}, 数量=${product.tempQuantity || 0}`
      );

      return {
        ...product,
        quantity: product.tempQuantity || 0
      };
    });
  }

  // 打印日志确认数据同步
  console.log('确认选择：已选择数量', selectionCount);
  console.log('确认选择：更新后的组', updatedGroup);

  // 打印价格摘要日志
  console.log(`[价格摘要] 选择${updatedGroup.isFree ? '赠品' : '商品'}组：${updatedGroup.optionalProducts.length}个商品，${selectionCount}个已选`);
  console.log(
    `[价格摘要] 价格状态：`,
    updatedGroup.optionalProducts.filter((p: any) => p.quantity > 0).map((p: any) => `${p.name}(数量=${p.quantity}, 价格=${p.price}, 原价=${p.currentPrice})`)
  );

  // 发送更新后的完整数据到父组件
  emit('confirm', updatedGroup);
  dialogVisible.value = false;
};

// 关闭对话框时处理
const handleDialogClosed = () => {
  emit('close');
};

// 将optionalGroup转换为productPackageUtils接口格式的函数
const convertToOptionalGroupFormat = (group: any): OptionalGroup | null => {
  if (!group || !group.optionalProducts) return null;

  return {
    name: group.name || '',
    optionType: group.optionType || group.type || 'by_count',
    optionCount: getOptionCount(),
    products: group.optionalProducts.map((product: any) => ({
      id: product.id,
      name: product.name || product.productName,
      count: 1, // 默认每个商品的计数单位为1
      selected_count: product.tempQuantity || product.quantity || 0,
      currentPrice: product.currentPrice || 0,
      // 保留其他属性
      ...product
    }))
  };
};

// 将productPackageUtils格式转回组件使用的格式
const applyOptionalGroupSelections = (group: any, defaultSelections: OptionalGroup) => {
  if (!group || !group.optionalProducts || !defaultSelections || !defaultSelections.products) {
    return group;
  }

  // 创建ID到默认选择数量的映射
  const idToSelectedCount = new Map();
  defaultSelections.products.forEach(product => {
    idToSelectedCount.set(product.id, product.selected_count || 0);
  });

  // 应用默认选择到原始组
  group.optionalProducts.forEach((product: any) => {
    if (idToSelectedCount.has(product.id)) {
      const selectedCount = idToSelectedCount.get(product.id);
      // 仅当当前未选择时才应用默认选择
      if (!product.quantity && !product.tempQuantity) {
        product.tempQuantity = selectedCount;
        product.quantity = selectedCount;
      }
    }
  });

  return group;
};

// 应用默认选择的方法
const applyDefaultOptionalGroupSelections = () => {
  if (!props.optionalGroup || !props.optionalGroup.optionalProducts) return false;

  // 如果所有商品都已经有选择，则不应用默认选择
  const hasExistingSelections = props.optionalGroup.optionalProducts.some((p: any) => (p.quantity && p.quantity > 0) || (p.tempQuantity && p.tempQuantity > 0));

  if (hasExistingSelections) {
    console.log('OptionalProductDialog: Existing selections found, skipping default selections.');
    return false; // 返回false表示未应用默认选择
  }

  // 1. 转换为productPackageUtils需要的格式
  const groupForUtils = convertToOptionalGroupFormat(props.optionalGroup);
  console.log('OptionalProductDialog: Converted to OptionalGroup format for utils:', JSON.stringify(groupForUtils));
  if (!groupForUtils) return false;

  // 2. 应用默认选择 - 使用简单的默认选择策略（选择商品列表中的第一项）
  // 注意：这里使用了更新后的默认选择策略，直接选择列表中的第一项，而不是按价格、销量等排序
  // 同时，我们增加了禁用逻辑，当达到选择上限时，禁用所有未选择的商品的操作，包括ByCount和ByPlan两种模式
  console.log('OptionalProductDialog: Applying default selections via productPackageUtils (simple first item selection)...');
  const groupWithDefaultSelections = applyDefaultSelections(groupForUtils);
  console.log('OptionalProductDialog: Group data after applyDefaultSelections from utils:', JSON.stringify(groupWithDefaultSelections));

  // 3. 将结果应用回组件格式
  applyOptionalGroupSelections(props.optionalGroup, groupWithDefaultSelections);
  console.log('OptionalProductDialog: props.optionalGroup after applying selections back:', JSON.stringify(props.optionalGroup));

  // 4. 确保UI更新
  if (props.optionalGroup.optionalProducts) {
    props.optionalGroup.optionalProducts = [...props.optionalGroup.optionalProducts];
  }

  return true; // 返回true表示已应用默认选择
};

// 监听对话框的可见性变化，当打开时初始化所有商品的tempQuantity为其quantity值，并应用默认选择
watch(
  () => dialogVisible.value,
  newValue => {
    if (newValue && props.optionalGroup && props.optionalGroup.optionalProducts) {
      console.log('OptionalProductDialog: dialog opened, processing default selections...', JSON.stringify(props.optionalGroup));
      // 弹窗打开时，初始化tempQuantity为现有的quantity值
      nextTick(() => {
        props.optionalGroup.optionalProducts.forEach((product: any) => {
          product.tempQuantity = product.quantity || 0;
        });
        console.log('OptionalProductDialog: tempQuantity initialized.', JSON.stringify(props.optionalGroup.optionalProducts));

        // 应用默认选择逻辑
        const defaultSelectionsApplied = applyDefaultOptionalGroupSelections();

        // 如果应用了默认选择，则更新状态并自动确认以同步到账单
        if (defaultSelectionsApplied) {
          // 确保验证状态正确更新
          validateTotalQuantity();

          // 使用nextTick确保计算属性已更新
          nextTick(() => {
            // 检查选择是否有效，且启用了自动确认功能
            if (isCurrentSelectionValid.value && AUTO_CONFIRM_DEFAULT_SELECTIONS) {
              console.log('OptionalProductDialog: Default selections valid and auto-confirm enabled. Confirming...');

              // 自动确认，将默认选择同步到账单
              // 注意：这里直接调用确认逻辑，但不通过confirm方法，以便控制弹窗关闭时机

              // 创建深拷贝以避免直接修改props
              const updatedGroup = JSON.parse(JSON.stringify(props.optionalGroup));

              // 确保count字段正确设置
              updatedGroup.count = getOptionCount();

              // 获取最终选择的数量
              let selectionCount;

              // 根据选择模式计算最终选择数量
              if (isOptionalByPlan(updatedGroup)) {
                // 方案模式：计算已选商品的种类数
                selectionCount = updatedGroup.optionalProducts.filter((p: any) => (p.tempQuantity || 0) > 0).length;
              } else {
                // 数量模式：计算所有商品的总数量
                selectionCount = updatedGroup.optionalProducts.reduce((sum: number, product: any) => sum + (product.tempQuantity || 0), 0);
              }

              // 确保数量不超过限制
              selectionCount = Math.min(selectionCount, getOptionCount());

              // 设置选中数量
              updatedGroup.quantity = selectionCount;
              updatedGroup.selectedQuantity = selectionCount;

              // 同步每个产品的tempQuantity到quantity
              if (updatedGroup.optionalProducts && updatedGroup.optionalProducts.length > 0) {
                updatedGroup.optionalProducts = updatedGroup.optionalProducts.map((product: any) => {
                  return {
                    ...product,
                    quantity: product.tempQuantity || 0
                  };
                });
              }

              // 发送更新后的完整数据到父组件
              emit('confirm', updatedGroup);

              // 显示提示信息（可选）
              if (updatedGroup.isFree) {
                ElMessage.success('已自动选择赠品');
              } else {
                ElMessage.success('已自动选择可选商品');
              }

              // 关闭对话框
              dialogVisible.value = false;
            } else {
              if (!isCurrentSelectionValid.value) {
                console.warn('OptionalProductDialog: Default selections applied, but current selection is invalid. Manual adjustment needed.');
              } else if (!AUTO_CONFIRM_DEFAULT_SELECTIONS) {
                console.log('OptionalProductDialog: Default selections applied and valid, but auto-confirm is disabled.');
              }
            }
          });
        }
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.fixed-header {
  background-color: white;
  z-index: 10;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
}

.fixed-footer {
  background-color: white;
  z-index: 10;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}
</style>
