<template>
  <div class="flex flex-col h-full relative">
    <el-table
      v-loading="loading && firstLoading"
      :data="tableData"
      stripe
      height="100%"
      class="custom-table"
      v-infinite-scroll="loadMore"
      :infinite-scroll-disabled="infiniteScrollDisabled"
      infinite-scroll-distance="50"
      @scroll="handleTableScroll">
      <!-- 订单号列 -->
      <el-table-column prop="sessionId" label="订单号" align="center">
        <template #default="{ row }">
          <div class="flex items-center text-[16px]">
            <div size="small" class="mr-2">{{ row?.sessionId }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 包厢名称列 -->
      <el-table-column label="包厢名称" align="center">
        <template #default="{ row }">
          <div class="text-[16px]">{{ row.roomVO?.name || '-' }}</div>
        </template>
      </el-table-column>

      <!-- 使用时间列 -->
      <el-table-column label="时间" align="center">
        <template #default="{ row }">
          <div class="space-y-1 text-[16px]">
            <div>{{ formatUnixTimestamp(row.startTime) }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 应收金额列 -->
      <el-table-column label="应收金额" align="right">
        <template #default="{ row }">
          <div class="text-[16px] font-medium">{{ formatYuanWithSymbol(row.totalFee) }}</div>
        </template>
      </el-table-column>

      <!-- 实收金额列 -->
      <el-table-column label="实收金额" align="right">
        <template #default="{ row }">
          <div class="text-[16px] font-medium">{{ formatYuanWithSymbol(row.paidAmount) }}</div>
        </template>
      </el-table-column>

      <!-- 开台状态列 -->
      <el-table-column label="开台状态" align="center">
        <template #default="{ row }">
          <span :class="getSessionStatusType(row.closeTime)" class="text-[16px]">
            {{ getSessionStatusText(row.closeTime) }}
          </span>
        </template>
      </el-table-column>

      <!-- 支付状态列 -->
      <el-table-column label="支付状态" align="center">
        <template #default="{ row }">
          <span :class="getPayStatusType(row.payStatus)" class="text-[16px]">
            {{ getPayStatusText(row.payStatus) }}
          </span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" min-width="80" align="center">
        <template #default="{ row }">
          <div class="flex gap-2">
            <el-button size="large" class="text-[16px]" @click="handleViewDetails(row)"> 查看 </el-button>
            <el-button v-if="!row.closeTime" size="large" class="text-[16px]" @click="handleRefund(row)"> 退单 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 加载更多提示 -->
    <div v-if="localLoading && !firstLoading" class="text-center py-4 absolute bottom-0 left-0 right-0 bg-white bg-opacity-80 z-10">
      <el-icon class="loading">
        <Loading />
      </el-icon>
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 无更多数据提示 -->
    <div hidden v-if="noMore && tableData.length > 0" class="text-center text-gray-500 py-4">没有更多数据了</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import { OrderApi } from '@/modules/order/api/order';
import type { OrderVO, SessionVO } from '@/types/projectobj';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import { Loading } from '@element-plus/icons-vue';
import { ElInfiniteScroll } from 'element-plus';

const props = defineProps<{
  searchParams: Record<string, any>;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:loading': [value: boolean];
  'view-details': [order: SessionVO];
  refund: [order: SessionVO];
}>();

// 表格数据
const tableData = ref<SessionVO[]>([]);
const currentPage = ref(1);
const pageSize = ref(100); // 设置为100
const total = ref(0);
// 存储完整数据集
const fullData = ref<SessionVO[]>([]);
// 下拉加载相关状态
const noMore = ref(false);
const firstLoading = ref(true);
const localLoading = ref(false);

// 计算是否禁用无限滚动
const infiniteScrollDisabled = computed(() => {
  return localLoading.value || noMore.value;
});

// 订单类型映射
const getOrderTypeText = (type: string) => {
  const map: Record<string, string> = {
    ROOM: '包厢订单',
    PRODUCT: '商品订单',
    PACKAGE: '套餐订单'
  };
  return map[type] || '';
};

// 支付状态映射
const getPayStatusType = (status: string) => {
  const map: Record<string, string> = {
    UNPAID: 'status-unpaid',
    PAID: 'status-paid',
    CANCELLED: 'status-cancelled',
    REFUNDED: 'status-refunded',
    SUSPENDED: 'status-suspended'
  };
  // 确保返回有效的类型值，如果不存在则返回默认值'info'而不是空字符串
  return map[status] || 'info';
};

// 开台状态映射
const getSessionStatusType = (closeTime?: number) => {
  return closeTime ? 'status-closed' : 'status-active';
};

const getSessionStatusText = (closeTime?: number) => {
  return closeTime ? '已关台' : '开台中';
};

const getPayStatusText = (status: string) => {
  const map: Record<string, string> = {
    unpaid: '未结',
    paid: '已结',
    cancelled: '已取消',
    refunded: '已退款',
    suspended: '挂单'
  };
  return map[status] || status;
};

// 加载数据
const loadData = async (isLoadMore = false) => {
  if (!isLoadMore) {
    // 初始加载时重置状态
    tableData.value = [];
    currentPage.value = 1;
    noMore.value = false;
    firstLoading.value = true;
  }

  if (localLoading.value) return;
  localLoading.value = true;
  emit('update:loading', true);

  try {
    const response = await OrderApi.listOrders({
      ...props.searchParams,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    });

    if (response.code === 0 && response.data) {
      // 兼容两种可能的API返回结构
      let newData: any[] = [];
      let totalItems = 0;

      // 由于TypeScript类型问题，我们使用类型断言和安全访问
      // 处理对象结构 { data: [], total: number }
      if (response.data && !Array.isArray(response.data) && typeof response.data === 'object') {
        const dataObj = response.data as any;
        if (Array.isArray(dataObj.data)) {
          newData = dataObj.data;
          totalItems = typeof dataObj.total === 'number' ? dataObj.total : dataObj.data.length;
        }
      }
      // 处理直接数组结构
      else if (Array.isArray(response.data)) {
        newData = response.data;
        totalItems = response.data.length;
      }

      // 如果有搜索关键词，执行精确匹配
      if (props.searchParams?.orderNo) {
        const keyword = props.searchParams.orderNo.trim();
        if (keyword) {
          // 先尝试精确匹配包厢名
          const exactRoomMatches = newData.filter(item => item.roomVO?.name === keyword);

          // 如果有精确匹配的包厢名，只显示这些结果
          if (exactRoomMatches.length > 0) {
            newData = exactRoomMatches;
            totalItems = exactRoomMatches.length;
          }
          // 否则再检查订单号和其他字段
          else {
            // 原有的模糊匹配逻辑保留
            const filteredData = newData.filter(
              item => (item.sessionId && item.sessionId.includes(keyword)) || (item.roomVO?.name && item.roomVO.name.includes(keyword))
            );

            newData = filteredData;
            totalItems = filteredData.length;
          }
        }
      }

      // 更新表格数据
      if (isLoadMore) {
        tableData.value = [...tableData.value, ...newData];
      } else {
        tableData.value = newData;
      }

      total.value = totalItems;
      fullData.value = [...tableData.value];

      // 判断是否还有更多数据可加载
      if (newData.length < pageSize.value || tableData.value.length >= totalItems) {
        noMore.value = true;
      } else {
        noMore.value = false;
      }
    }
  } catch (error) {
    console.error('加载订单列表失败:', error);
  } finally {
    localLoading.value = false;
    emit('update:loading', false);
    firstLoading.value = false;
  }
};

// 加载更多数据
const loadMore = () => {
  if (noMore.value || localLoading.value || firstLoading.value) return; // 添加firstLoading检查

  // 增加页码
  currentPage.value++;

  // 如果有足够的本地数据进行分页，不需要请求API
  if (fullData.value.length > tableData.value.length) {
    const start = tableData.value.length;
    const end = Math.min(start + pageSize.value, fullData.value.length);
    const nextPageData = fullData.value.slice(start, end);

    // 追加数据到当前显示列表
    tableData.value = [...tableData.value, ...nextPageData];

    // 检查是否还有更多数据
    noMore.value = tableData.value.length >= fullData.value.length;

    console.log(`本地加载更多: 当前记录数 ${tableData.value.length}, 总记录数 ${fullData.value.length}, 还有更多: ${!noMore.value}`);
  } else {
    // 如果本地数据不足，则请求API获取更多数据
    loadData(true);
  }
};

// 表格滚动处理
const handleTableScroll = (e: any) => {
  // 首次加载或已经触发加载时，不重复触发
  if (firstLoading.value || localLoading.value) return;

  // Element Plus的滚动事件可能不直接提供target
  // 检查事件对象结构并安全地获取滚动信息
  if (!e || !e.currentTarget) return;

  const el = e.currentTarget;
  const scrollTop = el.scrollTop;
  const scrollHeight = el.scrollHeight;
  const clientHeight = el.clientHeight;

  // 检查是否有足够的数据进行滚动
  if (scrollHeight > 0 && clientHeight > 0 && scrollHeight - scrollTop - clientHeight < 50 && !localLoading.value && !noMore.value) {
    loadMore();
  }
};

// 查看详情
const handleViewDetails = (order: SessionVO) => {
  emit('view-details', order);
};

// 退单处理
const handleRefund = (order: SessionVO) => {
  emit('refund', order);
};

// 监听搜索参数变化，但只在日期或其他非搜索参数变化时重新加载数据
const lastSearchParams = ref({ ...props.searchParams });
let isFirstWatch = true; // 添加标记避免首次监听触发加载

watch(
  () => props.searchParams,
  (newParams, oldParams = lastSearchParams.value) => {
    // 首次监听不触发请求
    if (isFirstWatch) {
      isFirstWatch = false;
      lastSearchParams.value = { ...newParams };
      return;
    }

    // 检查哪些参数变化了
    const hasDateChanged = newParams.startTime !== oldParams.startTime || newParams.endTime !== oldParams.endTime;

    const hasPageChanged = newParams.pageNum !== oldParams.pageNum || newParams.pageSize !== oldParams.pageSize;

    // 排除orderNo和payStatus的其他参数变化
    const hasOtherParamsChanged = Object.keys(newParams).some(key => {
      return (
        key !== 'orderNo' &&
        key !== 'payStatus' &&
        key !== 'startTime' &&
        key !== 'endTime' &&
        key !== 'pageNum' &&
        key !== 'pageSize' &&
        newParams[key] !== oldParams[key]
      );
    });

    // 检查搜索关键词变化
    const hasKeywordChanged = newParams.orderNo !== oldParams.orderNo;

    // 只有日期或其他重要参数变化时才重新加载数据
    if (hasDateChanged || hasPageChanged || hasOtherParamsChanged) {
      console.log('日期或其他重要参数变化，重新加载数据');
      loadData();
    } else if (hasKeywordChanged) {
      // 搜索关键词变化，包括清空搜索框的情况
      console.log('搜索关键词变化，使用已有数据进行过滤');

      // 如果新的搜索关键词为空，则重新加载数据以显示全部数据
      if (!newParams.orderNo && oldParams.orderNo) {
        console.log('搜索框已清空，重新加载全部数据');
        loadData();
      } else {
        // 否则使用现有的完整数据进行过滤
        filterLocalData();
      }
    } else {
      console.log('仅payStatus变化，不重新加载数据');
    }

    // 更新上次参数
    lastSearchParams.value = { ...newParams };
  },
  { deep: true, immediate: false }
);

// 本地过滤数据的函数
const filterLocalData = () => {
  // 如果没有完整数据，先加载
  if (fullData.value.length === 0) {
    loadData();
    return;
  }

  const keyword = props.searchParams?.orderNo?.trim() || '';
  // 使用API返回的原始数据作为基础
  let filteredData = [...fullData.value];

  if (keyword) {
    // 先尝试精确匹配包厢名
    const exactRoomMatches = filteredData.filter(item => item.roomVO?.name === keyword);

    // 如果有精确匹配的包厢名，只显示这些结果
    if (exactRoomMatches.length > 0) {
      filteredData = exactRoomMatches;
      console.log(`找到精确匹配包厢名 ${keyword} 的记录: ${exactRoomMatches.length}条`);
    }
    // 否则再检查订单号和其他字段
    else {
      filteredData = filteredData.filter(
        item => (item.sessionId && item.sessionId.includes(keyword)) || (item.roomVO?.name && item.roomVO.name.includes(keyword))
      );
      console.log(`找到模糊匹配 ${keyword} 的记录: ${filteredData.length}条`);
    }
  }

  // 更新本地显示的数据
  updateLocalData(filteredData);
};

// 初始加载
// 删除这里的自动加载，由父组件控制
// onMounted(() => {
//   loadData()
// })

// 更新本地数据
const updateLocalData = (data: any[]) => {
  // 为了支持下拉加载，我们需要在这里模拟分页
  const totalData = data;
  const currentPageData = data.slice(0, Math.min(pageSize.value, data.length));

  tableData.value = currentPageData;
  total.value = totalData.length;
  fullData.value = totalData;

  // 重置分页状态
  currentPage.value = 1;

  // 判断是否还有更多数据
  noMore.value = currentPageData.length >= totalData.length;

  console.log(`本地筛选后: 总记录数 ${totalData.length}, 当前显示 ${currentPageData.length}, 还有更多: ${!noMore.value}`);
};

// 直接更新API返回数据
const updateData = (response: any) => {
  // 安全检查
  if (!response || !response.data) return;

  try {
    let newData: any[] = [];
    let totalItems = 0;

    // 如果是分页数据结构 { data: [], total: number }
    if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
      const dataObj = response.data as any;
      if (Array.isArray(dataObj.data)) {
        newData = dataObj.data;
        totalItems = typeof dataObj.total === 'number' ? dataObj.total : dataObj.data.length;
      }
    }
    // 如果是数组
    else if (Array.isArray(response.data)) {
      newData = response.data;
      totalItems = response.data.length;
    }

    // 更新表格数据
    tableData.value = newData;
    total.value = totalItems;
    fullData.value = newData;

    // 重置滚动加载状态
    currentPage.value = 1;
    noMore.value = totalItems <= pageSize.value;

    console.log(`API数据更新完成: 总记录数 ${totalItems}, 当前显示 ${newData.length}, 还有更多: ${!noMore.value}`);
  } catch (e) {
    console.error('数据处理异常:', e);
  }
};

// 暴露方法
defineExpose({
  refresh: () => loadData(),
  updateLocalData,
  updateData,
  loadMore
});
</script>

<style scoped>
:deep(.el-table .el-table__header-wrapper) {
  background-color: #f5f5f5;
}

:deep(.el-table .el-table__header th) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  height: 54px;
}

:deep(.el-table .cell) {
  padding: 12px 8px;
}

:deep(.el-button.is-plain) {
  padding: 8px 16px;
}

:deep(.status-paid) {
  color: #000000;
  font-weight: 500;
}

:deep(.status-unpaid) {
  color: #ff0000;
  font-weight: 500;
}

/* 确保表格占满容器 */
:deep(.el-table) {
  height: 100% !important;
}

/* 开台状态样式 */
:deep(.status-active) {
  color: #67c23a;
  font-weight: 500;
}

:deep(.status-closed) {
  color: #909399;
  font-weight: 500;
}

/* 加载动画 */
.loading {
  animation: rotate 1s linear infinite;
  display: inline-block;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
