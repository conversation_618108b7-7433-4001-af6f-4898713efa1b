import type { BaseResponse } from '@/types/baseResponse';
import type { MemberVO, MemberCardLevelVO, UpdateMemberReqDto } from '@/api/autoGenerated/shared/types';
import { EMPTY_STRING } from './entity';
import { memberApi } from '../../api/member';
import { ElMessage } from 'element-plus';

// 定义会员详情VO类型
export interface MemberDetailVO {
  member: MemberVO;
  cardLevel: MemberCardLevelVO;
}

// 查询会员详情请求参数
export type QueryMemberDetailRequest = {
  id: string;
  venueId?: string;
};

// 更新会员请求参数
export type UpdateMemberRequest = UpdateMemberReqDto;

// 性别转换函数
export function convertGender(gender: string | undefined): string {
  if (!gender) return EMPTY_STRING;
  return gender.toLowerCase() === 'female' ? '女' : gender.toLowerCase() === 'male' ? '男' : gender;
}

// 消费记录请求参数
interface QueryConsumptionRecordsRequest {
  cardNumber: string;
  startTime?: number;
  endTime?: number;
  consumeType?: string;
  pageNum?: number;
  pageSize?: number;
}

// 优惠券请求参数
interface QueryCouponsRequest {
  cardNumber: string;
  status: string;
}

// 积分兑换请求参数
interface QueryPointsExchangesRequest {
  cardNumber: string;
  startTime?: number;
  endTime?: number;
  pageNum?: number;
  pageSize?: number;
}

// 积分历史请求参数
interface QueryPointsHistoryRequest {
  cardNumber: string;
  startTime?: number;
  endTime?: number;
  pointsType?: string;
  pageNum?: number;
  pageSize?: number;
}

export interface ConsumptionRecord {
  billNo: string;
  store: string;
  room: string;
  amount: number;
  payAmount: number;
  balance: number;
  time: number;
  formattedAmount: string;
  formattedPayAmount: string;
  formattedBalance: string;
  formattedTime: string;
}

export interface ConsumptionResponse {
  records: ConsumptionRecord[];
  total: number;
}

// 格式化日期时间
export function formatDateTime(timestamp: number | undefined): string {
  if (!timestamp || timestamp <= 0) return '-';

  // 处理毫秒和秒两种时间戳格式
  const timeMs = timestamp > 9999999999 ? timestamp : timestamp * 1000;
  const date = new Date(timeMs);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(
    2,
    '0'
  )}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 格式化日期
export function formatDate(timestamp: number | undefined): string {
  if (!timestamp || timestamp <= 0) return '-';

  // 处理毫秒和秒两种时间戳格式
  const timeMs = timestamp > 9999999999 ? timestamp : timestamp * 1000;
  const date = new Date(timeMs);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// 格式化金额为元
export function formatYuan(amount: number): string {
  if (!amount && amount !== 0) return '0.00';
  return (amount / 100).toFixed(2);
}

export function getTotalConsumptionAmount(payItems: any): number {
  const totalFee = payItems.totalFee || 0;
  const roomBonusAmount = payItems.roomBonusAmount || 0;
  const goodsBonusAmount = payItems.goodsBonusAmount || 0;
  const commonBonusAmount = payItems.commonBonusAmount || 0;
  return totalFee + roomBonusAmount + goodsBonusAmount + commonBonusAmount;
}

export function formatConsumeType(consumeType: string): string {
  if (!consumeType) return '-';
  const type = consumeType.toLowerCase();
  if (type === 'recharge') return '充值';
  if (type === 'consume') return '门店消费';
  if (type === 'refund') return '消费退单';
  return consumeType;
}

export class MemberDetailInteractor {
  private cardNumber: string = '';
  private memberId: string;

  constructor(memberId: string) {
    this.memberId = memberId;
  }

  private async ensureMemberId(): Promise<string> {
    return this.memberId;
  }

  async getMemberDetail(): Promise<BaseResponse<MemberDetailVO>> {
    try {
      const id = await this.ensureMemberId();
      const response = await memberApi.getMemberCardDetail({
        id
      });
      const memberDetail = response.data[0] as unknown as MemberDetailVO;
      console.log('[MemberDetailInteractor] memberDetail', memberDetail);
      return {
        code: 0,
        message: '',
        traceId: '',
        data: memberDetail
      };
    } catch (error) {
      console.error('Failed to get member detail:', error);
      throw error;
    }
  }

  // 获取消费记录
  async getConsumptionRecords(): Promise<ConsumptionResponse> {
    try {
      const params = {
        memberCardId: this.memberId
      };

      const response = await memberApi.getMemberRechargeBill(params);

      if (response.data) {
        // 映射API返回的数据到表格所需的格式
        const records = response.data.map((item: any) => ({
          billNo: item.billId || '-',
          store: item.venueName || '-',
          room: item.info || '-',
          amount: item.totalFee || 0,
          payAmount: item.totalFee || 0,
          time: item.ctime || item.billDate || 0,
          consumeLabel: formatConsumeType(item.bizType || ''),
          payRecordTotalAmout: item.payRecordTotalAmout || 0,
          memberCardBalance: item.memberCardBalance || 0,
          formattedTime: formatDateTime(item.ctime || item.billDate || 0)
        }));

        return {
          records,
          total: response.data.total || response.data.length || 0
        };
      } else {
        return {
          records: [],
          total: 0
        };
      }
    } catch (error) {
      console.error('获取消费历史出错:', error);
      throw error;
    }
  }

  // 冻结会员卡
  async freezeMemberCard(): Promise<BaseResponse<any>> {
    try {
      const id = await this.ensureMemberId();
      const response = await memberApi.frozenMemberCard({
        id
      });
      return {
        code: 0,
        message: '',
        traceId: '',
        data: response.data
      };
    } catch (error) {
      console.error('Failed to freeze member card:', error);
      throw error;
    }
  }

  // 解冻会员卡
  async unfreezeMemberCard(): Promise<BaseResponse<any>> {
    try {
      const id = await this.ensureMemberId();
      const response = await memberApi.unfrozenMemberCard({
        id
      });
      return {
        code: 0,
        message: '',
        traceId: '',
        data: response.data
      };
    } catch (error) {
      console.error('Failed to unfreeze member card:', error);
      throw error;
    }
  }

  // 以下接口暂未实现，先注释掉
  /*
  async getCoupons(status: string): Promise<BaseResponse<any>> {
    throw new Error('Coupons are not supported through getMemberDetail API')
  }

  async getPointsExchanges(params: Partial<QueryPointsExchangesRequest>): Promise<BaseResponse<any>> {
    throw new Error('Points exchanges are not supported through getMemberDetail API')
  }

  async getPointsHistory(params: Partial<QueryPointsHistoryRequest>): Promise<BaseResponse<any>> {
    throw new Error('Points history is not supported through getMemberDetail API')
  }

  async setPassword(newPassword: string): Promise<void> {
    throw new Error('Password update is not supported through updateMember API')
  }

  async renewCard(): Promise<BaseResponse<any>> {
    try {
      const id = await this.ensureMemberId()
      const response = await postApiMemberUpdate({
        id,
        cardNumber: this.cardNumber,
        level: 'active'
      })
      return {
        code: 0,
        message: '',
        traceId: '',
        data: response.data
      }
    } catch (error) {
      console.error('Failed to renew card:', error)
      throw error
    }
  }

  async adjustPoints(points: number): Promise<BaseResponse<any>> {
    try {
      const id = await this.ensureMemberId()
      const response = await postApiMemberUpdate({
        id,
        cardNumber: this.cardNumber,
        points
      })
      return {
        code: 0,
        message: '',
        traceId: '',
        data: response.data
      }
    } catch (error) {
      console.error('Failed to adjust points:', error)
      throw error
    }
  }

  async adjustCardLevel(level: string): Promise<BaseResponse<any>> {
    try {
      const id = await this.ensureMemberId()
      const response = await postApiMemberUpdate({
        id,
        cardNumber: this.cardNumber,
        level
      })
      return {
        code: 0,
        message: '',
        traceId: '',
        data: response.data
      }
    } catch (error) {
      console.error('Failed to adjust card level:', error)
      throw error
    }
  }

  async voidCoupon(couponId: string): Promise<void> {
    throw new Error('Coupon voiding is not supported through updateMember API')
  }
  */
}
