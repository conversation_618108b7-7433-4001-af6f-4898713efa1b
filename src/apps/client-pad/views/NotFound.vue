<template>
  <div class="not-found p-4">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6 text-center">
      <div class="text-6xl font-bold text-gray-300 mb-4">404</div>
      <h1 class="text-2xl font-bold text-gray-800 mb-2">页面未找到</h1>
      <p class="text-gray-600 mb-6">您请求的页面不存在或已被移除</p>

      <button @click="goBack" class="bg-gray-700 hover:bg-gray-800 text-white px-4 py-2 rounded-lg mr-2">返回上一页</button>

      <button @click="goHome" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">回到首页</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.back();
};

const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
.not-found {
  min-height: calc(100vh - 4rem);
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
}
</style>
