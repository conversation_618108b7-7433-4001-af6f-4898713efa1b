<template>
  <div class="flex flex-col h-screen bg-gray-100">
    <!-- 上方内容区域 - 支持滚动 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧区域 -->
      <div class="w-[630px] flex flex-col overflow-hidden">
        <!-- 头部区域 -->
        <div class="bg-white p-[24px] flex items-center gap-[24px] shrink-0">
          <el-button @click="vm.actions.goBack" icon="ArrowLeft" class="btn-normal w-[120px] h-[60px]">返回</el-button>
          <h1 class="text-[#E9223A] text-[32px]">会员详情</h1>
        </div>

        <!-- 会员卡片和信息区域 - 支持滚动 -->
        <div v-if="vm.state.loading" class="flex-1 bg-white p-[24px] flex justify-center items-center overflow-y-auto">
          <el-skeleton :rows="10" animated />
        </div>
        <div v-else-if="vm.state.memberInfo" class="flex-1 bg-white p-[24px] overflow-y-auto scrollbar-hide">
          <!-- 会员卡信息 -->
          <div class="flex justify-center w-full">
            <div
              class="member-card mb-8 rounded-lg overflow-hidden cursor-pointer"
              :class="{
                'card-disabled': !vm.computed.isActive.value
              }">
              <div class="card-content">
                <div class="p-[16px] h-full flex flex-col">
                  <div class="flex justify-between items-center">
                    <span class="text-[#FFFFFF] text-[28px] font-bold">{{ vm.state.memberInfo.member.cardNumber }}</span>
                  </div>

                  <div class="text-[#FFFFFF] text-[16px]">
                    {{ vm.state.memberInfo.member.cardLevelName || '-' }}
                  </div>

                  <div class="mt-auto flex items-baseline">
                    <span class="text-[#365a69] text-[16px] opacity-80">余额</span>
                    <PriceDisplay :amount-in-fen="vm.computed.totalBalance.value" class="price-display-normal price-display-large ml-[12px]" />
                  </div>

                  <div v-if="!vm.computed.isActive.value" class="card-status status-frozen">冻结</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 基础信息 -->
          <div class="mb-6 pb-4 px-[24px]">
            <div class="text-lg font-bold mb-4 flex justify-between items-center">
              <span class="label-value">基础信息</span>
              <!-- <el-button type="normal" size="small" round @click="vm.actions.editBasicInfo">编辑</el-button> -->
            </div>
            <!-- 两列布局 -->
            <div class="grid grid-cols-2 gap-[48px]">
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="label-text">卡号</span>
                  <span class="label-value">{{ vm.state.memberInfo.member.cardNumber || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">卡类型</span>
                  <span class="label-value">{{ vm.computed.cardTypeName.value }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">卡等级</span>
                  <span class="label-value">{{ vm.state.memberInfo.member.cardLevelName || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">开卡时间</span>
                  <span class="label-value">{{ vm.computed.cardStartTime.value }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">有效期至</span>
                  <span class="label-value">{{ vm.computed.cardEndTime.value }}</span>
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="label-text">姓名</span>
                  <span class="label-value">{{ vm.state.memberInfo.member.name || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">手机号</span>
                  <span class="label-value">{{ vm.state.memberInfo.member.phone || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">性别</span>
                  <span class="label-value">{{ vm.computed.formattedGender.value }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">生日</span>
                  <span class="label-value">{{ vm.computed.formattedBirthday.value }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="label-text">状态</span>
                  <span class="label-value">{{ vm.computed.isActive.value ? '正常' : '冻结' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 账户余额 -->
          <div class="mb-6 pb-4 px-[24px]">
            <div class="flex flex-col">
              <span class="label-value">账户余额</span>
              <div class="flex items-center justify-between my-[12px]">
                <PriceDisplay :amount-in-fen="vm.computed.totalBalance.value" class="price-display-normal" />
                <!-- <div>
                  <el-button type="normal" size="small" round @click="vm.actions.recharge">转账</el-button>
                  <el-button type="primary" size="small" round @click="vm.actions.recharge">充值</el-button>
                </div> -->
              </div>
            </div>
            <!-- 两列布局 -->
            <div class="grid grid-cols-2 gap-[48px]">
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="label-text">本金余额</span>
                  <PriceDisplay :amount-in-fen="vm.state.memberInfo.member.principalBalance" class="price-display-small" />
                </div>
                <div class="flex justify-between">
                  <span class="label-text">包厢赠金</span>
                  <PriceDisplay :amount-in-fen="vm.state.memberInfo.member.roomBonusBalance" class="price-display-small" />
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="label-text">通用赠金</span>
                  <PriceDisplay :amount-in-fen="vm.state.memberInfo.member.commonBonusBalance" class="price-display-small" />
                </div>
                <div class="flex justify-between">
                  <span class="label-text">商品赠金</span>
                  <PriceDisplay :amount-in-fen="vm.state.memberInfo.member.goodsBonusBalance" class="price-display-small" />
                </div>
                <div class="flex justify-between hidden">
                  <span class="label-text">积分</span>
                  <span class="label-value">{{ vm.state.memberInfo.member.points || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 - 消费记录历史 -->
      <div class="w-[1120px] m-[24px] p-[24px] bg-white rounded-lg flex flex-col overflow-hidden">
        <div class="p-4 border-b flex justify-between items-center shrink-0">
          <h2 class="text-xl font-bold">消费记录</h2>
        </div>

        <div class="flex-1 p-4 overflow-y-auto">
          <el-table :data="vm.state.consumptionRecords" stripe v-loading="vm.state.loading" :empty-text="vm.state.loading ? '加载中...' : '暂无消费记录'">
            <el-table-column prop="billNo" label="单号" max-width="160">
              <template #default="{ row }">{{ formatId(row.billNo) }}</template>
            </el-table-column>
            <el-table-column prop="store" label="门店" align="center" />

            <el-table-column prop="comsumeType" label="消费类型" align="center">
              <template #default="{ row }">{{ row.consumeLabel }}</template>
            </el-table-column>
            <el-table-column prop="amount" label="消费金额" align="right">
              <template #default="{ row }">
                <div class="w-full flex justify-end"><PriceDisplay :amount-in-fen="row.payRecordTotalAmout" class="price-display-small" /></div>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="卡余额" align="right">
              <template #default="{ row }">
                <div class="w-full flex justify-end"><PriceDisplay :amount-in-fen="row.memberCardBalance" class="price-display-small" /></div>
              </template>
            </el-table-column>
            <el-table-column prop="time" label="消费时间" width="240" align="center">
              <template #default="{ row }">{{ row.formattedTime }}</template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 底部固定高度的按钮区域 -->
    <div class="h-[120px] bg-white border-t flex justify-center items-center gap-[24px] px-[60px] shrink-0">
      <el-button class="btn-border-gray" @click="vm.actions.recharge">充值</el-button>
      <el-button class="btn-border-gray" @click="vm.actions.renewCard">续卡</el-button>
      <el-button class="btn-border-gray" @click="vm.actions.editBasicInfo">信息编辑</el-button>
      <el-button class="btn-border-gray" @click="vm.actions.adjustCardLevel">等级调整</el-button>
      <!-- <el-button class="btn-border-gray" @click="vm.actions.adjustPoints">积分变更</el-button> -->
      <!-- <el-button class="btn-border-gray" @click="vm.actions.manageCoupons">优惠券管理</el-button> -->
      <!-- <el-button class="btn-border-gray" @click="vm.actions.setPassword">设置密码</el-button> -->
      <!-- <el-button class="btn-border-gray" @click="vm.actions.bindAccount">绑定</el-button> -->
      <!-- <el-button class="btn-border-gray" @click="vm.actions.reportLoss">挂失</el-button> -->
      <el-button class="btn-border-gray" @click="vm.actions.freezeCard">
        {{ vm.computed.isActive.value ? '冻结' : '解冻' }}
      </el-button>
      <!-- <el-button class="btn-border-gray" @click="vm.actions.replaceCard">补卡</el-button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { ArrowLeft } from '@element-plus/icons-vue';
import { useMemberDetail } from './presenter';
import { formatId } from '@/utils/IdFormater';

// 初始化，获取会员ID
const route = useRoute();
const memberId = route.params.id as string;
const vm = useMemberDetail(memberId);
</script>

<style scoped>
.member-card {
  position: relative;
  width: 334px;
  height: 180px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  background: url('@/assets/images/membercard_bigbg.png') no-repeat center center;
  background-size: cover;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.member-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

.card-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.card-disabled {
  background-image: url('@/assets/images/membercard_bigbg_gray.png');
}

.card-status {
  position: absolute;
  top: 1.25rem;
  right: 1.25rem;
  padding: 0.35rem 0.85rem;
  border-radius: 0.35rem;
  font-size: 1rem;
  font-weight: 500;
}

.status-frozen {
  background-color: rgba(239, 68, 68, 0.2);
  color: rgb(254, 226, 226);
}

.card-balance {
  margin-top: auto;
}

.price-display-normal {
  color: #365a69 !important;
}

.label-text {
  font-size: 16px;
  color: #666666;
}

.label-value {
  font-size: 16px;
  color: #000;
  font-weight: 5000;
}
</style>
