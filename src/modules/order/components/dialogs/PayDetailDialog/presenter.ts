import { computed, reactive } from 'vue';
import type { IPayDetailViewModel, IPayDetailState, IPayDetailComputed, IPayDetailActions, PayBillVO, PayRecordVO } from './viewmodel';

export class PayDetailPresenter implements IPayDetailViewModel {
  // 状态
  public state: IPayDetailState = reactive({
    payBillVOs: [],
    payRecordVOs: [],
    visible: true
  });

  // 计算属性
  public computed: IPayDetailComputed = {
    // 计算应收总金额（分转元，保留两位小数）
    totalShouldFee: computed(() => {
      return this.state.payBillVOs.reduce((sum, bill) => sum + (bill.shouldFee || 0), 0) / 100;
    }),

    // 计算实收总金额（分转元，保留两位小数）
    totalActualFee: computed(() => {
      return this.state.payRecordVOs.reduce((sum, record) => sum + (record.totalFee || 0), 0) / 100;
    }),

    // 支付方式映射
    payTypeMap: computed(() => {
      return {
        cash: '现金',
        wechat: '微信支付',
        alipay: '支付宝',
        card: '刷卡',
        other: '其他'
      };
    }),

    // 状态映射
    statusMap: computed(() => {
      return {
        success: '支付成功',
        pending: '待支付',
        failed: '支付失败',
        refund: '已退款',
        paid: '已结'
      };
    }),

    // 按支付方式分组的支付金额
    paymentsByType: computed(() => {
      // 创建一个Map用于存储每种支付方式的总金额
      const paymentSummary: Record<string, number> = {};

      // 遍历所有支付记录，按payType分组并累加金额
      this.state.payRecordVOs.forEach(record => {
        const payType = record.payType || 'other';
        if (!paymentSummary[payType]) {
          paymentSummary[payType] = 0;
        }
        paymentSummary[payType] += record.totalFee || 0;
      });

      // 转换为数组形式，便于模板遍历
      return Object.entries(paymentSummary).map(([type, amount]) => ({
        payType: type,
        displayName: this.computed.payTypeMap.value[type] || type,
        amount: amount / 100 // 转为元
      }));
    })
  };

  // 动作
  public actions: IPayDetailActions = {
    // 关闭对话框
    handleClose: () => {
      this.state.visible = false;
    },

    // 打印功能
    handlePrint: () => {
      console.log('打印支付详情');
      // 实际打印功能需要根据项目需求实现
      window.print();
    }
  };

  // 构造函数，接收初始数据
  constructor(payBillVOs: PayBillVO[] = [], payRecordVOs: PayRecordVO[] = []) {
    this.state.payBillVOs = payBillVOs;
    this.state.payRecordVOs = payRecordVOs;
  }
}

// 导出组合式函数
export function usePayDetail(payBillVOs: PayBillVO[] = [], payRecordVOs: PayRecordVO[] = []): IPayDetailViewModel {
  return new PayDetailPresenter(payBillVOs, payRecordVOs);
}
