# Thunder ERP Windows 桌面应用开发指南

## 目录

1. [项目概述](#1-项目概述)
2. [开发环境准备](#2-开发环境准备)
3. [项目结构设计](#3-项目结构设计)
4. [核心功能实现](#4-核心功能实现)
5. [构建与打包](#5-构建与打包)
6. [测试与验证](#6-测试与验证)
7. [常见问题](#7-常见问题)

## 1. 项目概述

本项目旨在将现有 Thunder ERP Web 应用封装为 Windows 桌面应用，通过加载远程 URL 方式实现，确保与 Android 版本功能完全一致。我们将使用 Electron 作为桌面应用框架，实现对原有 Android WebView 接口的完全兼容。

### 1.1 技术选型

- **框架**: Electron 28+
- **打包工具**: Electron Builder
- **加载方式**: 远程 URL 加载（而非本地文件）
- **原生功能**: 通过 Electron 主进程和 IPC 通信实现与 Android 版本相同的接口

### 1.2 功能要点

1. 远程 URL 加载与参数传递
2. JSBridge 等效实现
3. 打印功能支持（与 Android 版本功能一致）
4. 应用升级机制（兼容服务器更新策略）
5. 设备信息获取（与 Android 提供格式一致）
6. VOD 服务代理实现

## 2. 开发环境准备

### 2.1 安装必要工具

```bash
# 安装 Node.js 18+ (建议使用 nvm)
nvm install 18
nvm use 18

# 安装项目依赖
npm install -g electron electron-builder

# 准备项目目录
mkdir thunder-erp-electron
cd thunder-erp-electron
npm init -y
```

### 2.2 安装项目依赖

```bash
npm install --save-dev electron electron-builder
npm install --save electron-updater electron-store axios
```

## 3. 项目结构设计

精简项目结构，专注于远程 URL 加载和接口实现：

```
thunder-erp-electron/
├── src/
│   ├── main/              # 主进程代码
│   │   ├── index.js       # 主进程入口文件（远程URL加载）
│   │   ├── updater.js     # 应用更新模块
│   │   ├── printer.js     # 打印模块
│   │   ├── vod.js         # VOD服务模块
│   │   └── ipc.js         # IPC 通信处理
│   ├── preload/           # 预加载脚本
│   │   ├── index.js       # 预加载主文件
│   │   ├── bridge.js      # JS 桥接实现
│   │   └── interfaces/    # 接口实现目录
│   │       ├── webview.js  # WebView接口
│   │       ├── printer.js  # 打印接口
│   │       ├── vod.js      # VOD接口
│   │       └── upgrade.js  # 升级接口
│   └── assets/            # 静态资源
├── build/                 # 构建资源
│   └── icon.ico           # 应用图标
├── package.json           # 项目配置
└── README.md              # 项目说明
```

## 4. 核心功能实现

### 4.1 主进程入口文件 (src/main/index.js)

```javascript
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const os = require('os');
const setupPrinter = require('./printer');
const setupUpdater = require('./updater');
const setupVod = require('./vod');
const setupIPC = require('./ipc');

// 全局窗口引用
let mainWindow = null;

// 创建主窗口
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    title: 'Thunder ERP',
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false, // 允许跨域请求
      allowRunningInsecureContent: true // 允许加载混合内容
    }
  });

  // 获取设备信息用于URL参数
  const deviceId = os.hostname();
  const ipAddress = getIpAddress();

  // 加载远程URL并传递设备信息参数
  const remoteUrl = `https://your-server.com/erp-web?deviceId=${deviceId}&ipAddress=${ipAddress}&macAddress=${deviceId}`;
  mainWindow.loadURL(remoteUrl);

  // 开发环境打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // 窗口关闭时清除引用
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 获取IP地址函数
function getIpAddress() {
  const nets = os.networkInterfaces();
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // 排除内部接口和非IPv4接口
      if (!net.internal && net.family === 'IPv4') {
        return net.address;
      }
    }
  }
  return '127.0.0.1';
}

// 应用初始化
app.whenReady().then(() => {
  // 设置打印服务
  setupPrinter(ipcMain);

  // 设置更新服务
  setupUpdater(ipcMain, mainWindow);

  // 设置VOD服务
  setupVod(ipcMain);

  // 设置IPC通信
  setupIPC(ipcMain, mainWindow);

  // 创建窗口
  createWindow();

  // macOS 应用激活时创建窗口
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});
```

### 4.2 预加载脚本 (src/preload/index.js)

```javascript
const { contextBridge, ipcRenderer } = require('electron');
const setupWebViewBridge = require('./interfaces/webview');
const setupPrinterBridge = require('./interfaces/printer');
const setupVodBridge = require('./interfaces/vod');
const setupUpgradeBridge = require('./interfaces/upgrade');
const setupJSBridge = require('./bridge');

// 设置各种接口，确保与Android版本接口完全一致
setupWebViewBridge(contextBridge, ipcRenderer);
setupPrinterBridge(contextBridge, ipcRenderer);
setupVodBridge(contextBridge, ipcRenderer);
setupUpgradeBridge(contextBridge, ipcRenderer);
setupJSBridge(contextBridge, ipcRenderer);

// 设备信息 - 与Android提供的格式保持一致
contextBridge.exposeInMainWorld('deviceInfo', {
  getDeviceId: () => ipcRenderer.invoke('get-device-id'),
  getIpAddress: () => ipcRenderer.invoke('get-ip-address'),
  getMacAddress: () => ipcRenderer.invoke('get-mac-address'),
  getPlatformInfo: () => ipcRenderer.invoke('get-platform-info')
});
```

### 4.3 JS桥接实现 (src/preload/bridge.js)

```javascript
// 实现与Android JSBridge完全相同的功能
module.exports = function setupJSBridge(contextBridge, ipcRenderer) {
  contextBridge.exposeInMainWorld('JSBridge', {
    // 执行JavaScript代码
    evaluateJavascript: script => {
      try {
        return eval(script);
      } catch (e) {
        console.error('执行JavaScript失败:', e);
        return null;
      }
    },

    // 调用JavaScript方法
    callJsMethod: (methodName, ...args) => {
      if (typeof window[methodName] === 'function') {
        try {
          return window[methodName](...args);
        } catch (e) {
          console.error(`调用 ${methodName} 方法失败:`, e);
          return null;
        }
      } else {
        console.warn(`${methodName} 不是一个函数`);
        return null;
      }
    }
  });
};
```

### 4.4 WebView接口实现 (src/preload/interfaces/webview.js)

```javascript
// 完全模拟WebViewJSInterface的功能
module.exports = function setupWebViewBridge(contextBridge, ipcRenderer) {
  contextBridge.exposeInMainWorld('webViewBridge', {
    // 重新加载WebView
    reloadWebView: async () => {
      // 获取设备信息用于URL参数
      const deviceId = await ipcRenderer.invoke('get-device-id');
      const ipAddress = await ipcRenderer.invoke('get-ip-address');

      // 构建完整URL
      const baseUrl = window.location.origin + window.location.pathname;
      const url = `${baseUrl}?deviceId=${deviceId}&ipAddress=${ipAddress}&macAddress=${deviceId}`;

      // 加载URL
      window.location.href = url;
    },

    // 退出应用程序
    exitApp: () => {
      ipcRenderer.invoke('exit-app');
    }
  });
};
```

### 4.5 VOD接口实现 (src/preload/interfaces/vod.js)

```javascript
// 完全模拟VodJSInterface的功能
module.exports = function setupVodBridge(contextBridge, ipcRenderer) {
  contextBridge.exposeInMainWorld('vodBridge', {
    // 发送请求
    request: (method, url, params, body, callbackId) => {
      ipcRenderer
        .invoke('vod-request', { method, url, params, body })
        .then(result => {
          // 使用与Android版本相同的回调格式
          if (typeof window.vodBridgeCallback === 'function') {
            window.vodBridgeCallback(callbackId, JSON.stringify(result));
          }
        })
        .catch(error => {
          // 错误处理
          const errorObj = {
            errcode: 500,
            errmsg: `请求失败: ${error.message}`
          };
          if (typeof window.vodBridgeCallback === 'function') {
            window.vodBridgeCallback(callbackId, JSON.stringify(errorObj));
          }
        });
    }
  });
};
```

### 4.6 打印接口实现 (src/preload/interfaces/printer.js)

```javascript
// 完全模拟PrinterJSInterface的功能
module.exports = function setupPrinterBridge(contextBridge, ipcRenderer) {
  contextBridge.exposeInMainWorld('printerBridge', {
    // 扫描打印机
    scanPrinters: async () => {
      return await ipcRenderer.invoke('scan-printers');
    },

    // 原始打印
    rawPrint: async (ip, port, commandsJson) => {
      return await ipcRenderer.invoke('raw-print', { ip, port, commandsJson });
    },

    // 测试打印
    printTest: async (ip, port) => {
      return await ipcRenderer.invoke('print-test', { ip, port });
    }
  });
};
```

### 4.7 升级接口实现 (src/preload/interfaces/upgrade.js)

```javascript
// 完全模拟AppUpgradeJSInterface的功能
module.exports = function setupUpgradeBridge(contextBridge, ipcRenderer) {
  contextBridge.exposeInMainWorld('appUpgradeBridge', {
    // 检查更新
    checkUpgrade: callbackName => {
      ipcRenderer.invoke('check-upgrade').then(result => {
        if (typeof window[callbackName] === 'function') {
          window[callbackName](result);
        }
      });
    },

    // 执行升级
    upgrade: callbackName => {
      ipcRenderer.invoke('start-upgrade').then(result => {
        if (typeof window[callbackName] === 'function') {
          window[callbackName](result);
        }
      });
    }
  });

  // 监听升级事件
  ipcRenderer.on('upgrade-event', (event, data) => {
    if (typeof window.appUpgradeEvent === 'function') {
      window.appUpgradeEvent(data.event, data.data);
    }
  });
};
```

### 4.8 VOD服务模块 (src/main/vod.js)

```javascript
const axios = require('axios');

module.exports = function setupVod(ipcMain) {
  // 处理VOD请求
  ipcMain.handle('vod-request', async (event, { method, url, params, body }) => {
    try {
      const response = await axios({
        method: method.toLowerCase(),
        url: `${url}?${params}`,
        data: body
      });

      return response.data;
    } catch (error) {
      console.error('VOD请求失败:', error);
      return {
        errcode: error.response?.status || 500,
        errmsg: `请求失败: ${error.message}`
      };
    }
  });
};
```

### 4.9 打印模块 (src/main/printer.js)

```javascript
const net = require('net');

module.exports = function setupPrinter(ipcMain) {
  // 扫描网络打印机
  ipcMain.handle('scan-printers', async () => {
    try {
      // 实现打印机扫描逻辑，或返回预配置的打印机列表
      const printers = [
        { ip: '*************', port: 9100 },
        { ip: '*************', port: 9100 }
      ];
      return JSON.stringify(printers);
    } catch (error) {
      console.error('扫描打印机失败:', error);
      return '[]';
    }
  });

  // 发送原始打印命令
  ipcMain.handle('raw-print', async (event, { ip, port, commandsJson }) => {
    try {
      const commands = JSON.parse(commandsJson);

      return await new Promise((resolve, reject) => {
        const client = new net.Socket();

        // 设置超时
        client.setTimeout(5000);

        client.on('timeout', () => {
          client.end();
          reject(new Error('打印超时'));
        });

        client.on('error', err => {
          reject(err);
        });

        client.connect(port, ip, () => {
          console.log(`已连接到打印机 ${ip}:${port}`);

          // 依次发送命令
          for (const cmd of commands) {
            let data;

            switch (cmd.type) {
              case 'text':
                data = Buffer.from(cmd.data, 'utf8');
                break;
              case 'command':
              case 'raw':
                data = Buffer.from(cmd.data);
                break;
            }

            if (data) {
              client.write(data);
            }
          }

          // 关闭连接
          client.end(() => {
            resolve(true);
          });
        });
      });
    } catch (error) {
      console.error('打印失败:', error);
      return false;
    }
  });

  // 测试打印
  ipcMain.handle('print-test', async (event, { ip, port }) => {
    try {
      const commands = [
        { type: 'command', data: [27, 64] }, // 初始化打印机
        { type: 'command', data: [27, 33, 0] }, // 设置正常字体
        { type: 'command', data: [27, 97, 1] }, // 居中对齐
        { type: 'text', data: '\n打印机测试页\n----------------\n' },
        { type: 'text', data: `IP: ${ip}\n` },
        { type: 'text', data: `端口: ${port}\n` },
        { type: 'text', data: `时间: ${new Date().toLocaleString()}\n` },
        { type: 'text', data: '----------------\n\n\n' },
        { type: 'command', data: [29, 86, 49] } // 切纸
      ];

      const commandsJson = JSON.stringify(commands);
      return await ipcMain.handle('raw-print', event, { ip, port, commandsJson });
    } catch (error) {
      console.error('测试打印失败:', error);
      return false;
    }
  });
};
```

### 4.10 IPC通信处理 (src/main/ipc.js)

```javascript
const os = require('os');
const { app } = require('electron');

module.exports = function setupIPC(ipcMain, mainWindow) {
  // 获取设备 ID（主机名）
  ipcMain.handle('get-device-id', () => {
    return os.hostname();
  });

  // 获取 IP 地址
  ipcMain.handle('get-ip-address', () => {
    const nets = os.networkInterfaces();
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // 跳过内部接口和非 IPv4 接口
        if (!net.internal && net.family === 'IPv4') {
          return net.address;
        }
      }
    }
    return '127.0.0.1';
  });

  // 获取 MAC 地址
  ipcMain.handle('get-mac-address', () => {
    const nets = os.networkInterfaces();
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // 跳过内部接口
        if (!net.internal) {
          return net.mac;
        }
      }
    }
    return '00:00:00:00:00:00';
  });

  // 获取平台信息
  ipcMain.handle('get-platform-info', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      osVersion: os.release(),
      osName: os.type()
    };
  });

  // 退出应用
  ipcMain.handle('exit-app', () => {
    app.quit();
  });
};
```

### 4.11 应用更新模块 (src/main/updater.js)

```javascript
const { autoUpdater } = require('electron-updater');
const { dialog } = require('electron');

module.exports = function setupUpdater(ipcMain, mainWindow) {
  // 配置更新服务器
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: 'https://your-update-server.com/updates/'
  });

  // 检查更新错误
  autoUpdater.on('error', error => {
    console.error('更新出错:', error);
    // 发送错误事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeFailed',
        data: error.message
      });
    }
  });

  // 检查到更新
  autoUpdater.on('update-available', info => {
    console.log('有可用更新:', info);
    // 发送更新事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onCheckResult',
        data: {
          hasUpdate: true,
          upgradeInfo: {
            version: info.version,
            releaseNotes: info.releaseNotes,
            isForceUpgrade: info.releaseNotes?.includes('FORCE') || false
          }
        }
      });
    }
  });

  // 没有更新
  autoUpdater.on('update-not-available', () => {
    console.log('无可用更新');
    // 发送无更新事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onCheckResult',
        data: {
          hasUpdate: false,
          upgradeInfo: null
        }
      });
    }
  });

  // 更新下载进度
  autoUpdater.on('download-progress', progressObj => {
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onDownloadProgress',
        data: progressObj
      });
    }
  });

  // 更新下载完成
  autoUpdater.on('update-downloaded', () => {
    console.log('更新下载完成');
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeComplete',
        data: {}
      });

      dialog
        .showMessageBox({
          type: 'info',
          title: '应用更新',
          message: '更新已下载，是否立即安装？',
          buttons: ['是', '否']
        })
        .then(returnValue => {
          if (returnValue.response === 0) {
            autoUpdater.quitAndInstall();
          }
        });
    }
  });

  // 检查更新
  ipcMain.handle('check-upgrade', async () => {
    try {
      console.log('开始检查更新');
      const result = await autoUpdater.checkForUpdates();
      if (result) {
        return {
          success: true,
          data: {
            hasUpdate: !!result.updateInfo,
            upgradeInfo: result.updateInfo
              ? {
                  version: result.updateInfo.version,
                  releaseNotes: result.updateInfo.releaseNotes,
                  isForceUpgrade: result.updateInfo.releaseNotes?.includes('FORCE') || false
                }
              : null
          }
        };
      } else {
        return {
          success: true,
          data: {
            hasUpdate: false,
            upgradeInfo: null
          }
        };
      }
    } catch (error) {
      console.error('检查更新失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  });

  // 开始升级
  ipcMain.handle('start-upgrade', async () => {
    try {
      console.log('开始下载更新');
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeStarted',
        data: {}
      });

      autoUpdater.downloadUpdate();
      return { success: true, message: '开始下载更新' };
    } catch (error) {
      console.error('开始升级失败:', error);
      return { success: false, message: error.message };
    }
  });

  // 自动检查更新（应用启动时）
  setTimeout(() => {
    autoUpdater.checkForUpdates();
  }, 3000);
};
```

## 5. 构建与打包

### 5.1 准备package.json

```json
{
  "name": "thunder-erp-electron",
  "version": "1.0.0",
  "description": "Thunder ERP Windows Desktop Application",
  "main": "src/main/index.js",
  "scripts": {
    "start": "electron .",
    "dev": "NODE_ENV=development electron .",
    "build": "electron-builder",
    "build:win": "electron-builder --win --x64",
    "postinstall": "electron-builder install-app-deps"
  },
  "author": "Thunder ERP Team",
  "license": "ISC",
  "dependencies": {
    "axios": "^1.7.7",
    "electron-updater": "^6.1.7",
    "electron-store": "^8.1.0"
  },
  "devDependencies": {
    "electron": "^28.1.0",
    "electron-builder": "^24.9.1"
  },
  "build": {
    "appId": "com.thunder.erp",
    "productName": "Thunder ERP",
    "directories": {
      "output": "dist"
    },
    "files": ["src/**/*", "package.json"],
    "win": {
      "target": ["nsis"],
      "icon": "build/icon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "Thunder ERP"
    },
    "publish": {
      "provider": "generic",
      "url": "https://your-update-server.com/updates/"
    }
  }
}
```

### 5.2 构建流程

不再需要复制前端构建产物，直接构建即可：

1. **准备构建环境**

```bash
# 安装所有依赖
npm install
```

2. **开发模式运行**

```bash
npm run dev
```

3. **构建 Windows 安装包**

```bash
npm run build:win
```

完成后，安装包将在 `dist` 目录下生成。

## 6. 测试与验证

### 6.1 功能测试清单

- [ ] 远程URL加载
- [ ] WebViewJSInterface 功能
- [ ] PrinterJSInterface 功能
- [ ] VodJSInterface 功能
- [ ] AppUpgradeJSInterface 功能
- [ ] JSBridge 功能
- [ ] 设备信息获取
- [ ] 应用更新

### 6.2 常见问题排查

1. **远程URL加载失败**

   - 检查网络连接
   - 确认URL是否正确
   - 检查是否有跨域问题

2. **JS接口无法调用**

   - 确认contextBridge暴露的接口名称与Android版本完全一致
   - 检查预加载脚本是否正确加载
   - 检查远程页面是否尝试调用了接口

3. **打印功能不工作**
   - 检查网络打印机连接
   - 确认打印命令格式正确

## 7. 常见问题

### 7.1 远程URL加载注意事项

- 确保远程服务器允许跨域请求
- 考虑应用离线使用场景
- 根据加载的URL调整窗口大小和标题

### 7.2 Electron安全性考虑

- 注意nodeIntegration设置为false
- 使用contextIsolation和预加载脚本提供安全的API
- 不要直接在渲染进程中使用Node.js API

### 7.3 接口兼容性

- 确保所有接口名称、参数和返回值与Android版本完全一致
- 对于Windows特有功能，可以通过额外的接口提供
- 考虑接口调用的异步性质差异
