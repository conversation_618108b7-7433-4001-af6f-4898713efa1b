import { Receipt } from '../../shared/models/receipt-model';
import { TextAlignment, FontSize, ReceiptTableColumn } from '../../shared/models/receipt-model';
import { ReceiptFactory } from '../../shared/utils/receipt-factory';
import { SessionOrderData } from '../models/session-order-data';
import { formatDateTime } from '@/utils/dateUtils';
import { formatYuan } from '@/utils/priceUtils';

/**
 * 开台单构建器
 * 负责将开台单数据转换为结构化的票据表示
 */
export class SessionOrderBuilder {
  /**
   * 构建开台单票据
   * @param data 开台单数据实体
   * @param venueId 场馆ID
   * @param title 票据标题，默认为"开台单"
   */
  build(data: SessionOrderData, venueId: string, title: string = '开台单'): Receipt {
    const receipt: Receipt = { elements: [] };
    const elements = receipt.elements;

    // 标题
    elements.push(
      ReceiptFactory.text(title, {
        align: TextAlignment.CENTER,
        bold: true,
        fontSize: FontSize.DOUBLE,
      })
    );
    elements.push(
      ReceiptFactory.text('\n', {
        fontSize: FontSize.NORMAL,
        bold: false,
      })
    );

    // 基本信息
    elements.push(
      ReceiptFactory.text(`店铺名称: ${data.shopName || '--'}`, {
        fontSize: FontSize.SMALL,
        align: TextAlignment.LEFT,
      })
    );
    elements.push(
      ReceiptFactory.text(`包厢名称: ${data.roomInfo?.name || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`开台单号: ${data.sessionId || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );

    // 预订信息（如果有）
    // if (data.reservationInfo) {
    //   const reservationInfo = data.reservationInfo;
    //   elements.push(
    //     ReceiptFactory.text(`预订会员卡号: ${reservationInfo.memberCardNo || ''}`, {
    //       fontSize: FontSize.SMALL,
    //     })
    //   );
    //   elements.push(
    //     ReceiptFactory.text(`预订会员名称: ${reservationInfo.memberName || ''}`, {
    //       fontSize: FontSize.SMALL,
    //     })
    //   );
    //   elements.push(
    //     ReceiptFactory.text(`预订会员手机号: ${reservationInfo.memberPhone || ''}`, {
    //       fontSize: FontSize.SMALL,
    //     })
    //   );
    // }

    // 时间信息
    elements.push(
      ReceiptFactory.text(`开台时间: ${formatDateTime(data.openTime)}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`消费开始时间: ${formatDateTime(data.startTime)}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`消费结束时间: ${formatDateTime(data.endTime)}`, {
        fontSize: FontSize.SMALL,
      })
    );
    
    elements.push(ReceiptFactory.line());

    // 房间消费信息
    if (data.roomPackages?.length) {
      elements.push(
        ReceiptFactory.tableRow([
          { text: '项目', width: 30, align: TextAlignment.LEFT },
          { text: '时长', width: 10, align: TextAlignment.RIGHT },
          { text: '原价', width: 10, align: TextAlignment.RIGHT },
          { text: '现价', width: 10, align: TextAlignment.RIGHT },
        ])
      );

      // 创建房间方案数据行
      const roomPackageRows = data.roomPackages.map(roomPackage => [
        { text: roomPackage.planName, width: 30 },
        { text: `${roomPackage.duration}`, width: 10, align: TextAlignment.RIGHT },
        { text: formatYuan(roomPackage.originalPrice), width: 10, align: TextAlignment.RIGHT },
        { text: formatYuan(roomPackage.actualPrice), width: 10, align: TextAlignment.RIGHT },
      ]);

      elements.push(ReceiptFactory.table(roomPackageRows));
    }

    elements.push(ReceiptFactory.line());
    elements.push(
      ReceiptFactory.text(`包厢消费总计: ${formatYuan(data.roomFeeTotal)}`, {
        align: TextAlignment.RIGHT,
        bold: true,
      })
    );

    elements.push(ReceiptFactory.line());

    // 商品消费信息
    if (data.products?.length) {
      elements.push(
        ReceiptFactory.tableRow([
          { text: '名称', width: 20, align: TextAlignment.LEFT },
          { text: '数量', width: 10, align: TextAlignment.RIGHT },
          { text: '原价', width: 10, align: TextAlignment.RIGHT },
          { text: '现价', width: 10, align: TextAlignment.RIGHT },
          { text: '现价小计', width: 10, align: TextAlignment.RIGHT },
        ])
      );

      // 商品数据行
      const productRows: ReceiptTableColumn[][] = [];
      data.products.forEach((item) => {
        // 添加主商品行
        productRows.push([
          { text: item.productName, width: 20 },
          { text: `${item.quantity}${item.unit || ''}`, width: 10, align: TextAlignment.RIGHT },
          { text: formatYuan(item.price), width: 10, align: TextAlignment.RIGHT },
          { text: formatYuan(item.payPrice), width: 10, align: TextAlignment.RIGHT },
          { text: formatYuan(item.totalAmount), width: 10, align: TextAlignment.RIGHT },
        ]);

        // 如果有子商品（套餐），添加子商品行
        if (item.subProducts?.length) {
          item.subProducts.forEach((subProduct) => {
            productRows.push([
              { text: `   ${subProduct.productName}`, width: 20 }, // 缩进显示子商品
              { text: `${subProduct.quantity}${subProduct.unit || ''}`, width: 10, align: TextAlignment.RIGHT },
              { text: '', width: 10 }, // 子商品不显示价格
              { text: '', width: 10 },
              { text: '', width: 10 },
            ]);
          });
        }
      });

      elements.push(ReceiptFactory.table(productRows));
    }

    elements.push(ReceiptFactory.line());
    elements.push(
      ReceiptFactory.text(`商品消费: ${formatYuan(data.productFeeTotal)}`, {
        align: TextAlignment.RIGHT,
        bold: true,
      })
    );

    // 赠送商品信息
    if (data.giftProducts?.length) {
      elements.push(ReceiptFactory.line());
      elements.push(
        ReceiptFactory.tableRow([
          { text: '名称', width: 20, align: TextAlignment.LEFT },
          { text: '数量', width: 15, align: TextAlignment.RIGHT },
          { text: '原价', width: 15, align: TextAlignment.RIGHT },
          { text: '备注', width: 10, align: TextAlignment.RIGHT },
        ])
      );

      // 赠品数据行
      const giftRows = data.giftProducts.map((item) => [
        { text: item.productName, width: 20 },
        { text: `${item.quantity}${item.unit || ''}`, width: 15, align: TextAlignment.RIGHT },
        { text: formatYuan(item.price), width: 15, align: TextAlignment.RIGHT },
        { text: item.remark || '赠送', width: 10, align: TextAlignment.RIGHT },
      ]);

      elements.push(ReceiptFactory.table(giftRows));
      elements.push(ReceiptFactory.line());
      elements.push(
        ReceiptFactory.text(`赠送小计: ${formatYuan(data.giftProductTotal || 0)}`, {
          align: TextAlignment.RIGHT,
        })
      );
    }

    elements.push(ReceiptFactory.line());
    elements.push(
      ReceiptFactory.text(`商品消费总计: ${formatYuan(data.productFeeTotal)}`, {
        align: TextAlignment.RIGHT,
        bold: true,
      })
    );

    // // 预付信息
    // elements.push(
    //   ReceiptFactory.text(`预付余额: ${formatYuan(data.prePayBalance || 0)}`, {
    //     fontSize: FontSize.SMALL,
    //   })
    // );

    // 代订人信息（如果有）
    if (data.reservationInfo?.proxyOrderer) {
      elements.push(
        ReceiptFactory.text(`代订人: ${data.reservationInfo.proxyOrderer || ''}`, {
          fontSize: FontSize.SMALL,
        })
      );
      elements.push(
        ReceiptFactory.text(`代订人手机号: ${data.reservationInfo.proxyOrdererPhone || ''}`, {
          fontSize: FontSize.SMALL,
        })
      );
    }

    // 底部信息
    elements.push(
      ReceiptFactory.text(`收银员: ${data.cashierName || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`打单时间: ${formatDateTime(data.printTime)}`, {
        fontSize: FontSize.SMALL,
      })
    );

    // 备注
    if (data.remark) {
      elements.push(
        ReceiptFactory.text(`备注: ${data.remark}`, {
          fontSize: FontSize.SMALL,
        })
      );
    }

    // 编号
    elements.push(ReceiptFactory.emptyLine());
    elements.push(
      ReceiptFactory.text(`No.${venueId}`, {
        align: TextAlignment.CENTER,
      })
    );

    return receipt;
  }
}

// 导出单例
export const sessionOrderBuilder = new SessionOrderBuilder(); 