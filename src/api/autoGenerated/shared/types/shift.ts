// shift相关类型定义
import { PaymentMethodSummary } from './payment';
import { EmployeeVO } from './employee';

export type AddShiftHandoverFormAndPayBillReqDto = {
    /** 支付id */
    payId?: string;
    /** 备注 */
    remark?: string;
    /** 员工ID */
    shiftNo?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddShiftHandoverFormReqDto = {
    alipayPayAmount?: number;
    bankCardPayAmount?: number;
    businessActual?: number;
    businessNet?: number;
    businessReceivable?: number;
    cashPayAmount?: number;
    couponPayAmount?: number;
    creditAmount?: number;
    customerBatches?: number;
    employeeId?: string;
    generalGiftAmount?: number;
    koubeiPayAmount?: number;
    meituanPayAmount?: number;
    memberCardPay?: number;
    memberCardPrincipalAmount?: number;
    memberDiscount?: number;
    merchantDiscount?: number;
    minimumAdjustment?: number;
    operator?: string;
    productGiftAmount?: number;
    rechargeAmount?: number;
    rechargeGift?: number;
    remark?: string;
    roomGiftAmount?: number;
    shiftEndTime?: number;
    shiftStartTime?: number;
    staffGifts?: number;
    venueId?: string;
    wechatPayAmount?: number;
    writeOffActual?: number;
    writeOffDiscount?: number;
    writeOffReceivable?: number;
  };

export type CreateShiftChangePrintRecordReq = {
    /** 打印设备名称 */
    deviceName?: string;
    /** 员工ID (可选，如果为空则使用交班单中的员工ID) */
    employeeId?: string;
    /** 交班单号 */
    handNo: string;
    /** 操作员ID */
    operatorId: string;
    /** 门店ID */
    venueId: string;
  };

export type DeleteShiftHandoverFormAndPayBillReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteShiftHandoverFormReqDto = {
    id?: string;
  };

export type GetShiftChangePrintRecordsByHandNoReqDto = {
    /** 交班单号 */
    handNo: string;
    /** 门店ID */
    venueId: string;
  };

export type QueryShiftHandoverFormAndPayBillReqDto = {
    /** 交班单号 */
    handNo?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 支付id */
    payId?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryShiftHandoverFormReqDto = {
    /** 创建时间结束 */
    ctimeEnd?: number;
    /** 创建时间开始 */
    ctimeStart?: number;
    /** 日期 */
    date?: number;
    /** 员工ID */
    employeeId?: string;
    /** 交班单号 */
    handNo?: string;
    /** 主键ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ResultArrayVoShiftChangePrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftChangePrintRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoShiftHandoverFormAndPayBillVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftHandoverFormAndPayBillVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoShiftHandoverFormVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftHandoverFormVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoShiftChangePrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftChangePrintRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoShiftHandoverFormAndPayBillVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftHandoverFormAndPayBillVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoShiftHandoverFormVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftHandoverFormVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ShiftChangeBillDataVO = {
    /** 异常支付金额 */
    abnormalPayment?: number;
    /** 异常支付笔数 */
    abnormalPaymentCount?: number;
    /** 收银优惠 */
    chargeDiscount?: number;
    /** 已结台数量 */
    closedTableCount?: number;
    /** 优惠券优惠 */
    couponDiscount?: number;
    /** 员工赠送 */
    employeeGift?: number;
    /** 交班员工 */
    handEmployee?: string;
    /** 交班单号 */
    handNo?: string;
    /** 交班时间 */
    handTime?: string;
    /** 会员优惠 */
    memberDiscount?: number;
    /** 商家优惠 */
    merchantDiscount?: number;
    /** 开台数量 */
    openTableCount?: number;
    /** 营业中台数量 */
    openingTableCount?: number;
    /** 包装费 */
    packageFee?: number;
    /** 支付方式汇总 */
    paymentMethods?: PaymentMethodSummary[];
    /** 打印时间 */
    printTime?: string;
    /** 商品金额 */
    productFee?: number;
    /** 账单数量 */
    sessionCount?: number;
    /** 店铺名称 */
    shopName?: string;
    /** 实收金额 */
    totalActual?: number;
    /** 应收金额 */
    totalReceivable?: number;
    /** 抹零优惠 */
    zeroDiscount?: number;
  };

export type ShiftChangePrintRecordVO = {
    /** 创建时间 */
    createTime?: number;
    /** 设备名称 */
    deviceName?: string;
    /** 错误信息 */
    errorMsg?: string;
    /** 交班单号 */
    handNo?: string;
    /** 主键ID */
    id?: string;
    /** 操作员ID */
    operatorId?: string;
    /** 操作员姓名 */
    operatorName?: string;
    /** 打印编号 */
    printNo?: string;
    /** 打印时间 */
    printTime?: number;
    /** 打印类型 */
    printType?: string;
    /** 备注 */
    remark?: string;
    /** 会话ID */
    sessionId?: string;
    /** 交班单数据 */
    shiftChangeBillData?: ShiftChangeBillDataVO;
    /** 状态：0-等待打印，1-打印成功，2-打印失败 */
    status?: number;
    /** 更新时间 */
    updateTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ShiftHandoverFormAndPayBillVO = {
    /** billId */
    billId?: string;
    /** 创建时间 */
    ctime?: number;
    /** 交班单号 */
    handNo?: string;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type ShiftHandoverFormVO = {
    /** 支付方式-支付宝 */
    alipay?: number;
    /** 支付方式-银行卡 */
    bank?: number;
    /** 支付方式-现金 */
    cash?: number;
    /** 创建时间 */
    ctime?: number;
    /** 营业概况-员工赠送 */
    employeeGift?: number;
    /** 交班人ID */
    employeeId?: string;
    /** 交班人名称 */
    employeeName?: string;
    /** 交班人 */
    employeeVO?: EmployeeVO;
    /** 交班单号 */
    handNo?: string;
    /** 交班时间 */
    handTime?: number;
    id?: string;
    /** 支付方式-口碑 */
    koubei?: number;
    /** 营业概况-低消差额 */
    lowConsumptionFee?: number;
    /** 支付方式-美团 */
    meituan?: number;
    /** 营业概况-会员优惠 */
    memberDiscount?: number;
    /** 营业概况-商家优惠 */
    merchantDiscount?: number;
    /** 营业概况-净收 */
    netFee?: number;
    /** 营业数据-开台数 */
    openCount?: number;
    operator?: string;
    /** 营业数据-点单数 */
    orderPaidCount?: number;
    /** 营业数据-待结订单数 */
    orderUnpaidCount?: number;
    /** 支付方式-其他 */
    other?: number;
    /** 营业概况-商品费用 */
    productFee?: number;
    remark?: string;
    /** 营业概况-房费 */
    roomFee?: number;
    /** 营业概况-应收 */
    shouldFee?: number;
    /** 状态 */
    state?: number;
    /** 支付方式-招待券 */
    ticket?: number;
    /** 营业概况-实收 */
    totalFee?: number;
    /** 营业概况-未结金额 */
    unpaidFee?: number;
    /** 更新时间 */
    utime?: number;
    venueId?: string;
    /** 版本号 */
    version?: number;
    /** 支付方式-微信 */
    wechat?: number;
    /** 营业概况-抹零金额 */
    zeroFee?: number;
  };

export type UpdateShiftHandoverFormAndPayBillReqDto = {
    /** ID */
    id?: string;
    /** 支付id */
    payId?: string;
    /** 备注 */
    remark?: string;
    /** 员工ID */
    shiftNo?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateShiftHandoverFormReqDto = {
    alipayPayAmount?: number;
    bankCardPayAmount?: number;
    businessActual?: number;
    businessNet?: number;
    businessReceivable?: number;
    cashPayAmount?: number;
    couponPayAmount?: number;
    creditAmount?: number;
    customerBatches?: number;
    employeeId?: string;
    generalGiftAmount?: number;
    id?: string;
    koubeiPayAmount?: number;
    meituanPayAmount?: number;
    memberCardPay?: number;
    memberCardPrincipalAmount?: number;
    memberDiscount?: number;
    merchantDiscount?: number;
    minimumAdjustment?: number;
    operator?: string;
    productGiftAmount?: number;
    rechargeAmount?: number;
    rechargeGift?: number;
    remark?: string;
    roomGiftAmount?: number;
    shiftEndTime?: number;
    shiftStartTime?: number;
    staffGifts?: number;
    venueId?: string;
    wechatPayAmount?: number;
    writeOffActual?: number;
    writeOffDiscount?: number;
    writeOffReceivable?: number;
  };
