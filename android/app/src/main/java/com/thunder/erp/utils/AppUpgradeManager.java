package com.thunder.erp.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.URLUtil;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;

import com.google.gson.Gson;
import com.thunder.erp.entity.AppUpgradeInfo;
import com.thunder.erp.api.AppUpgradeApi;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 应用升级管理器
 * 负责检查更新、下载和安装APK
 */
public class AppUpgradeManager {
    private static final String TAG = "AppUpgradeManager";
    
    // 下载相关常量
    private static final int BUFFER_SIZE = 8192;
    private static final int CONNECTION_TIMEOUT = 30000;
    private static final int READ_TIMEOUT = 30000;
    
    private final Context context;
    private final String baseUrl;
    private final Gson gson;
    private final Handler mainHandler;
    private final AppUpgradeApi upgradeApi;
    
    private AppUpgradeInfo upgradeInfo;
    private DownloadTask downloadTask;
    private File downloadedApkFile;
    private ProgressDialog progressDialog;
    private UpgradeCallback upgradeCallback;
    private final AtomicBoolean isDownloading = new AtomicBoolean(false);
    
    /**
     * 升级回调接口
     */
    public interface UpgradeCallback {
        /**
         * 检查结果回调
         * @param hasUpdate 是否有更新
         * @param upgradeInfo 升级信息
         */
        void onCheckResult(boolean hasUpdate, AppUpgradeInfo upgradeInfo);
        
        /**
         * 升级开始回调
         */
        void onUpgradeStarted();
        
        /**
         * 升级完成回调
         */
        void onUpgradeComplete();
        
        /**
         * 升级失败回调
         * @param errorMessage 错误信息
         */
        void onUpgradeFailed(String errorMessage);
    }
    
    /**
     * 创建升级管理器
     * @param context 上下文
     * @param baseUrl API基础URL
     */
    public AppUpgradeManager(Context context, String baseUrl) {
        this.context = context;
        this.baseUrl = baseUrl;
        this.gson = new Gson();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.upgradeApi = new AppUpgradeApi(baseUrl);
    }
    
    /**
     * 设置升级回调
     * @param upgradeCallback 升级回调
     */
    public void setUpgradeCallback(UpgradeCallback upgradeCallback) {
        this.upgradeCallback = upgradeCallback;
    }
    
    /**
     * 获取当前升级回调
     * @return 当前设置的升级回调
     */
    public UpgradeCallback getUpgradeCallback() {
        return this.upgradeCallback;
    }
    
    /**
     * 检查应用更新
     * @param showToast 是否显示提示信息
     */
    public void checkUpgrade(boolean showToast) {
        Log.d(TAG, "Checking for upgrades...");
        
        try {
            // 获取当前应用版本信息
            PackageInfo packageInfo = getCurrentPackageInfo();
            if (packageInfo == null) {
                String errorMessage = "无法获取当前应用版本";
                Log.e(TAG, errorMessage);
                callUpgradeFailed(errorMessage);
                return;
            }
            
            final int versionCode = packageInfo.versionCode;
            // 使用完全限定类名引用BuildConfig
            final String clientType = com.thunder.erp.android.BuildConfig.CLIENT_TYPE;
            final boolean finalShowToast = showToast;
            
            Log.d(TAG, "Checking for upgrades with clientType: " + clientType + ", versionCode: " + versionCode);
            
            // 使用AppUpgradeApi检查更新
            upgradeApi.checkUpgrade(clientType, versionCode, new AppUpgradeApi.CheckUpgradeCallback() {
                @Override
                public void onSuccess(AppUpgradeInfo result) {
                    mainHandler.post(() -> {
                        // 处理升级信息
                        if (result != null) {
                            // 有新版本
                            upgradeInfo = result;
                            
                            if (finalShowToast) {
                                Toast.makeText(context, "发现新版本: " + result.getVersionName(), Toast.LENGTH_SHORT).show();
                            }
                            
                            callCheckResult(true, result);
                        } else {
                            // 没有新版本
                            if (finalShowToast) {
                                Toast.makeText(context, "已是最新版本", Toast.LENGTH_SHORT).show();
                            }
                            
                            callCheckResult(false, null);
                        }
                    });
                }
                
                @Override
                public void onFailure(String errorMessage) {
                    Log.e(TAG, "Check upgrade failed: " + errorMessage);
                    mainHandler.post(() -> {
                        if (finalShowToast) {
                            Toast.makeText(context, "检查更新失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                        }
                        callUpgradeFailed(errorMessage);
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Check upgrade exception", e);
            if (showToast) {
                Toast.makeText(context, "检查更新异常: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
            callUpgradeFailed("检查更新异常: " + e.getMessage());
        }
    }
    
    /**
     * 开始应用升级流程
     * 包括确认下载、下载APK、确认安装等完整流程
     */
    public void startUpgradeProcess() {
        if (upgradeInfo == null) {
            Log.e(TAG, "No upgrade info available");
            callUpgradeFailed("没有可用的升级信息");
            return;
        }
        
        if (isDownloading.get()) {
            Log.d(TAG, "Download already in progress");
            Toast.makeText(context, "下载已在进行中", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 通知升级开始
        callUpgradeStarted();
        
        // 显示确认对话框
        Activity activity = getActivity();
        if (activity == null) {
            callUpgradeFailed("无法显示确认对话框");
            return;
        }
        
        activity.runOnUiThread(() -> {
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            builder.setTitle("发现新版本")
                   .setMessage("版本：" + upgradeInfo.getVersionName() + "\n\n" +
                               "大小：" + formatFileSize(upgradeInfo.getApkSize()) + "\n\n" +
                               "更新内容：\n" + upgradeInfo.getDescription())
                   .setPositiveButton("立即下载", (dialog, which) -> {
                       dialog.dismiss();
                       startDownload();
                   })
                   .setNegativeButton("稍后再说", (dialog, which) -> {
                       dialog.dismiss();
                       callUpgradeFailed("用户取消升级");
                   })
                   .setCancelable(false)
                   .show();
        });
    }
    
    /**
     * 开始下载APK
     */
    private void startDownload() {
        if (upgradeInfo == null || TextUtils.isEmpty(upgradeInfo.getDownloadUrl())) {
            Log.e(TAG, "Invalid download URL");
            callUpgradeFailed("无效的下载地址");
            return;
        }
        
        // 如果已经在下载则不重复下载
        if (isDownloading.get()) {
            Log.d(TAG, "Download already in progress");
            Toast.makeText(context, "下载已在进行中", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 显示下载进度对话框
        showDownloadProgressDialog();
        
        // 开始下载任务
        downloadTask = new DownloadTask();
        downloadTask.execute(upgradeInfo.getDownloadUrl());
    }
    
    /**
     * 显示下载进度对话框
     */
    private void showDownloadProgressDialog() {
        Activity activity = getActivity();
        if (activity == null) return;
        
        activity.runOnUiThread(() -> {
            progressDialog = new ProgressDialog(activity);
            progressDialog.setTitle("正在下载");
            progressDialog.setMessage("正在下载新版本，请稍候...");
            progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            progressDialog.setMax(100);
            progressDialog.setCancelable(false);
            progressDialog.setButton(
                    AlertDialog.BUTTON_NEGATIVE,
                    "取消",
                    (dialog, which) -> {
                        cancelDownload();
                        callUpgradeFailed("用户取消下载");
                    }
            );
            progressDialog.show();
        });
    }
    
    /**
     * 取消下载
     */
    public void cancelDownload() {
        if (downloadTask != null && !downloadTask.isCancelled()) {
            downloadTask.cancel(true);
            isDownloading.set(false);
        }
        
        dismissProgressDialog();
    }
    
    /**
     * 关闭进度对话框
     */
    private void dismissProgressDialog() {
        Activity activity = getActivity();
        if (activity == null) return;
        
        activity.runOnUiThread(() -> {
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.dismiss();
                progressDialog = null;
            }
        });
    }
    
    /**
     * 下载完成后显示安装确认对话框
     * @param apkFile 下载好的APK文件
     */
    private void showInstallConfirmDialog(final File apkFile) {
        Activity activity = getActivity();
        if (activity == null) return;
        
        activity.runOnUiThread(() -> {
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            builder.setTitle("下载完成")
                   .setMessage("新版本已下载完成，是否立即安装？")
                   .setPositiveButton("立即安装", (dialog, which) -> {
                       dialog.dismiss();
                       installApk(apkFile);
                   })
                   .setNegativeButton("稍后安装", (dialog, which) -> {
                       dialog.dismiss();
                       callUpgradeFailed("用户延迟安装");
                   })
                   .setCancelable(false)
                   .show();
        });
    }
    
    /**
     * 安装APK文件
     * @param apkFile APK文件
     */
    public void installApk(File apkFile) {
        if (apkFile == null || !apkFile.exists()) {
            Log.e(TAG, "APK file does not exist");
            callUpgradeFailed("安装包不存在");
            return;
        }
        
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            Uri apkUri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0及以上需要使用FileProvider
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                String authority = context.getPackageName() + ".fileprovider";
                apkUri = FileProvider.getUriForFile(context, authority, apkFile);
            } else {
                apkUri = Uri.fromFile(apkFile);
            }
            
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
            
            context.startActivity(intent);
            callUpgradeComplete();
        } catch (Exception e) {
            Log.e(TAG, "Failed to install APK", e);
            callUpgradeFailed("安装失败: " + e.getMessage());
            
            // 如果是Android 8.0及以上版本，可能需要引导用户启用未知来源应用安装权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Toast.makeText(context, "请在设置中允许安装未知来源应用", Toast.LENGTH_LONG).show();
                Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                try {
                    context.startActivity(intent);
                } catch (Exception ex) {
                    Log.e(TAG, "Failed to open settings", ex);
                }
            }
        }
    }
    
    /**
     * 获取当前Activity
     * @return 当前Activity，如果无法获取则返回null
     */
    private Activity getActivity() {
        if (context instanceof Activity) {
            return (Activity) context;
        }
        Log.e(TAG, "Context is not an Activity");
        return null;
    }
    
    /**
     * 格式化文件大小
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    private String formatFileSize(long size) {
        if (size <= 0) return "未知大小";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        digitGroups = Math.min(digitGroups, units.length - 1);
        double value = size / Math.pow(1024, digitGroups);
        
        return String.format("%.2f %s", value, units[digitGroups]);
    }
    
    /**
     * 获取下载文件保存路径
     * @return 下载文件目录
     */
    private File getDownloadDir() {
        File downloadDir;
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            downloadDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "upgrade");
        } else {
            downloadDir = new File(context.getFilesDir(), "upgrade");
        }
        
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }
        
        return downloadDir;
    }
    
    /**
     * 获取应用当前版本信息
     * @return 当前应用版本信息
     */
    private PackageInfo getCurrentPackageInfo() {
        try {
            return context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Failed to get package info", e);
            return null;
        }
    }
    
    /**
     * 调用升级检查结果回调
     * @param hasUpdate 是否有更新
     * @param upgradeInfo 升级信息
     */
    private void callCheckResult(boolean hasUpdate, AppUpgradeInfo upgradeInfo) {
        if (upgradeCallback != null) {
            mainHandler.post(() -> upgradeCallback.onCheckResult(hasUpdate, upgradeInfo));
        }
    }
    
    /**
     * 调用升级开始回调
     */
    private void callUpgradeStarted() {
        if (upgradeCallback != null) {
            mainHandler.post(() -> upgradeCallback.onUpgradeStarted());
        }
    }
    
    /**
     * 调用升级完成回调
     */
    private void callUpgradeComplete() {
        if (upgradeCallback != null) {
            mainHandler.post(() -> upgradeCallback.onUpgradeComplete());
        }
    }
    
    /**
     * 调用升级失败回调
     * @param errorMessage 错误信息
     */
    private void callUpgradeFailed(String errorMessage) {
        if (upgradeCallback != null) {
            mainHandler.post(() -> upgradeCallback.onUpgradeFailed(errorMessage));
        }
    }
    
    /**
     * 下载任务
     */
    private class DownloadTask extends AsyncTask<String, Integer, File> {
        private long totalBytes = 0;
        private long downloadedBytes = 0;
        private String errorMessage;
        
        @Override
        protected void onPreExecute() {
            isDownloading.set(true);
        }
        
        @Override
        protected File doInBackground(String... urls) {
            InputStream input = null;
            FileOutputStream output = null;
            HttpURLConnection connection = null;
            
            try {
                String downloadUrl = urls[0];
                if (TextUtils.isEmpty(downloadUrl)) {
                    errorMessage = "下载地址为空";
                    return null;
                }
                
                // 创建临时文件
                String fileName = URLUtil.guessFileName(downloadUrl, null, null);
                if (TextUtils.isEmpty(fileName)) {
                    fileName = "app_update_" + System.currentTimeMillis() + ".apk";
                }
                
                // 获取下载目录
                File outputDir = getDownloadDir();
                File outputFile = new File(outputDir, fileName);
                
                // 如果文件已存在且大小与升级信息中的一致，直接返回
                if (outputFile.exists() && upgradeInfo != null && outputFile.length() == upgradeInfo.getApkSize()) {
                    Log.d(TAG, "File already exists and size matches: " + outputFile.getAbsolutePath());
                    downloadedBytes = outputFile.length();
                    totalBytes = downloadedBytes;
                    downloadedApkFile = outputFile;
                    return outputFile;
                }
                
                // 打开连接
                URL url = new URL(downloadUrl);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(CONNECTION_TIMEOUT);
                connection.setReadTimeout(READ_TIMEOUT);
                connection.connect();
                
                // 检查响应状态
                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    errorMessage = "服务器响应错误: " + responseCode;
                    return null;
                }
                
                // 获取文件总大小
                totalBytes = connection.getContentLength();
                
                // 创建输入流
                input = connection.getInputStream();
                
                // 创建输出流
                output = new FileOutputStream(outputFile);
                
                // 下载文件
                byte[] buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                downloadedBytes = 0;
                
                while ((bytesRead = input.read(buffer)) != -1) {
                    // 检查是否取消下载
                    if (isCancelled()) {
                        errorMessage = "下载已取消";
                        outputFile.delete();
                        return null;
                    }
                    
                    // 写入文件
                    output.write(buffer, 0, bytesRead);
                    
                    // 更新进度
                    downloadedBytes += bytesRead;
                    int progress = totalBytes > 0 ? (int) ((downloadedBytes * 100) / totalBytes) : 0;
                    publishProgress(progress);
                }
                
                // 刷新并关闭输出流
                output.flush();
                
                downloadedApkFile = outputFile;
                return outputFile;
            } catch (IOException e) {
                Log.e(TAG, "Download failed", e);
                errorMessage = "下载失败: " + e.getMessage();
                return null;
            } finally {
                // 关闭连接和流
                try {
                    if (output != null) output.close();
                    if (input != null) input.close();
                    if (connection != null) connection.disconnect();
                } catch (IOException e) {
                    Log.e(TAG, "Error closing streams", e);
                }
                
                isDownloading.set(false);
            }
        }
        
        @Override
        protected void onProgressUpdate(Integer... values) {
            int progress = values[0];
            
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.setProgress(progress);
                progressDialog.setMessage(
                        String.format("正在下载: %d%% (%s / %s)",
                                progress,
                                formatFileSize(downloadedBytes),
                                formatFileSize(totalBytes))
                );
            }
        }
        
        @Override
        protected void onPostExecute(File result) {
            isDownloading.set(false);
            dismissProgressDialog();
            
            if (result != null && result.exists()) {
                Log.d(TAG, "Download completed: " + result.getAbsolutePath());
                showInstallConfirmDialog(result);
            } else {
                Log.e(TAG, "Download failed: " + (errorMessage != null ? errorMessage : "未知错误"));
                callUpgradeFailed(errorMessage != null ? errorMessage : "下载失败");
                
                // 显示错误信息
                Activity activity = getActivity();
                if (activity != null) {
                    activity.runOnUiThread(() -> 
                        Toast.makeText(context, 
                                errorMessage != null ? errorMessage : "下载失败", 
                                Toast.LENGTH_SHORT).show()
                    );
                }
            }
        }
        
        @Override
        protected void onCancelled() {
            isDownloading.set(false);
            dismissProgressDialog();
        }
    }
} 