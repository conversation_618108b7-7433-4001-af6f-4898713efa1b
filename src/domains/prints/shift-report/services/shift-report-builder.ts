import { Receipt } from '../../shared/models/receipt-model';
import { TextAlignment, FontSize } from '../../shared/models/receipt-model';
import { ReceiptFactory } from '../../shared/utils/receipt-factory';
import { ShiftReportData } from '../models/shift-report-data';
import { formatDateTime } from '@/utils/dateUtils';
import { formatYuan } from '@/utils/priceUtils';

/**
 * 交班单构建器
 * 负责将交班单数据转换为结构化的票据表示
 */
export class ShiftReportBuilder {
  /**
   * 构建交班单票据
   * @param data 交班单数据实体
   */
  build(data: ShiftReportData, venueId: string): Receipt {
    const receipt: Receipt = { elements: [] };
    const elements = receipt.elements;

    // 标题
    elements.push(
      ReceiptFactory.text('交班单', {
        align: TextAlignment.CENTER,
        bold: true,
        fontSize: FontSize.DOUBLE,
      })
    );
    elements.push(
      ReceiptFactory.text('\n', {
        fontSize: FontSize.NORMAL,
        bold: false,
      })
    );

    // 基本信息
    elements.push(
      ReceiptFactory.text(`打单时间: ${formatDateTime(data.shiftTime)}`, {
        align: TextAlignment.LEFT,
      })
    );
    elements.push(
      ReceiptFactory.text(`交班时间: ${formatDateTime(data.shiftTime)}`)
    );
    elements.push(
      ReceiptFactory.text(`交班员工: ${data.employee || '--'}`)
    );
    elements.push(
      ReceiptFactory.text(`已结账单数: ${data.orderCount ?? '--'}`)
    );

    elements.push(ReceiptFactory.line());

    // 营业数据
    if (data.businessData) {
      elements.push(
        ReceiptFactory.text('营业数据', {
          bold: true,
        })
      );
      elements.push(
        ReceiptFactory.tableRow([
          { text: '项目', width: 24 },
          { text: '金额', width: 24, align: TextAlignment.RIGHT },
        ])
      );
      elements.push(
        ReceiptFactory.tableRow([
          { text: '营业应收', width: 24 },
          { text: formatYuan(data.businessData.receivable), width: 24, align: TextAlignment.RIGHT },
        ])
      );
      // 更多业务数据可以根据需要从 data.businessData 添加
      elements.push(ReceiptFactory.line());
    }

    // 支付方式
    if (data.paymentMethods?.length) {
      elements.push(
        ReceiptFactory.text('支付方式', {
          bold: true,
        })
      );
      elements.push(
        ReceiptFactory.tableRow([
          { text: '方式', width: 24 },
          { text: '金额', width: 24, align: TextAlignment.RIGHT },
        ])
      );
      const paymentRows = data.paymentMethods.map(p => ([
        { text: p.method, width: 24 },
        { text: formatYuan(p.amount), width: 24, align: TextAlignment.RIGHT },
      ]));
      elements.push(ReceiptFactory.table(paymentRows));
      elements.push(ReceiptFactory.line());
    }

    // 底部信息
    elements.push(ReceiptFactory.emptyLine());
    elements.push(
      ReceiptFactory.text(`No.${venueId}`, {
        align: TextAlignment.CENTER,
      })
    );

    return receipt;
  }
}

// 导出单例
export const shiftReportBuilder = new ShiftReportBuilder(); 