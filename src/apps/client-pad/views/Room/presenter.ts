import { reactive, computed, ref, onMounted, onActivated, onBeforeUnmount, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { toast } from '@/components/customer/toast';
import DialogManager from '@/utils/dialog';

import { IRealTimeTableViewModel, IRealTimeTableState, IRealTimeTableComputed, IRealTimeTableActions, IRealTimeTableHelpers } from './viewmodel';
import { RealTimeTableConverter } from './converter';
import { useRealTimeTableInteractor } from './interactor';
import { RoomVO } from '@/types/projectobj';
import { useStageStore } from '@/stores/stageStore';
import { useTimeStore } from '@/stores/timeStore';
import { TimeoutType } from '@/application/vodService';
import type { CancelTableForm } from '@/modules/order/types';
import { ExtendedStageVO } from '@/modules/room/types/extendedStageVO';
import { SESSION_PAY_STATUS, SESSION_TAGS, getCombinedStatusName, ROOM_STATUS, getStatusPriority } from '@/modules/room/constants/stageStatus';
import { formatUnixTimestampToSimple, now10 } from '@/utils/dateUtils';

export class RealTimeTablePresenter implements IRealTimeTableViewModel {
  private router = useRouter();
  private interactor = useRealTimeTableInteractor();
  private stageStore = useStageStore();
  private timeStore = useTimeStore();
  private roomGridRef: any = null;

  // 根据排序类型对房间列表进行排序
  private sortStages(stages: ExtendedStageVO[], sortType: string): ExtendedStageVO[] {
    console.log(`[RealTimeTable] 执行排序，类型: ${sortType}, 数据数量: ${stages.length}`);

    // 防御性复制
    const result = [...stages];

    switch (sortType) {
      case 'name':
        // 按房间名称排序
        return result.sort((a, b) => {
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB);
        });

      case 'status':
        // 按房间状态优先级排序
        return result.sort((a, b) => {
          // 首先按照使用中、空闲中等状态排序
          const priorityA = getStatusPriority(a);
          const priorityB = getStatusPriority(b);
          if (priorityA !== priorityB) {
            return priorityA - priorityB;
          }

          // 状态相同时，按房间名称排序
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB);
        });

      case 'area':
        // 按区域名称排序
        return result.sort((a, b) => {
          const areaA = a.areaVO?.name || '';
          const areaB = b.areaVO?.name || '';

          if (areaA !== areaB) {
            return areaA.localeCompare(areaB);
          }

          // 区域相同时按房间名称排序
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB);
        });

      case 'consumption':
        // 按消费金额排序（从高到低）- 与RealTimeTable保持一致
        return result.sort((a, b) => {
          // 获取原始值
          const rawA = a.sessionVO?.totalFee;
          const rawB = b.sessionVO?.totalFee;

          // 安全的数值转换，处理null、undefined、字符串等情况
          const numA = rawA == null ? 0 : typeof rawA === 'string' ? parseFloat(rawA) || 0 : Number(rawA) || 0;
          const numB = rawB == null ? 0 : typeof rawB === 'string' ? parseFloat(rawB) || 0 : Number(rawB) || 0;

          // 从高到低排序
          return numB - numA;
        });

      case 'openTime':
        // 按开台时间排序（从新到旧）
        return result.sort((a, b) => {
          const timeA = a.sessionVO?.startTime || 0;
          const timeB = b.sessionVO?.startTime || 0;

          // 降序排列（新开台在前）
          if (timeB !== timeA) {
            return timeB - timeA;
          }

          // 时间相同时按房间名称排序
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB);
        });

      case 'closeTime':
        // 按关台时间排序 - 与RealTimeTable保持一致
        return result.sort((a, b) => {
          const timeA = a.sessionVO?.endTime || 0;
          const timeB = b.sessionVO?.endTime || 0;
          return timeB - timeA;
        });

      case 'type':
        // 按房间类型排序
        return result.sort((a, b) => {
          const typeA = a.roomTypeVO?.name || '';
          const typeB = b.roomTypeVO?.name || '';

          if (typeA !== typeB) {
            return typeA.localeCompare(typeB);
          }

          // 类型相同时按房间名称排序
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB);
        });

      default:
        // 默认排序 - 直接返回原始数据
        return result;
    }
  }

  // 状态
  public state: IRealTimeTableState = reactive({
    loading: false,
    stages: [],
    currentStageId: null,
    searchText: '',
    sortType: 'name',
    filters: [],
    displayMode: 'grid',
    refreshTimers: [], // 确保初始化为空数组
    // 新增状态
    tempFilters: [], // 临时筛选条件
    filterPopoverVisible: false, // 筛选弹窗可见状态
    selectedButton: 'grid', // 当前选中的视图模式按钮
    retryCount: 0, // 新增重试次数
    activeTab: 'inUse' // 默认选中"使用中"标签
  });

  // 计算属性
  public computed: IRealTimeTableComputed = {
    stages: computed(() => {
      const stages = this.stageStore.getStages();
      // 返回已处理好的ExtendedStageVO类型数据
      return stages as ExtendedStageVO[];
    }),
    currentStage: computed(() => {
      const stage = this.stageStore.getCurrentStage;
      console.log('currentStage:', stage);
      if (!stage) return null;
      // 返回已处理好的ExtendedStageVO类型数据
      return stage as ExtendedStageVO;
    }),
    // 添加状态计数属性
    statusCounts: computed(() => {
      const stages = this.computed.stages.value;
      return {
        idle: stages.filter(stage => stage.roomStatus === ROOM_STATUS.IDLE).length,
        inUse: stages.filter(stage => stage.roomStatus === ROOM_STATUS.IN_USE).length,
        timeout: stages.filter(stage => stage.tags?.includes(SESSION_TAGS.TIMEOUT)).length,
        fault: stages.filter(stage => stage.roomStatus === ROOM_STATUS.FAULT).length,
        cleaning: stages.filter(stage => stage.roomStatus === ROOM_STATUS.CLEANING).length,
        withGuest: stages.filter(stage => stage.roomStatus === ROOM_STATUS.WITH_GUEST).length
      };
    }),

    filteredStages: computed(() => {
      let result = [...this.computed.stages.value];

      // 根据activeTab筛选 - 最高优先级筛选
      if (this.state.activeTab === 'inUse') {
        // 筛选使用中的房间（包括超时状态）
        result = result.filter(stage => {
          // 详细检查roomStatus是否为IN_USE
          const isInUse = stage.roomStatus === ROOM_STATUS.IN_USE;
          // 详细检查是否有超时标签
          const hasTimeoutTag = stage.tags && Array.isArray(stage.tags) && stage.tags.includes(SESSION_TAGS.TIMEOUT);

          return isInUse || hasTimeoutTag;
        });

        // 打印"使用中"标签下筛选后的结果数量
        console.log(`[Tab筛选] "使用中"标签下筛选后的包厢数量: ${result.length}`);
      }

      // 根据搜索文本筛选
      if (this.state.searchText) {
        const searchText = this.state.searchText.toLowerCase();
        result = result.filter(stage => {
          return (
            (stage.roomVO?.name && stage.roomVO.name.toLowerCase().includes(searchText)) ||
            (stage.roomTypeVO?.name && stage.roomTypeVO.name.toLowerCase().includes(searchText)) ||
            (stage.areaVO?.name && stage.areaVO.name.toLowerCase().includes(searchText))
          );
        });
      }

      // 筛选条件过滤
      if (this.state.filters.length > 0) {
        result = result.filter(stage => {
          // 筛选逻辑保持与RealTimeTable相同
          return this.state.filters.some(filter => {
            // 区域筛选
            if (filter.startsWith('area_') && stage.areaVO) {
              return filter === `area_${stage.areaVO.id}`;
            }

            // 状态筛选
            if (filter.startsWith('status_')) {
              const statusValue = filter.replace('status_', '');
              if (statusValue === 'idle' && stage.roomStatus === ROOM_STATUS.IDLE) {
                return true;
              }
              if (statusValue === 'in_use' && stage.roomStatus === ROOM_STATUS.IN_USE) {
                return true;
              }
              if (statusValue === 'with_guest' && stage.roomStatus === ROOM_STATUS.WITH_GUEST) {
                return true;
              }
              if (statusValue === 'fault' && stage.roomStatus === ROOM_STATUS.FAULT) {
                return true;
              }
              if (statusValue === 'cleaning' && stage.roomStatus === ROOM_STATUS.CLEANING) {
                return true;
              }
              if (statusValue === 'timeout' && stage.tags?.includes(SESSION_TAGS.TIMEOUT)) {
                return true;
              }
            }

            // 支付状态筛选
            if (filter.startsWith('pay_')) {
              const payValue = filter.replace('pay_', '');
              if (payValue === 'paid' && stage.payStatus === SESSION_PAY_STATUS.PAID) {
                return true;
              }
              if (payValue === 'unpaid' && stage.payStatus === SESSION_PAY_STATUS.UNPAID) {
                return true;
              }
            }

            return false;
          });
        });
      }

      // 根据排序类型对结果进行排序
      if (this.state.sortType && this.state.sortType !== 'default') {
        console.log(`[RealTimeTable] 正在应用排序: ${this.state.sortType}`);
        result = this.sortStages(result, this.state.sortType);
      }

      return result;
    }),
    // 新增: 当前排序标签
    currentSortLabel: computed(() => {
      const sortOptions = [
        { label: '默认排序', value: 'default' },
        { label: '状态排序', value: 'status' },
        { label: '类型排序', value: 'type' },
        { label: '区域排序', value: 'area' },
        { label: '消费排序', value: 'consumption' },
        { label: '名称排序', value: 'name' },
        { label: '开台时间', value: 'openTime' },
        { label: '关台时间', value: 'closeTime' }
      ];
      return sortOptions.find(option => option.value === this.state.sortType)?.label || '排序方式';
    }),
    // 新增: 筛选选项组
    filterGroups: computed(() => {
      // 从stages中提取所有区域
      const areas = this.computed.stages.value.filter(stage => stage.areaVO).map(stage => ({ id: stage.areaVO!.id, name: stage.areaVO!.name }));

      // 按区域名称合并，同名区域仅保留一个
      const uniqueAreas: { id: string; name: string }[] = [];
      const areaNames = new Set<string>();

      areas.forEach(area => {
        if (!areaNames.has(area.name)) {
          areaNames.add(area.name);
          uniqueAreas.push(area);
        }
      });

      // 按名称排序
      uniqueAreas.sort((a, b) => a.name.localeCompare(b.name));

      return [
        {
          name: '区域',
          options: uniqueAreas.map(area => ({
            label: area.name,
            value: `area_${area.id}`
          }))
        },
        {
          name: '包厢状态',
          options: [
            { label: '空闲中', value: 'status_idle' },
            { label: '使用中', value: 'status_in_use' },
            { label: '带客中', value: 'status_with_guest' },
            { label: '超时', value: 'status_timeout' },
            { label: '故障中', value: 'status_fault' },
            { label: '清洁中', value: 'status_cleaning' }
          ]
        },
        {
          name: '支付状态',
          options: [
            { label: '未结算', value: 'pay_unpaid' },
            { label: '已结算', value: 'pay_paid' }
          ]
        }
      ];
    })
  };

  // 辅助方法
  public helpers: IRealTimeTableHelpers = {
    processStageData: (rawData: any[]) => {
      return RealTimeTableConverter.processStageData(rawData);
    }
  };

  // 动作
  public sortOptions = [
    { label: '默认排序', value: 'default' },
    { label: '名称排序', value: 'name' },
    { label: '状态排序', value: 'status' },
    { label: '消费排序', value: 'consumption' },
    { label: '开台时间', value: 'openTime' },
    { label: '关台时间', value: 'closeTime' }
  ];

  public actions: IRealTimeTableActions = {
    // 手动刷新数据
    manualRefresh: async () => {
      console.log('[RealTimeTable] 手动刷新数据');
      // 重置重试计数
      this.state.retryCount = 0;
      await this.actions.fetchStages();
      ElMessage.success('数据已刷新');
    },

    // 处理排序选择
    handleSortCommand: (command: string) => {
      console.log('[RealTimeTable] 排序方式变更:', command);
      this.state.sortType = command;
    },

    // NATS重连
    handleNatsReconnect: async () => {
      try {
        console.log('尝试重新连接NATS服务器...');
        const NatsListenerService = (await import('@/application/natsListenerService')).default;
        const success = await NatsListenerService.triggerReconnect();
        if (success) {
          console.log('NATS服务重连成功');
          ElMessage.success('NATS连接已恢复');
        } else {
          console.log('NATS服务重连失败');
          ElMessage.error('NATS连接失败，请稍后再试');
        }
      } catch (error) {
        console.error('NATS重连出错:', error);
        ElMessage.error('NATS连接出错');
      }
    },

    // 数据加载
    fetchStages: async () => {
      this.state.loading = true;
      try {
        console.log('[RealTimeTable] 开始获取房间数据');
        const data = await this.interactor.fetchStages();

        // 直接处理为ExtendedStageVO类型
        const processedData = this.helpers.processStageData(data);
        // 设置到store中
        this.stageStore.setStages(processedData as any);

        this.actions.setupRoomTimers();
        console.log('[RealTimeTable] 房间数据获取成功');
      } catch (error) {
        console.error('[RealTimeTable] 获取房间数据失败:', error);
        // 添加错误重试逻辑
        if (!this.state.retryCount || this.state.retryCount < 3) {
          this.state.retryCount = (this.state.retryCount || 0) + 1;
          console.log(`[RealTimeTable] 尝试重新获取数据，第${this.state.retryCount}次重试`);
          setTimeout(() => {
            this.actions.fetchStages();
          }, 1000);
        } else {
          this.state.retryCount = 0;
          ElMessage.error('获取房间数据失败，请手动刷新页面');
        }
      } finally {
        this.state.loading = false;
      }
    },

    refreshRoomData: () => {
      this.stageStore.updateStageCombinedStatus();
    },

    refreshCurrentStage: (stage: ExtendedStageVO) => {
      this.stageStore.updateStageInfo(stage);
    },

    setupRoomTimers: () => {
      this.actions.clearAllTimers();
      const currentStages = this.stageStore.getStages();
      if (currentStages) {
        currentStages.forEach(stage => {
          if (stage.sessionVO?.endTime) {
            const closeTime = Number(stage.sessionVO.endTime) * 1000;
            const now = this.timeStore.getCorrectedTime().getTime();

            if (closeTime > now) {
              const delay = closeTime - now + 30 * 60;
              const timer = window.setTimeout(() => {
                this.actions.fetchStages();
              }, delay);
              this.state.refreshTimers.push(timer);
            }
          }
        });
      }
    },

    clearAllTimers: () => {
      console.log('[RealTimeTable] 清理所有定时器', this.state.refreshTimers?.length || 0);
      if (this.state.refreshTimers && Array.isArray(this.state.refreshTimers)) {
        this.state.refreshTimers.forEach(timer => {
          console.log('[RealTimeTable] 清理定时器:', timer);
          clearTimeout(timer);
          clearInterval(timer);
        });
        this.state.refreshTimers = [];
      }
    },

    // 房间操作
    handleStageSelect: (stage: ExtendedStageVO) => {
      if (!stage?.roomVO?.id) {
        ElMessage.warning('无效的包厢信息');
        return;
      }

      this.stageStore.setCurrentStage(stage as any);
    },

    handleSelectedRoomUpdate: (newRoom: RoomVO) => {
      if (this.computed.currentStage.value) {
        // 通过 store 更新当前 stage
        const updatedStage = { ...this.computed.currentStage.value, roomVO: newRoom } as any;
        this.stageStore.setCurrentStage(updatedStage);
        // 同时更新 stages 中的对应数据
        this.stageStore.updateStageInfo(updatedStage);
      }
    },

    // 筛选、排序与视图切换
    handleSearch: (value: string) => {
      this.state.searchText = value;
    },

    handleFilterCommand: (command: string) => {
      const index = this.state.tempFilters.indexOf(command);
      if (index === -1) {
        // 如果是区域或类型筛选，需要先清除其他同类筛选
        if (command.startsWith('area_') || command.startsWith('type_')) {
          this.state.tempFilters = this.state.tempFilters.filter(f => !f.startsWith(command.split('_')[0] + '_'));
        }
        this.state.tempFilters.push(command);
      } else {
        this.state.tempFilters.splice(index, 1);
      }
    },

    clearFilters: () => {
      this.state.tempFilters = [];
      this.state.filters = [];
      this.state.filterPopoverVisible = false;
    },

    applyFilters: () => {
      this.state.filters = [...this.state.tempFilters];
      this.state.filterPopoverVisible = false;
    },

    toggleDisplayMode: (mode: string) => {
      this.state.selectedButton = mode;
      this.state.displayMode = mode === 'grid' ? 'grid' : 'list';
    },

    // Tab切换
    handleTabChange: (tab: string) => {
      this.state.activeTab = tab;

      // 直接将过滤后的数据设置到stageStore中，这样RoomGrid就能拿到正确的数据
      if (tab === 'inUse') {
        // 过滤出使用中的包厢
        const inUseStages = this.computed.stages.value.filter(stage => {
          return stage.roomStatus === ROOM_STATUS.IN_USE || (stage.tags && Array.isArray(stage.tags) && stage.tags.includes(SESSION_TAGS.TIMEOUT));
        });

        // 临时保存原始stages
        const originalStages = [...this.computed.stages.value];

        // 设置过滤后的stages到store中
        this.stageStore.setStages(inUseStages as any);

        // 打印更新信息
        console.log(`[Tab切换] 切换到"使用中"，设置${inUseStages.length}个包厢到store`);

        // 强制更新RoomGrid
        if (this.roomGridRef && this.roomGridRef.value) {
          this.roomGridRef.value.$forceUpdate();
        }

        // 1秒后恢复原始数据，避免持久改变store状态
        setTimeout(() => {
          if (this.state.activeTab === 'inUse') {
            console.log('[Tab恢复] 保持使用中筛选状态');
          } else {
            this.stageStore.setStages(originalStages as any);
            console.log('[Tab恢复] 恢复全部包厢数据');
          }
        }, 1000);
      } else {
        // 全部tab，加载所有数据
        this.actions.fetchStages();
      }
    },

    // 用户头像点击
    handleAvatarClick: () => {
      console.log('用户头像点击');
      this.router.push('/user/profile');
    },

    // 用户命令处理
    handleUserCommand: (command: string) => {
      if (command === 'profile') {
        this.router.push('/user/profile');
      } else if (command === 'logout') {
        ElMessageBox.confirm('确定要退出登录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await this.interactor.logout();
              this.router.push('/login');
            } catch (error) {
              console.error('退出登录失败:', error);
              ElMessage.error('退出登录失败');
            }
          })
          .catch(() => {
            // 取消操作
          });
      }
    }
  };

  // 组件属性
  private props: {
    searchText?: string;
    sortType?: string;
    filters?: string[];
    showOperations?: boolean;
  } = {};

  constructor(props?: any) {
    // 已经在类声明时初始化了state和refreshTimers数组，无需在这里重复
    if (props) {
      if (props.searchText !== undefined) {
        this.state.searchText = props.searchText;
      }
      if (props.sortType !== undefined) {
        this.state.sortType = props.sortType;
      }
      if (props.filters !== undefined) {
        this.state.filters = props.filters;
      }
      if (props.displayMode !== undefined) {
        this.state.displayMode = props.displayMode;
      }
      if (props.showOperations !== undefined) {
        this.props.showOperations = props.showOperations;
      }
    }

    this.setupLifecycles();
  }

  // 添加更新props的方法
  public setProps(props: any) {
    if (props.searchText !== undefined) {
      this.state.searchText = props.searchText;
    }
    if (props.sortType !== undefined) {
      this.state.sortType = props.sortType;
    }
    if (props.filters !== undefined) {
      this.state.filters = props.filters;
    }
    if (props.displayMode !== undefined) {
      this.state.displayMode = props.displayMode;
    }
    if (props.showOperations !== undefined) {
      this.props.showOperations = props.showOperations;
    }
  }

  // 设置RoomGrid引用
  setRoomGridRef(ref: any) {
    this.roomGridRef = ref;
  }

  private setupLifecycles() {
    // 监听路由变化
    watch(
      () => this.router.currentRoute.value.fullPath,
      (newPath, oldPath) => {
        if (newPath.includes('/room/realtimetable') && newPath !== oldPath) {
          console.log('[RealTimeTable] 路由变化，重新获取数据');
          this.actions.fetchStages();
        }
      }
    );

    // 组件激活时
    onActivated(() => {
      console.log('[RealTimeTable] 组件激活，开始获取数据');
      this.actions.fetchStages();
      this.actions.setupRoomTimers();
    });

    // 组件挂载时
    onMounted(() => {
      console.log('[RealTimeTable] 组件挂载，开始获取数据');
      this.actions.fetchStages();
      this.actions.setupRoomTimers();

      // 添加页面刷新事件监听
      window.addEventListener('pageshow', event => {
        // persisted属性表示页面是否从缓存中恢复
        if (event.persisted) {
          console.log('[RealTimeTable] 页面从缓存恢复，重新获取数据');
          this.actions.fetchStages();
        }
      });

      // 每10分钟自动刷新一次数据
      const refreshInterval = window.setInterval(
        () => {
          console.log('[RealTimeTable] 定时刷新数据');
          this.actions.fetchStages();
        },
        10 * 60 * 1000
      );

      // 保存定时器ID以便清理
      this.state.refreshTimers.push(refreshInterval);
    });

    // 组件卸载前
    onBeforeUnmount(() => {
      console.log('[RealTimeTable] 组件卸载，清理定时器');
      this.actions.clearAllTimers();

      // 移除事件监听器
      window.removeEventListener('pageshow', () => {});
    });
  }
}

export function useRealTimeTablePresenter(props?: any): IRealTimeTableViewModel {
  const presenter = new RealTimeTablePresenter(props);
  return presenter;
}
