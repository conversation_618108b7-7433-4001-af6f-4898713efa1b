import { createApp } from 'vue';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import router from './router';
import VConsole from 'vconsole';
import { EnvironmentUtils } from '@/utils/envUtils';

// 统一引入样式（使用SCSS替代CSS）
import '@/style/index.scss';
// Element Plus 相关
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 根组件
import App from './App.vue';

// 导入对话框管理器
import DialogManager from '@/utils/dialog';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

// 将版本信息挂载到 window 对象
declare global {
  interface Window {
    APP_VERSION: string;
    APP_RELEASE_TIMESTAMP: string;
    APP_GIT_BRANCH: string;
    APP_GIT_COMMIT_HASH: string;
    APP_APP_TYPE: string;
    BASE_URL: string;
    DialogManager: typeof DialogManager;
  }
}

// 设置基本URL，确保所有资源都能正确加载
window.BASE_URL = import.meta.env.BASE_URL || '/';

// 版本信息
window.APP_VERSION = import.meta.env.VITE_APP_VERSION;
window.APP_RELEASE_TIMESTAMP = import.meta.env.VITE_APP_RELEASE_TIMESTAMP;
window.APP_GIT_BRANCH = import.meta.env.VITE_APP_GIT_BRANCH;
window.APP_GIT_COMMIT_HASH = import.meta.env.VITE_APP_GIT_HASH;
window.APP_APP_TYPE = import.meta.env.VITE_APP_TYPE;

// 初始化应用
const initApp = () => {
  // 设置标题和图标
  document.title = import.meta.env.VITE_APP_TITLE || 'Thunder ERP Client Pad';

  // 初始化 VConsole（仅在开发环境或显式启用时）
  if (EnvironmentUtils.showVConsole()) {
    new VConsole({ theme: 'light' });
  }

  const app = createApp(App);

  // 创建 Pinia 实例并配置持久化插件
  const pinia = createPinia();
  pinia.use(piniaPluginPersistedstate);

  // 注册插件
  app.use(pinia);
  app.use(router);
  app.use(ElementPlus, {
    locale: zhCn
  });

  // 注册 Element Plus 图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
  }

  // 将对话框管理器挂载到全局
  app.config.globalProperties.$dialog = DialogManager;
  // 也可以挂载到 window 对象，方便调试
  (window as any).DialogManager = DialogManager;

  // 全局错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('[Client Pad] 全局错误:', err);
    console.error('[Client Pad] 错误信息:', info);
  };

  // 添加应用标识
  console.log('[Client Pad] 应用初始化完成，应用类型:', import.meta.env.VITE_APP_TYPE);
  console.log('[Client Pad] 基本路径:', window.BASE_URL);

  // 挂载应用
  app.mount('#app');
};

// 启动应用
initApp();
