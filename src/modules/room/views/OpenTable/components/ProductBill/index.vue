<template>
  <div class="p-4 bg-white rounded-[16px] mt-[16px]">
    <h4 class="text-base font-semibold mb-3 flex items-center justify-between">
      <span>商品账单</span>
      <el-button v-if="showAddButton" :icon="Plus" class="border-none" @click="handleAddProducts"> 添加商品 </el-button>
    </h4>

    <!-- 开台方案商品分组 -->
    <div v-if="initialOrderProducts.length > 0" class="mb-6">
      <div class="flex items-center mb-3">
        <div class="w-1 h-4 bg-[#FF650F] rounded mr-2"></div>
        <h5 class="text-sm font-medium text-[#FF650F]">开台方案商品</h5>
        <span class="text-xs text-gray-500 ml-2">({{ initialOrderMainProductsCount }}项)</span>
      </div>

      <div class="w-full overflow-x-auto rounded-md md:overflow-visible" style="-webkit-overflow-scrolling: touch">
        <el-table
          :data="initialOrderProducts"
          table-layout="fixed"
          class="w-full mb-[8px] min-w-[600px] md:min-w-0 table-no-padding initial-order-table"
          stripe
          :empty-text="'暂无商品'"
          :row-class-name="setRowClass"
          :span-method="objectSpanMethod">
          <!-- 商品名称列 -->
          <el-table-column label="商品名称" prop="productName" align="left" :show-overflow-tooltip="true">
            <template #default="scope">
              <ProductNameCell :row="scope.row" :tableData="props.tableData" @optional-group-edit="handleOptionalGroupEdit" />
            </template>
          </el-table-column>

          <!-- 数量列 -->
          <el-table-column label="数量" prop="quantity" align="center" class-name="non-detail-cell" width="64px">
            <template #default="scope">
              <QuantityCell :row="scope.row" />
            </template>
          </el-table-column>

          <!-- 单价列 -->
          <el-table-column label="单价" prop="price" align="right" class-name="non-detail-cell" min-width="92px">
            <template #default="scope">
              <PriceCell :row="scope.row" />
            </template>
          </el-table-column>

          <!-- 金额列 -->
          <el-table-column label="金额" prop="totalAmount" align="right" class-name="non-detail-cell" min-width="92px">
            <template #default="scope">
              <AmountCell :row="scope.row" />
            </template>
          </el-table-column>
        </el-table>

        <!-- 开台方案小计 -->
        <div class="text-right text-sm flex justify-end items-baseline mb-2">
          <span class="text-[#666]">开台方案小计：</span>
          <PriceDisplay :amountInFen="initialOrderSubtotal" />
        </div>
      </div>
    </div>

    <!-- 单独点单商品分组 -->
    <div v-if="additionalOrderProducts.length > 0">
      <div class="flex items-center mb-3">
        <div class="w-1 h-4 bg-[#2670de] rounded mr-2"></div>
        <h5 class="text-sm font-medium text-[#2670de]">单独点单商品</h5>
        <span class="text-xs text-gray-500 ml-2">({{ additionalOrderMainProductsCount }}项)</span>
      </div>

      <div class="w-full overflow-x-auto rounded-md md:overflow-visible" style="-webkit-overflow-scrolling: touch">
        <el-table
          :data="additionalOrderProducts"
          table-layout="fixed"
          class="w-full mb-[8px] min-w-[600px] md:min-w-0 table-no-padding additional-order-table"
          stripe
          :empty-text="'暂无商品'"
          :row-class-name="setRowClass"
          :span-method="objectSpanMethod">
          <!-- 商品名称列 -->
          <el-table-column label="商品名称" prop="productName" align="left" :show-overflow-tooltip="true">
            <template #default="scope">
              <ProductNameCell
                :row="scope.row"
                :showDelete="true"
                :tableData="props.tableData"
                @optional-group-edit="handleOptionalGroupEdit"
                @delete-product="handleDeleteProduct" />
            </template>
          </el-table-column>

          <!-- 数量列 -->
          <el-table-column label="数量" prop="quantity" align="center" class-name="non-detail-cell" width="64px">
            <template #default="scope">
              <QuantityCell :row="scope.row" />
            </template>
          </el-table-column>

          <!-- 单价列 -->
          <el-table-column label="单价" prop="price" align="right" class-name="non-detail-cell" min-width="92px">
            <template #default="scope">
              <PriceCell :row="scope.row" />
            </template>
          </el-table-column>

          <!-- 金额列 -->
          <el-table-column label="金额" prop="totalAmount" align="right" class-name="non-detail-cell" min-width="92px">
            <template #default="scope">
              <AmountCell :row="scope.row" />
            </template>
          </el-table-column>
        </el-table>

        <!-- 单独点单小计 -->
        <div class="text-right text-sm flex justify-end items-baseline mb-2">
          <span class="text-[#666]">单独点单小计：</span>
          <PriceDisplay :amountInFen="additionalOrderSubtotal" />
        </div>
      </div>
    </div>

    <!-- 暂无商品提示 -->
    <div v-if="initialOrderProducts.length === 0 && additionalOrderProducts.length === 0" class="py-8 text-center text-gray-400">暂无商品</div>

    <!-- 总计 -->
    <div v-if="initialOrderProducts.length > 0 || additionalOrderProducts.length > 0" class="text-right text-sm flex justify-end items-baseline border-t pt-2">
      <span class="text-[#666] font-medium">商品总计：</span>
      <PriceDisplay :amountInFen="subtotal" class="font-semibold" />
    </div>

    <!-- 可选组商品编辑对话框 -->
    <OptionalProductDialog
      v-model:visible="optionalGroupDialogVisible"
      :optionalGroup="currentOptionalGroup"
      @confirm="handleOptionalGroupConfirm"
      @close="handleOptionalGroupDialogClosed" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, defineComponent, h, type PropType } from 'vue';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, ElButton, ElIcon } from 'element-plus';
import { sumBy } from 'lodash';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import PackageDetailItem from '@/modules/room/components/PackageDetailItem.vue';
import OptionalProductDialog from '@/modules/room/components/OptionalProductDialog.vue';
import type { TableRowItem } from '../../billUtils';

// Props 定义
interface Props {
  tableData: TableRowItem[];
  showAddButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showAddButton: true
});

// Emits 定义
interface Emits {
  (e: 'add-products'): void;
  (e: 'optional-group-updated', data: any): void;
  (e: 'delete-product', product: TableRowItem): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const optionalGroupDialogVisible = ref(false);
const currentOptionalGroup = ref<any>(null);

// 计算属性
const subtotal = computed(() => {
  return sumBy(
    props.tableData.filter(item => !item.isFree && !item.isDetail),
    item => item.totalAmount || 0
  );
});

// 分组计算属性
const initialOrderProducts = computed(() => {
  const products: TableRowItem[] = [];
  const processedIndices = new Set<number>();

  // 遍历所有数据，找到开台方案商品及其明细
  props.tableData.forEach((item, index) => {
    if (processedIndices.has(index)) return; // 跳过已处理的项

    if (item.isInitialOrder === true) {
      // 添加主商品
      products.push(item);
      processedIndices.add(index);

      // 如果是套餐或可选组，查找其后续的明细行
      if (item.isPackage || item.isOptionalGroup) {
        // 查找紧跟在当前商品后面的明细行
        for (let i = index + 1; i < props.tableData.length; i++) {
          const nextItem = props.tableData[i];
          if (nextItem.isDetail && nextItem.parentId === item.id) {
            products.push(nextItem);
            processedIndices.add(i);
          } else if (!nextItem.isDetail) {
            // 遇到非明细行就停止查找
            break;
          }
        }
      }
    }
  });

  console.log('开台方案商品:', products);
  return products;
});

const additionalOrderProducts = computed(() => {
  const products: TableRowItem[] = [];
  const processedIndices = new Set<number>();

  // 遍历所有数据，找到单独点单商品及其明细
  props.tableData.forEach((item, index) => {
    if (processedIndices.has(index)) return; // 跳过已处理的项

    if (item.isInitialOrder === false) {
      // 添加主商品
      products.push(item);
      processedIndices.add(index);

      // 如果是套餐或可选组，查找其后续的明细行
      if (item.isPackage || item.isOptionalGroup) {
        // 查找紧跟在当前商品后面的明细行
        for (let i = index + 1; i < props.tableData.length; i++) {
          const nextItem = props.tableData[i];
          if (nextItem.isDetail && nextItem.parentId === item.id) {
            products.push(nextItem);
            processedIndices.add(i);
          } else if (!nextItem.isDetail) {
            // 遇到非明细行就停止查找
            break;
          }
        }
      }
    }
  });

  console.log('单独点单商品:', products);
  return products;
});

const initialOrderSubtotal = computed(() => {
  return sumBy(
    initialOrderProducts.value.filter(item => !item.isFree && !item.isDetail),
    item => item.totalAmount || 0
  );
});

const additionalOrderSubtotal = computed(() => {
  return sumBy(
    additionalOrderProducts.value.filter(item => !item.isFree && !item.isDetail),
    item => item.totalAmount || 0
  );
});

// 计算主商品数量（不包含明细行）
const initialOrderMainProductsCount = computed(() => {
  return initialOrderProducts.value.filter(item => !item.isDetail).length;
});

const additionalOrderMainProductsCount = computed(() => {
  return additionalOrderProducts.value.filter(item => !item.isDetail).length;
});

// 方法
const handleAddProducts = () => {
  emit('add-products');
};

const findParentItem = (parentId: string): any => {
  if (!parentId) {
    return undefined;
  }

  // 在表格数据中查找父项
  const parentItem = props.tableData.find(item => item.id === parentId);
  return parentItem;
};

const getSelectedQuantity = (row: TableRowItem): number => {
  // 非可选组商品，直接返回quantity
  if (!row.isOptionalGroup) {
    return row.quantity || 0;
  }

  // 可选组商品，如果有selectedQuantity字段则使用它
  if (row.selectedQuantity !== undefined) {
    return row.selectedQuantity;
  }

  // 否则计算已选商品总量
  if (row.optionalProducts && row.optionalProducts.length > 0) {
    return row.optionalProducts.reduce((sum: number, product: any) => sum + (product.quantity || 0), 0);
  }

  return 0;
};

const setRowClass = ({ row }: { row: any }): string => {
  if (row.isDetail) {
    return 'detail-row-class';
  }
  if (row.isPackage) {
    return 'is-package-row';
  }
  if (row.isOptionalGroup) {
    return 'is-optional-group-row';
  }
  return '';
};

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 如果是详情行，则合并所有列
  if (row.isDetail) {
    if (columnIndex === 0) {
      return {
        colspan: 4, // 合并所有列（商品名称、数量、单价、金额）
        rowspan: 1
      };
    } else {
      return {
        colspan: 0,
        rowspan: 0
      };
    }
  }
};

const handleOptionalGroupEdit = (group: any) => {
  // 检查必要的属性
  if (!group.isOptionalGroup) {
    ElMessage.error('非可选组商品，不能编辑');
    return;
  }

  if (!group.optionalProducts) {
    ElMessage.error('该可选组没有可选商品');
    return;
  }

  try {
    // 先确保对话框是关闭状态
    optionalGroupDialogVisible.value = false;

    // 创建深拷贝
    const clonedGroup = JSON.parse(JSON.stringify(group));

    // 确保每个商品都有正确的tempQuantity值初始化为其quantity值
    if (clonedGroup.optionalProducts) {
      clonedGroup.optionalProducts.forEach((product: any) => {
        // 使用现有quantity作为初始值
        product.tempQuantity = product.quantity || 0;
      });
    }

    // 使用nextTick确保视图更新后再打开对话框
    nextTick(() => {
      // 设置当前编辑的组
      currentOptionalGroup.value = clonedGroup;
      // 然后再打开对话框
      optionalGroupDialogVisible.value = true;
    });
  } catch (error) {
    console.error('打开可选组编辑对话框错误:', error);
    ElMessage.error('无法打开编辑对话框');
  }
};

const handleOptionalGroupDialogClosed = () => {
  // 完全重置状态
  nextTick(() => {
    currentOptionalGroup.value = null;
    optionalGroupDialogVisible.value = false;
  });
};

const handleOptionalGroupConfirm = (updatedGroup: any) => {
  try {
    if (!updatedGroup || !updatedGroup.optionalProducts) {
      ElMessage.error('无效的可选组数据');
      return;
    }

    // 发射事件给父组件处理
    emit('optional-group-updated', updatedGroup);

    // 关闭对话框
    handleOptionalGroupDialogClosed();
  } catch (error) {
    console.error('处理可选组确认出错:', error);
    ElMessage.error('更新可选组数据失败');
  }
};

const handleDeleteProduct = (row: TableRowItem) => {
  // 确认删除
  ElMessageBox.confirm(`确定要删除商品"${row.productName}"吗？`, '删除确认', {
    confirmButtonText: '确定',
    showCancelButton: false,
    type: 'warning'
  })
    .then(() => {
      // 发射删除事件给父组件处理
      emit('delete-product', row);
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 子组件定义
const ProductNameCell = defineComponent({
  props: {
    row: {
      type: Object as PropType<TableRowItem>,
      required: true
    },
    showDelete: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array as PropType<TableRowItem[]>,
      default: () => []
    }
  },
  emits: ['optional-group-edit', 'delete-product'],
  setup(props, { emit }) {
    // 在子组件中重新定义 findParentItem 函数
    const findParentItem = (parentId: string): any => {
      if (!parentId) {
        return undefined;
      }
      // 在表格数据中查找父项
      const parentItem = props.tableData.find(item => item.id === parentId);
      return parentItem;
    };

    return () => {
      const { row, showDelete } = props;

      // 明细行
      if (row.isDetail) {
        return h(PackageDetailItem, {
          row: row,
          isDetail: row.isDetail,
          parentId: row.parentId,
          parent: findParentItem(row.parentId || '')
        });
      }

      // 套餐行
      if (row.isPackage) {
        return h(
          'div',
          {
            class: 'text-[#000] text-left text-[16px] font-medium py-2 leading-normal break-words whitespace-normal flex items-center justify-between'
          },
          [
            h('div', [h('strong', row.productName), h('div', { class: 'text-xs text-gray-500 mt-1' }, '(套餐)')]),
            showDelete
              ? h(ElButton, {
                  icon: Delete,
                  type: 'danger',
                  text: true,
                  size: 'small',
                  class: 'ml-2',
                  title: '删除商品',
                  onClick: () => emit('delete-product', row)
                })
              : null
          ]
        );
      }

      // 普通商品行
      return h(
        'div',
        {
          class: 'text-[#000] text-left text-[16px] font-medium py-2 leading-normal break-words whitespace-normal flex items-center justify-between'
        },
        [
          h('div', { class: 'flex-1' }, [
            // 可选组
            row.isOptionalGroup
              ? h(
                  'div',
                  {
                    class: 'cursor-pointer hover:text-[#2670de] flex items-center',
                    onClick: () => emit('optional-group-edit', row)
                  },
                  [
                    h('span', { class: 'text-[#E23939] mr-1 text-xs font-medium' }, '*'),
                    h('span', { class: 'text-[#2670de] font-bold' }, row.productName),
                    h(ElIcon, { class: 'ml-1 text-[#666] text-[14px]' }, () => h(Edit))
                  ]
                )
              : h('span', row.productName),

            // 标签
            !row.isOptionalGroup
              ? h('div', { class: 'mt-1' }, [
                  row.isFree ? h('div', { class: 'inline-block px-1 py-0.5 text-xs text-[#FF650F] mr-1' }, '(赠送)') : null,
                  row.isInitialOrder ? h('div', { class: 'inline-block px-1 py-0.5 text-xs text-[#FF650F] mr-1' }, '(例点)') : null
                ])
              : null
          ]),

          // 删除按钮
          showDelete && !row.isOptionalGroup
            ? h(ElButton, {
                icon: Delete,
                type: 'danger',
                text: true,
                size: 'small',
                class: 'ml-2',
                title: '删除商品',
                onClick: () => emit('delete-product', row)
              })
            : null
        ]
      );
    };
  }
});

const QuantityCell = defineComponent({
  props: {
    row: {
      type: Object as PropType<TableRowItem>,
      required: true
    }
  },
  setup(props) {
    // 在子组件中重新定义 getSelectedQuantity 函数
    const getSelectedQuantity = (row: TableRowItem): number => {
      // 非可选组商品，直接返回quantity
      if (!row.isOptionalGroup) {
        return row.quantity || 0;
      }

      // 可选组商品，如果有selectedQuantity字段则使用它
      if (row.selectedQuantity !== undefined) {
        return row.selectedQuantity;
      }

      // 否则计算已选商品总量
      if (row.optionalProducts && row.optionalProducts.length > 0) {
        return row.optionalProducts.reduce((sum: number, product: any) => sum + (product.quantity || 0), 0);
      }

      return 0;
    };

    return () => {
      const { row } = props;
      if (!row.isDetail) {
        return h('div', { class: 'flex justify-center w-full' }, [h('span', { class: 'font-medium' }, getSelectedQuantity(row))]);
      }
      return null;
    };
  }
});

const PriceCell = defineComponent({
  props: {
    row: {
      type: Object as PropType<TableRowItem>,
      required: true
    }
  },
  setup(props) {
    return () => {
      const { row } = props;

      if (row.isOptionalGroup) {
        return h('div', { class: 'flex justify-end' }, [h('span', { class: 'text-gray-500' }, '--')]);
      }

      if (!row.isDetail && !row.isFree) {
        return h('div', { class: 'flex justify-end' }, [h(PriceDisplay, { amountInFen: row.currentPrice || row.price || 0 })]);
      }

      if (!row.isDetail && row.isFree) {
        return h('div', { class: 'flex justify-end' }, [
          h('div', { class: 'line-through text-gray-500' }, [h(PriceDisplay, { amountInFen: row.currentPrice || 0 })])
        ]);
      }

      return null;
    };
  }
});

const AmountCell = defineComponent({
  props: {
    row: {
      type: Object as PropType<TableRowItem>,
      required: true
    }
  },
  setup(props) {
    return () => {
      const { row } = props;

      if (row.isOptionalGroup) {
        return h('div', { class: 'flex justify-end' }, [h(PriceDisplay, { amountInFen: row.totalAmount })]);
      }

      if (!row.isDetail && !row.isFree) {
        return h('div', { class: 'flex justify-end' }, [
          h(PriceDisplay, {
            amountInFen: row.totalAmount !== undefined ? row.totalAmount : (row.price || 0) * (row.quantity || 0)
          })
        ]);
      }

      if (!row.isDetail && row.isFree) {
        return h('div', { class: 'flex justify-end' }, [h(PriceDisplay, { amountInFen: 0 })]);
      }

      return null;
    };
  }
});
</script>

<style scoped>
.table-no-padding :deep(.el-table__cell) {
  padding: 8px 12px;
}

.detail-row-class {
  background-color: #f8f9fa;
}

.is-package-row {
  background-color: #fff7e6;
}

.is-optional-group-row {
  background-color: #f0f9ff;
}

.non-detail-cell {
  /* 非明细行的样式 */
}

/* 开台方案商品表格样式 */
.initial-order-table {
  border-radius: 8px;
  overflow: hidden;
}

.initial-order-table :deep(.el-table__header-wrapper tr th) {
  background-color: rgba(255, 101, 15, 0.08);
  color: #ff650f;
  font-weight: 500;
}

.initial-order-table :deep(.el-table__body tr:hover > td) {
  background-color: rgba(255, 101, 15, 0.05) !important;
}

/* 单独点单商品表格样式 */
.additional-order-table {
  border-radius: 8px;
  overflow: hidden;
}

.additional-order-table :deep(.el-table__header-wrapper tr th) {
  background-color: rgba(38, 112, 222, 0.08);
  color: #2670de;
  font-weight: 500;
}

.additional-order-table :deep(.el-table__body tr:hover > td) {
  background-color: rgba(38, 112, 222, 0.05) !important;
}

/* 分组标题样式 */
.group-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.group-indicator {
  width: 4px;
  height: 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.group-indicator.initial {
  background-color: #ff650f;
}

.group-indicator.additional {
  background-color: #2670de;
}
</style>
