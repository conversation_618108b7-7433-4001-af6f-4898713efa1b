import { receiptBuilder } from '@/domains/prints/receipt-builder';
import { escPosCommandGenerator } from '@/infrastructure/printing/escPosCommandGenerator';
import { printerService, configurePrinter } from '@/infrastructure/printing/printerService';
import { toast } from '@/components/customer/toast';
import { OrderDetailData } from '@/domains/prints/order-detail/models/order-detail-data';
import { ProductionOrderData, ProductionOrderItem } from '@/domains/prints/production-order/models/production-order-data';
import { ShiftReportData } from '@/domains/prints/shift-report/models/shift-report-data';
import { SessionOrderData } from '@/domains/prints/session-order/models/session-order-data';
import { useDeviceStore } from '@/stores/deviceStore';
import { postApiPrintRecordOpenTableCreate, postApiPrintRecordProductOutCreate, postApiPrintRecordCheckoutCreate, postApiPrintRecordShiftChangeCreate, postApiPrintRecordRoomExtensionCreate } from '@/api/autoGenerated';
import type { CreateOpenTablePrintRecordReqDto, CreateProductOutPrintRecordReqDto, CreateCheckoutPrintRecordReqDto, CreateShiftChangePrintRecordReq, CreateRoomExtensionPrintRecordReqDto } from '@/api/autoGenerated';
import { formatDateTime } from '@/utils/dateUtils';
import { useUserStore } from '@/stores/userStore';
import { useVenueStore } from '@/stores/venueStore';
/**
 * 打印应用服务 - 负责编排打印流程
 * 接收领域实体作为输入
 */
export class PrintingService {
  private readonly port = 9100;
  private readonly simulatePrint = true;

  /**
   * 打印出品单
   * @param productionOrderData 转换后的出品单领域实体
   * @param printerConfig 打印机配置 - 用于特定出品点的网络打印机
   */
  private async printProductionOrder(productionOrderData: ProductionOrderData, venueId: string, printerConfig?: { ip: string; port: number }): Promise<boolean> {
    try {
      // 1. 数据已由调用方准备好 (productionOrderData)
      if (!productionOrderData) {
        throw new Error('出品单数据不能为空');
      }

      // 2. 配置打印机 (使用传入的配置)
      if (!this.simulatePrint) {
        if (!printerConfig || !printerConfig.ip) {
          throw new Error('未提供有效的打印机配置');
        }
        configurePrinter(printerConfig);
      }

      // 3. 构建结构化票据对象 (使用传入的领域实体)
      const receipt = receiptBuilder.buildProductionOrderReceipt(productionOrderData, venueId);

      // 4. 将票据转换为打印命令
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      // 5. 执行打印（如果不是模拟打印）
      if (!this.simulatePrint) {
        const success = await printerService.print(commands);

        if (!success) {
          throw new Error('打印失败');
        }

        // 6. 处理结果
        toast({
          title: '成功',
          description: '打印成功'
        });
      } else {
        console.log('模拟打印模式：无需实际打印');
      }

      return true;
    } catch (error) {
      console.error('打印出品单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000
        });
      }
      return false;
    }
  }

  /**
   * 打印交班单
   * @param shiftReportData 转换后的交班单领域实体
   */
  async printShiftReport(shiftReportData: ShiftReportData): Promise<boolean> {
    try {
      // 1. 数据已由调用方准备好 (shiftReportData)
      if (!shiftReportData) {
        throw new Error('交班数据不能为空');
      }

      // 2. 从deviceStore获取打印机配置
      const deviceStore = useDeviceStore();
      const printerConfig = this.getPrinterConfig(deviceStore);

      // 3. 配置打印机
      if (!this.simulatePrint) {
        configurePrinter(printerConfig);
      }

      // 4. 构建结构化票据对象 (使用传入的领域实体)
      const receipt = receiptBuilder.buildShiftReportReceipt(shiftReportData);

      // 5. 将票据转换为打印命令
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      // 6. 执行打印（如果不是模拟打印）
      if (!this.simulatePrint) {
        const success = await printerService.print(commands);

        if (!success) {
          throw new Error('打印失败');
        }

        // 7. 处理结果
        toast({
          title: '成功',
          description: '打印成功'
        });
      } else {
        console.log('模拟打印模式：无需实际打印');
      }

      return true;
    } catch (error) {
      console.error('打印交班单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000
        });
      }
      return false;
    }
  }

  /**
   * 根据会话ID打印出品单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印是否成功
   */
  async printProductOutBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      if (!sessionId) {
        console.warn('打印服务: 没有可打印出品单的sessionId');
        return false;
      }

      // 1. 获取场馆ID和员工姓名
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;
      // 获取员工姓名
      const userStore = useUserStore();
      const employeeName = userStore.userInfo?.name || '';

      // 2. 准备API请求参数
      const params: CreateProductOutPrintRecordReqDto = {
        orderNos: orderNos,
        sessionId: sessionId,
        venueId: venueId,
        employeeName: employeeName
      };

      // 3. 调用API获取打印任务列表
      console.log('打印服务: 请求打印出品单数据，参数:', params);
      const response = await postApiPrintRecordProductOutCreate(params);
      const printTasks = response.data;

      if (!printTasks || printTasks.length === 0) {
        console.warn('打印服务: 获取的出品单数据为空');
        return false;
      }

      console.log('打印服务: 获取到出品单数据:', printTasks);

      // 4. 逐个执行打印任务
      const printResults: boolean[] = [];

      for (const task of printTasks) {
        const printerConfig = {
          ip: task.printerIp || 'localhost',
          port: this.port
        };

        if (!task.data) {
          console.warn('打印服务: 出品单数据为空');
          continue;
        }

        // 将API返回的ProductionOrderDataVO转换为领域实体ProductionOrderData
        const productionOrderData: ProductionOrderData = {
          roomInfo: task.data.roomInfo,
          employeeName: task.data.employeeName,
          orderTime: task.data.orderTime || formatDateTime(new Date().toISOString()),
          productionOrderNo: task.data.productionOrderNo || '',
          orderNo: task.data.orderNo,
          sessionId: task.data.sessionId,
          products: (task.data.products || []) as unknown as ProductionOrderItem[],
          producerName: task.data.producerName,
          productionTime: task.data.productionTime
        };

        // 调用已有的出品单打印方法
        const result = await this.printProductionOrder(productionOrderData, venueId, printerConfig);
        printResults.push(result);
      }

      // 5. 只要有一个打印成功，就认为整体打印成功
      const isSuccess = printResults.some(result => result === true);

      if (isSuccess) {
        if (!this.simulatePrint) {
          toast({
            title: '成功',
            description: '打印出品单成功'
          });
        }
      } else {
        throw new Error('所有打印任务均失败');
      }

      return isSuccess;
    } catch (error) {
      console.error('打印出品单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000
        });
      }
      return false;
    }
  }

  /**
   * 根据会话ID打印开台单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印是否成功
   */
  async printSessionOrderBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      if (!sessionId) {
        console.warn('打印服务: 没有可打印开台单的sessionId');
        return false;
      }

      // 1. 获取场馆ID
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;

      // 2. 准备API请求参数
      const params: CreateOpenTablePrintRecordReqDto = {
        orderNos: orderNos,
        sessionId: sessionId,
        venueId: venueId
      };

      // 3. 调用API获取打印数据
      console.log('打印服务: 请求打印开台单数据，参数:', params);
      const response = await postApiPrintRecordOpenTableCreate(params);
      const printData = response.data.sessionOrderData;

      if (!printData) {
        console.warn('打印服务: 获取的开台单数据为空');
        return false;
      }

      console.log('打印服务: 获取到开台单数据:', printData);

      // 4. 从deviceStore获取打印机配置
      const deviceStore = useDeviceStore();
      const printerConfig = this.getPrinterConfig(deviceStore);

      // 5. 配置打印机
      if (!this.simulatePrint) {
        console.log('打印服务: 配置打印机:', printerConfig);
        configurePrinter(printerConfig);
      }

      // 6. 构建结构化票据对象
      const receipt = receiptBuilder.buildSessionOrderReceipt(printData as SessionOrderData, venueId);

      // 7. 将票据转换为打印命令
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      // 8. 执行打印（如果不是模拟打印）
      if (!this.simulatePrint) {
        console.log('打印服务: 执行打印开台单');
        const success = await printerService.print(commands);

        if (!success) {
          throw new Error('打印失败');
        }

        // 9. 处理结果
        toast({
          title: '成功',
          description: '打印开台单成功'
        });
      } else {
        console.log('模拟打印模式：无需实际打印开台单');
      }

      return true;
    } catch (error) {
      console.error('打印开台单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000
        });
      }
      return false;
    }
  }

  /**
   * 根据会话ID打印续房单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印是否成功
   */
  async printRoomExtensionBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      if (!sessionId) {
        console.warn('打印服务: 没有可打印续房单的sessionId');
        return false;
      }

      // 1. 获取场馆ID
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;

      // 2. 准备API请求参数
      const params: CreateRoomExtensionPrintRecordReqDto = {
        orderNos: orderNos,
        sessionId: sessionId,
        venueId: venueId
      };

      // 3. 调用API获取打印数据
      console.log('打印服务: 请求打印续房单数据，参数:', params);
      const response = await postApiPrintRecordRoomExtensionCreate(params);
      const printData = response.data.sessionOrderData;

      if (!printData) {
        console.warn('打印服务: 获取的续房单数据为空');
        return false;
      }

      console.log('打印服务: 获取到续房单数据:', printData);

      // 4. 从deviceStore获取打印机配置
      const deviceStore = useDeviceStore();
      const printerConfig = this.getPrinterConfig(deviceStore);

      // 5. 配置打印机
      if (!this.simulatePrint) {
        console.log('打印服务: 配置打印机:', printerConfig);
        configurePrinter(printerConfig);
      }

      // 6. 构建结构化票据对象
      const receipt = receiptBuilder.buildRoomExtensionReceipt(printData as SessionOrderData, venueId);

      // 7. 将票据转换为打印命令
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      // 8. 执行打印（如果不是模拟打印）
      if (!this.simulatePrint) {
        console.log('打印服务: 执行打印续房单');
        const success = await printerService.print(commands);

        if (!success) {
          throw new Error('打印失败');
        }

        // 9. 处理结果
        toast({
          title: '成功',
          description: '打印续房单成功'
        });
      } else {
        console.log('模拟打印模式：无需实际打印续房单');
      }

      return true;
    } catch (error) {
      console.error('打印续房单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000
        });
      }
      return false;
    }
  }

  /**
   * 根据结账单ID打印结账单
   * @param payBillId 结账单ID
   * @param sessionId 会话ID
   * @returns 打印是否成功
   */
  async printCheckoutBillByPayBillId(payBillId: string | undefined, sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      if (!payBillId) {
        console.warn('打印服务: 没有可打印结账单的payBillId');
        return false;
      }

      // 1. 获取场馆ID
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;

      // 2. 准备API请求参数
      const params: CreateCheckoutPrintRecordReqDto = {
        payBillId: payBillId,
        sessionId: sessionId || '',
        venueId: venueId,
        orderNos: orderNos || []
      };

      // 3. 调用API获取打印数据
      console.log('打印服务: 请求打印结账单数据，参数:', params);
      const response = await postApiPrintRecordCheckoutCreate(params);
      const printData = response.data.checkoutBillData;

      if (!printData) {
        console.warn('打印服务: 获取的结账单数据为空');
        return false;
      }

      console.log('打印服务: 获取到结账单数据:', printData);

      // 4. 从deviceStore获取打印机配置
      const deviceStore = useDeviceStore();
      const printerConfig = this.getPrinterConfig(deviceStore);

      // 5. 配置打印机
      if (!this.simulatePrint) {
        console.log('打印服务: 配置打印机:', printerConfig);
        configurePrinter(printerConfig);
      }

      // 6. 构建结构化票据对象
      const orderDetailData: OrderDetailData = printData as OrderDetailData;

      const receipt = receiptBuilder.buildOrderDetailReceipt(orderDetailData, venueId);

      // 7. 将票据转换为打印命令
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      // 8. 执行打印（如果不是模拟打印）
      if (!this.simulatePrint) {
        console.log('打印服务: 执行打印结账单');
        const success = await printerService.print(commands);

        if (!success) {
          throw new Error('打印失败');
        }

        // 9. 处理结果
        toast({
          title: '成功',
          description: '打印结账单成功'
        });
      } else {
        console.log('模拟打印模式：无需实际打印结账单');
      }

      return true;
    } catch (error) {
      console.error('打印结账单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000
        });
      }
      return false;
    }
  }

  /**
   * 根据交班单号打印交班单
   * @param handNo 交班单号
   * @param employeeId 员工ID（可选）
   * @returns 打印是否成功
   */
  async printShiftChangeByHandNo(
    handNo: string | undefined,
    employeeId?: string
  ): Promise<boolean> {
    try {
      if (!handNo) {
        console.warn('打印服务: 没有可打印交班单的交班单号');
        return false;
      }

      // 1. 获取场馆ID和用户信息
      const deviceStore = useDeviceStore();
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;
      const userStore = useUserStore();
      const operatorId = userStore.userInfo?.employee?.id || '';
      
      // 2. 准备API请求参数
      const params: CreateShiftChangePrintRecordReq = {
        handNo: handNo,
        venueId: venueId,
        operatorId: operatorId,
        employeeId: employeeId,
        deviceName: deviceStore.cashierMachine?.name || '未知设备'
      }
      
      // 3. 调用API获取打印数据
      console.log('打印服务: 请求打印交班单数据，参数:', params);
      const response = await postApiPrintRecordShiftChangeCreate(params);
      const printData = response.data.shiftChangeBillData;
      
      if (!printData) {
        console.warn('打印服务: 获取的交班单数据为空');
        return false;
      }
      
      console.log('打印服务: 获取到交班单数据:', printData);
      
      // 4. 从deviceStore获取打印机配置
      const printerConfig = this.getPrinterConfig(deviceStore);

      // 5. 配置打印机
      if (!this.simulatePrint) {
        console.log('打印服务: 配置打印机:', printerConfig);
        configurePrinter(printerConfig);
      }

      // 6. 构建交班单领域实体
      const shiftReportData: ShiftReportData = {
        shiftTime: printData.printTime || new Date().toISOString(),
        employee: printData.handEmployee || '',
        shiftId: printData.handNo || '',
        orderCount: printData.sessionCount || 0,
        // 转换营业数据
        businessData: {
          receivable: printData.totalReceivable || 0,
          // 可以根据需要添加更多字段
        },
        // 转换支付方式
        paymentMethods: (printData.paymentMethods || []).map((payment: any) => ({
          method: payment.paymentMethod || '',
          amount: payment.amount || 0
        }))
      };
      
      // 7. 构建结构化票据对象
      const receipt = receiptBuilder.buildShiftReportReceipt(shiftReportData, venueId);

      // 8. 将票据转换为打印命令
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      // 9. 执行打印（如果不是模拟打印）
      if (!this.simulatePrint) {
        console.log('打印服务: 执行打印交班单');
        const success = await printerService.print(commands);

        if (!success) {
          throw new Error('打印失败');
        }

        // 10. 处理结果
        toast({
          title: '成功',
          description: '打印交班单成功',
        });
      } else {
        console.log('模拟打印模式：无需实际打印交班单');
      }

      return true;
    } catch (error) {
      console.error('打印交班单失败:', error);
      if (!this.simulatePrint) {
        toast({
          title: '错误',
          description: error instanceof Error ? error.message : '打印失败',
          duration: 1000,
        });
      }
      return false;
    }
  }


  /**
   * 从deviceStore获取打印机配置
   * @private
   */
  private getPrinterConfig(deviceStore: ReturnType<typeof useDeviceStore>): { ip: string; port: number } {
    // 从收银机配置中获取打印机IP
    const cashierMachine = deviceStore.cashierMachine;
    const useNetworkPrinter = cashierMachine?.useNetworkPrinter;
    const printerIp = cashierMachine?.printerIp;

    console.log('cashierMachine', cashierMachine);
    // 如果使用网络打印机且有配置IP，则使用配置的打印机
    if (useNetworkPrinter && printerIp) {
      return { ip: printerIp, port: this.port };
    }

    // 默认返回USB打印机配置（本地打印机）
    return { ip: 'localhost', port: this.port };
  }
}

// 导出服务实例
export const printingService = new PrintingService();
