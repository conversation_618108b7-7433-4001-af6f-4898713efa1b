<template>
  <div v-if="isDetail" class="package-detail-expanded-item">
    <div class="w-full px-4 py-2">
      <template v-if="parentId && parent?.packageDetail">
        <div v-if="allProductItems.length">
          <div class="flex flex-wrap -mx-1">
            <div v-for="(product, index) in allProductItems" :key="index" class="px-1 py-1">
              <span class="inline-block rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600">
                {{ product.name }} x {{ calculateQuantity(product.count) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 如果没有详细数据，显示简单字符串 -->
        <div v-if="!allProductItems.length" class="text-gray-600">
          {{ getPackageDetailsText() }}
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import { ICartItem } from '@/modules/production/views/OpenTableProduct/viewmodel';

interface DetailItem {
  id: string;
  name: string;
  count: number;
  [key: string]: any;
}

const props = defineProps({
  // 行数据
  row: {
    type: Object,
    required: true
  },
  // 是否为详情行
  isDetail: {
    type: Boolean,
    default: false
  },
  // 父项ID
  parentId: {
    type: String,
    default: ''
  },
  // 父项数据
  parent: {
    type: Object as () => ICartItem | undefined,
    default: undefined
  }
});

// 解析固定商品
const packageProducts = computed(() => {
  if (!props.parent?.packageDetail?.packageProducts) {
    return [];
  }

  try {
    // 获取商品列表
    const products = JSON.parse(props.parent.packageDetail.packageProducts);
    const productVOList = props.parent.packageDetail.productVOList || [];

    // 将商品ID转换为具体商品信息
    return products.map((product: any) => {
      const productInfo = productVOList.find((p: any) => p.id === product.id);
      return {
        id: product.id,
        name: productInfo?.name || `商品(${product.id})`,
        count: product.count
      };
    });
  } catch (e) {
    console.error('解析套餐固定商品失败:', e);
    return [];
  }
});

// 解析可选组商品
const optionalProducts = computed(() => {
  if (!props.parent?.packageDetail?.optionalGroups) {
    return [];
  }

  try {
    const result: DetailItem[] = [];
    const optionalGroups = JSON.parse(props.parent.packageDetail.optionalGroups);
    const productVOList = props.parent.packageDetail.productVOList || [];

    // 从每个可选组中提取选中的商品
    optionalGroups.forEach((group: any) => {
      if (Array.isArray(group.products)) {
        group.products.forEach((product: any) => {
          // 确保读取正确的selected_count属性
          const selectedCount = product.selected_count || 0;
          if (selectedCount > 0) {
            const productInfo = productVOList.find((p: any) => p.id === product.id);

            // 如果找不到产品信息，尝试直接从product对象读取name
            const productName = productInfo?.name || product.name || `可选商品(${product.id})`;

            result.push({
              id: product.id,
              name: productName,
              count: selectedCount
            });
          }
        });
      }
    });

    return result;
  } catch (e) {
    console.error('解析套餐可选商品失败:', e);
    return [];
  }
});

// 合并所有商品为一个扁平列表
const allProductItems = computed(() => {
  // 尝试先从packageDetail直接读取selectedProducts
  if (props.parent?.packageDetail) {
    try {
      // 如果packageDetail是字符串，尝试解析它
      let packageDetailObj = props.parent.packageDetail;
      if (typeof packageDetailObj === 'string') {
        packageDetailObj = JSON.parse(packageDetailObj);
      }

      // 检查是否有selectedProducts字段
      if (packageDetailObj && 'selectedProducts' in packageDetailObj) {
        const selectedProducts = packageDetailObj.selectedProducts;

        if (Array.isArray(selectedProducts) && selectedProducts.length > 0) {
          const mappedProducts = selectedProducts.map((product: any) => ({
            id: product.id,
            name: product.name,
            count: product.count
          }));
          return mappedProducts;
        }
      }

      // 如果没有selectedProducts，检查是否有detailString
      if (packageDetailObj && packageDetailObj.detailString) {
        // 可以考虑解析detailString，但通常这只是显示用的字符串
      }
    } catch (e) {
      console.error('解析packageDetail中的selectedProducts失败:', e);
    }
  }

  // 如果没有selectedProducts或解析失败，从optionalGroups中获取实际选择的商品
  const fromOptionalGroups = computed(() => {
    if (!props.parent?.packageDetail?.optionalGroups) {
      return [];
    }

    try {
      const result: DetailItem[] = [];
      const optionalGroups = JSON.parse(props.parent.packageDetail.optionalGroups);
      const productVOList = props.parent.packageDetail.productVOList || [];

      // 从每个可选组中提取选中的商品
      optionalGroups.forEach((group: any, groupIndex: number) => {
        if (Array.isArray(group.products)) {
          group.products.forEach((product: any, productIndex: number) => {
            // 确保读取正确的selected_count属性
            const selectedCount = product.selected_count || 0;

            if (selectedCount > 0) {
              const productInfo = productVOList.find((p: any) => p.id === product.id);

              // 如果找不到产品信息，尝试直接从product对象读取name
              const productName = productInfo?.name || product.name || `可选商品(${product.id})`;

              result.push({
                id: product.id,
                name: productName,
                count: selectedCount
              });
            }
          });
        }
      });

      return result;
    } catch (e) {
      console.error('解析套餐可选商品失败:', e);
      return [];
    }
  });

  // 合并固定商品和实际选择的可选商品
  const finalResult = [...packageProducts.value, ...fromOptionalGroups.value];
  return finalResult;
});

// 计算随套餐数量变化的商品数量
const calculateQuantity = (originalCount: number | undefined): number => {
  // 处理undefined情况
  if (originalCount === undefined) {
    return 0;
  }
  // 父项商品的数量(套餐数量)
  const parentQuantity = props.parent?.quantity || 1;
  // 返回原始数量 × 套餐数量
  const finalQuantity = originalCount * parentQuantity;

  return finalQuantity;
};

// 获取套餐明细显示文本(仅作为备选)
const getPackageDetailsText = (): string => {
  return props.parent?.packageDetail?.detailString || '';
};
</script>

<style lang="scss" scoped>
.package-detail-expanded-item {
  background-color: #f9f9f9;
  padding: 8px 0;
  margin-top: -1px;
}
</style>
