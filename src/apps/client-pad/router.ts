import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { EnvironmentUtils } from '@/utils/envUtils';
import NProgress from 'nprogress';
import '@/styles/nprogress.css';
import PadLayout from '@/layouts/PadLayout.vue';

// NProgress配置
NProgress.configure({
  easing: 'ease',
  speed: 500,
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.2
});

// 定义 Pad 应用的路由
const padRoutes: RouteRecordRaw[] = [
  // 基础路由 - 认证和登录
  {
    path: '/auth',
    name: 'auth',
    meta: {
      title: '设备授权',
      requiresAuth: false
    },
    component: () => import('./views/Auth/index.vue')
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      requiresAuth: false
    },
    component: () => import('./views/Login/index.vue')
  },

  // 使用 PadLayout 作为需要认证的路由的布局
  {
    path: '/',
    component: PadLayout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'room',
        meta: {
          title: '包厢首页'
        },
        component: () => import('./views/Room/index.vue')
      },
      {
        path: 'product-order',
        name: 'product-order',
        meta: {
          title: '商品点单'
        },
        component: () => import('./views/ProductOrder/index.vue')
      }
    ]
  },

  // 通配符路由 - 捕获所有未匹配的路由
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('./views/NotFound.vue')
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL || '/'),
  routes: padRoutes,
  scrollBehavior(to, from, savedPosition) {
    // 使用更可靠的滚动行为处理
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 设置路由守卫
router.beforeEach((to, from, next) => {
  // 开始加载进度条
  NProgress.start();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Thunder ERP Pad`;
  }

  console.log('路由 to', to);

  // 权限检查逻辑 - 后续可以扩展更复杂的验证
  const isAuthenticated = localStorage.getItem('pad_token') !== null;

  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要认证但用户未登录，重定向到登录页
    next({ name: 'login', query: { redirect: to.fullPath } });
  } else {
    console.log('路由 next', next);
    // 其他情况直接通过
    next();
  }
});

router.afterEach(() => {
  // 完成加载进度条
  NProgress.done();
});

export default router;
