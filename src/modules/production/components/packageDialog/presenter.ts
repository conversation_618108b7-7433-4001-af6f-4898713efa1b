import { ref, computed } from 'vue';
import type { IPackageDialogViewModel, SelectedPackageProduct } from './viewmodel';
import type { ProductPackageVO } from '@/types/projectobj';
import { usePackageDialogConverter } from './converter';
import { isOptionalByCount, isOptionalByPlan } from '@/utils/productPackageUtils';
import { computeSelectedPackageProducts } from '@/utils/selectedPackageUtils';
import { isString, isNumber, isArray, isEmpty, keyBy, groupBy, debounce } from 'lodash';

// 引入扩展接口
interface ExtendedProductPackageVO extends ProductPackageVO {
  packageDetail?: string | any;
}

// 定义可选组视图模型类型，用于组件视图层
export interface OptionalGroupViewModel {
  name: string;
  optionCount: number;
  optionType: 'by_count' | 'by_plan';
  products: Array<{
    id: string;
    name: string;
    currentPrice: number;
    count: number;
    selected_count: number;
    // 对于by_plan类型，保存原始的count作为基数
    originalCount?: number;
    // 标记是否为by_plan类型的商品
    isPlanProduct?: boolean;
  }>;
}

// 扩展类型以处理packageDetail属性
interface PackageConfirmData extends ExtendedProductPackageVO {
  packageDetail?: string;
}

export class PackageDialogPresenter implements IPackageDialogViewModel {
  private converter = usePackageDialogConverter();

  // 内部状态
  private _state = ref({
    packageData: null as ExtendedProductPackageVO | null,
    visible: false,
    packageQuantity: 1,
    // 新增：维护响应式的可选组数据
    optionalGroupsData: [] as OptionalGroupViewModel[],
    // 新增：跟踪套餐内所选商品的状态
    selectedPackageProducts: [] as SelectedPackageProduct[]
  });

  constructor(initialData?: ExtendedProductPackageVO) {
    if (initialData) {
      // 使用 lodash 验证套餐数据
      if (this.validatePackageData(initialData)) {
        this._state.value.packageData = initialData;
        // 初始化可选组数据
        this._state.value.optionalGroupsData = this.converter.getOptionalGroups(initialData as any);
        // 初始化已选商品状态
        this.initializeSelectedProducts();
        console.log('✅ 套餐数据验证通过，初始化完成');
      } else {
        console.error('❌ 套餐数据验证失败，无法初始化');
        // 设置默认空状态
        this._state.value.packageData = null;
        this._state.value.optionalGroupsData = [];
        this._state.value.selectedPackageProducts = [];
      }
    }
  }

  // 初始化已选商品状态
  private initializeSelectedProducts() {
    if (!this._state.value.packageData) return;

    this._state.value.selectedPackageProducts = computeSelectedPackageProducts(this._state.value.packageData, this._state.value.optionalGroupsData) as any;
  }

  // 🔥 新增：重新计算所有已选商品状态
  private recalculateSelectedProducts() {
    if (!this._state.value.packageData) return;

    this._state.value.selectedPackageProducts = computeSelectedPackageProducts(this._state.value.packageData, this._state.value.optionalGroupsData) as any;
  }

  // 生成套餐配置唯一标识
  private generateConfigFingerprint(selectedProducts: SelectedPackageProduct[]): string {
    // 按ID排序以确保相同选择但顺序不同的产品生成相同的指纹
    const sortedProducts = [...selectedProducts].sort((a, b) => {
      if (a.id === b.id) {
        // 如果ID相同，比较数量
        return a.count - b.count;
      }
      return a.id.localeCompare(b.id);
    });

    // 转换为简化的字符串表示
    const productsStr = sortedProducts.map(p => `${p.id}:${p.count}${p.groupIndex !== undefined ? `:${p.groupIndex}` : ''}`).join('|');

    return productsStr;
  }

  // 使用 lodash 的数据验证辅助方法
  private validatePackageData(packageData: ExtendedProductPackageVO | null): boolean {
    if (!packageData) {
      console.warn('套餐数据为空');
      return false;
    }

    // 验证基本字段
    if (!isString(packageData.id) || isEmpty(packageData.id)) {
      console.warn('套餐ID无效');
      return false;
    }

    if (!isString(packageData.name) || isEmpty(packageData.name)) {
      console.warn('套餐名称无效');
      return false;
    }

    if (!isNumber(packageData.currentPrice) || packageData.currentPrice < 0) {
      console.warn('套餐价格无效');
      return false;
    }

    // 验证商品列表
    if (!isArray(packageData.productVOList) || isEmpty(packageData.productVOList)) {
      console.warn('套餐商品列表无效');
      return false;
    }

    // 验证固定商品
    if (packageData.packageProducts) {
      if (!isString(packageData.packageProducts)) {
        console.warn('固定商品数据格式无效');
        return false;
      }

      try {
        const fixedProducts = JSON.parse(packageData.packageProducts);
        if (!isArray(fixedProducts)) {
          console.warn('固定商品数据不是数组');
          return false;
        }
      } catch (e) {
        console.warn('固定商品数据解析失败');
        return false;
      }
    }

    // 验证可选组
    if (packageData.optionalGroups) {
      if (!isString(packageData.optionalGroups)) {
        console.warn('可选组数据格式无效');
        return false;
      }

      try {
        const optionalGroups = JSON.parse(packageData.optionalGroups);
        if (!isArray(optionalGroups)) {
          console.warn('可选组数据不是数组');
          return false;
        }

        // 验证每个可选组的结构
        const isValidGroups = optionalGroups.every(group => {
          return (
            group &&
            isString(group.name) &&
            !isEmpty(group.name) &&
            isNumber(group.optionCount) &&
            group.optionCount > 0 &&
            isString(group.optionType) &&
            ['by_count', 'by_plan'].includes(group.optionType) &&
            isArray(group.products) &&
            !isEmpty(group.products)
          );
        });

        if (!isValidGroups) {
          console.warn('可选组结构验证失败');
          return false;
        }
      } catch (e) {
        console.warn('可选组数据解析失败');
        return false;
      }
    }

    return true;
  }

  // 计算属性
  public computed = {
    // 计算套餐总价
    totalPrice: computed(() => {
      if (!this._state.value.packageData || this._state.value.packageData.currentPrice === undefined) return 0;

      // 套餐价格是固定的，直接取套餐的 currentPrice
      const packagePrice = this._state.value.packageData.currentPrice || 0;

      // 乘以套餐数量
      return packagePrice * this._state.value.packageQuantity;
    }),

    // 获取默认商品列表
    defaultProducts: computed(() => {
      if (!this._state.value.packageData) return [];
      return this.converter.getDefaultProducts(this._state.value.packageData as any);
    }),

    // 获取可选组列表 - 直接返回响应式数据
    optionalGroups: computed(() => {
      return this._state.value.optionalGroupsData;
    }),

    // 获取已选的可选商品列表 - 用于左侧栏显示
    selectedOptionalItems: computed(() => {
      // 基于optionalGroupsData过滤出有已选商品的组
      return (
        this._state.value.optionalGroupsData
          .map(group => ({
            ...group,
            // 只包含已选数量大于0的商品
            products: group.products.filter(p => p.selected_count > 0)
          }))
          // 过滤掉没有选中任何商品的组
          .filter(group => group.products.length > 0)
      );
    }),

    // 验证是否可以提交
    isValid: computed(() => {
      if (!this._state.value.packageData) return false;
      return this.converter.validate(this._state.value.packageData as any);
    }),

    // 计算所有已选商品
    allSelectedProducts: computed(() => {
      return this._state.value.selectedPackageProducts;
    })
  };

  // 暴露状态
  public get state() {
    return this._state.value;
  }

  // 动作实现
  public actions = {
    // 使用 lodash debounce 优化用户交互体验
    updateProductCount: debounce((groupIndex: number, productIndex: number, count: number) => {
      if (!this._state.value.packageData) return;

      console.log(`🔥 updateProductCount: 组${groupIndex}, 商品${productIndex}, 数量${count}`);

      // 1. 更新响应式数据
      const group = this._state.value.optionalGroupsData[groupIndex];
      if (!group) {
        console.warn(`组${groupIndex}不存在`);
        return;
      }

      const product = group.products[productIndex];
      if (!product) {
        console.warn(`组${groupIndex}中商品${productIndex}不存在`);
        return;
      }

      // 🔥 重要：根据可选组类型应用不同的限制逻辑
      if (isOptionalByCount(group)) {
        // by_count 模式：限制总数量不超过 optionCount
        // 计算当前总数（不包括当前要修改的商品）
        const currentTotalWithoutSelf = group.products.reduce((sum: number, p: any, idx: number) => {
          if (idx === productIndex) return sum;
          const productCount = isNumber(p.count) && p.count > 0 ? p.count : 1;
          const selectedCount = isNumber(p.selected_count) && p.selected_count >= 0 ? p.selected_count : 0;
          return sum + productCount * selectedCount;
        }, 0);

        // 计算剩余可选数量
        const remainingCount = group.optionCount - currentTotalWithoutSelf;
        const productCount = isNumber(product.count) && product.count > 0 ? product.count : 1;
        const maxAllowed = Math.floor(remainingCount / productCount);

        // 限制不超过最大允许数量，且不能为负数
        count = Math.max(0, Math.min(count, maxAllowed));

        console.log(`🔥 by_count限制: 当前总数${currentTotalWithoutSelf}, 剩余${remainingCount}, 最大允许${maxAllowed}, 最终数量${count}`);
      } else if (isOptionalByPlan(group)) {
        // by_plan 模式：限制选中商品种类数不超过 optionCount
        // 计算当前已选商品种类数（不包括当前要修改的商品）
        const currentSelectedTypesWithoutSelf = group.products.filter((p: any, idx: number) => {
          if (idx === productIndex) return false;
          const selectedCount = isNumber(p.selected_count) ? p.selected_count : 0;
          return selectedCount > 0;
        }).length;

        if (count > 0) {
          // 如果要选择这个商品，检查是否会超过限制
          if (currentSelectedTypesWithoutSelf >= group.optionCount) {
            // 已经达到限制，不能再选择新商品
            console.log(`🔥 by_plan限制: 已选${currentSelectedTypesWithoutSelf}种，达到上限${group.optionCount}，禁止选择`);
            count = 0;
          } else {
            // 可以选择，但 by_plan 模式下每种商品只能选1个
            count = 1;
            console.log(`🔥 by_plan选择: 已选${currentSelectedTypesWithoutSelf}种，允许选择，设为1`);
          }
        }
        // 如果 count <= 0，表示取消选择，直接设为0
        if (count <= 0) {
          count = 0;
          console.log(`🔥 by_plan取消选择`);
        }
      }

      // 更新选择数量
      console.log(`🔥 更新商品${product.name}: ${product.selected_count} -> ${count}`);
      product.selected_count = count;

      // 🔥 简化：不再同步到packageData，只在确认时构建最终数据
      console.log(`🔥 updateProductCount完成，当前组状态:`, {
        groupName: group.name,
        products: group.products.map(p => ({
          name: p.name,
          selected_count: p.selected_count
        }))
      });
    }, 100), // 100ms 防抖延迟

    // 修改为不需要显式传递参数的版本，直接使用内部状态
    convertToConfirmData: () => {
      if (!this._state.value.packageData) return {} as ProductPackageVO;

      // 🔥 新增：检查converter.getDefaultProducts的结果
      const defaultProductsFromConverter = this.converter.getDefaultProducts(this._state.value.packageData as any);

      // 🔥 关键改进：直接从响应式数据构建optionalGroups，不依赖packageData中的数据
      const freshOptionalGroups = JSON.stringify(this._state.value.optionalGroupsData);

      // 构建确认数据，使用新构建的optionalGroups
      const confirmData = {
        ...this._state.value.packageData,
        quantity: this._state.value.packageQuantity,
        currentPrice: this._state.value.packageData.currentPrice,
        // 🔥 重要：直接使用响应式数据构建的optionalGroups
        optionalGroups: freshOptionalGroups
      } as unknown as PackageConfirmData;

      // 构建选中商品列表，包含固定商品和可选组商品
      const selectedProducts: any[] = [];

      // 🔥 新增：直接使用converter.getDefaultProducts的结果添加固定商品
      if (defaultProductsFromConverter && defaultProductsFromConverter.length > 0) {
        defaultProductsFromConverter.forEach((product: any) => {
          selectedProducts.push({
            id: product.id,
            name: product.name,
            count: product.count,
            currentPrice: product.currentPrice,
            // 标记为固定商品
            isFixed: true
          });
        });
      }

      // 🔥 添加可选组商品到selectedProducts
      if (isArray(this._state.value.optionalGroupsData) && !isEmpty(this._state.value.optionalGroupsData)) {
        const productVOList = this._state.value.packageData?.productVOList || [];
        // 使用 lodash keyBy 优化商品信息查找
        const productInfoMap = keyBy(productVOList, 'id');

        this._state.value.optionalGroupsData.forEach((group: any) => {
          // 验证组数据的有效性
          if (group && isArray(group.products) && !isEmpty(group.products)) {
            group.products.forEach((product: any) => {
              // 使用 lodash 进行数据验证
              if (product && isString(product.id) && !isEmpty(product.id) && isNumber(product.selected_count) && product.selected_count > 0) {
                // 从productVOList中获取完整商品信息
                const productInfo = productInfoMap[product.id];

                // 🔥 修复：根据可选组类型正确计算商品数量
                let finalCount = product.selected_count;
                if (isOptionalByPlan(group)) {
                  // by_plan类型：显示数量 = count × selected_count
                  const productCount = isNumber(product.count) && product.count > 0 ? product.count : 1;
                  finalCount = productCount * product.selected_count;
                } else {
                  // by_count类型：显示数量 = selected_count
                  finalCount = product.selected_count;
                }

                selectedProducts.push({
                  id: product.id,
                  name: productInfo?.name || product.name || '未知商品',
                  count: finalCount, // 使用修正后的数量
                  currentPrice: isNumber(productInfo?.currentPrice) ? productInfo.currentPrice : isNumber(product.currentPrice) ? product.currentPrice : 0,
                  // 保留原始数据以便调试
                  originalProduct: product,
                  productInfo: productInfo,
                  // 标记为可选商品
                  isOptional: true
                });
              }
            });
          }
        });
      }

      // 🔥 使用 lodash 优化商品去重合并：如果固定商品和可选商品中有相同商品，合并数量
      let finalSelectedProducts: any[] = [];

      if (isArray(selectedProducts) && !isEmpty(selectedProducts)) {
        // 使用 lodash groupBy 按商品ID分组
        const groupedProducts = groupBy(selectedProducts, 'id');

        finalSelectedProducts = Object.values(groupedProducts).map(productGroup => {
          if (productGroup.length === 1) {
            // 只有一个商品，直接返回
            return productGroup[0];
          } else {
            // 有多个相同ID的商品，需要合并
            const mergedProduct = { ...productGroup[0] }; // 以第一个为基础

            // 累加数量
            mergedProduct.count = productGroup.reduce((sum, product) => {
              return sum + (isNumber(product.count) ? product.count : 0);
            }, 0);

            // 优先保留有完整信息的商品数据
            const productWithInfo = productGroup.find(p => p.productInfo && !isEmpty(p.productInfo));
            if (productWithInfo) {
              mergedProduct.name = productWithInfo.name;
              mergedProduct.currentPrice = productWithInfo.currentPrice;
              mergedProduct.productInfo = productWithInfo.productInfo;
            }

            // 合并标记（既是固定又是可选）
            mergedProduct.isFixed = productGroup.some(p => p.isFixed);
            mergedProduct.isOptional = productGroup.some(p => p.isOptional);

            return mergedProduct;
          }
        });
      }

      // 添加已选商品明细到packageDetail
      if (finalSelectedProducts.length > 0) {
        const selectedProductsInfo: any = {
          selectedProducts: finalSelectedProducts,
          optionalGroups: freshOptionalGroups, // 保存完整的可选组数据
          productVOList: this._state.value.packageData.productVOList || [] // 保存商品信息映射
        };

        // 生成配置指纹，用于识别相同配置的套餐
        const configFingerprint = this.generateConfigFingerprint(finalSelectedProducts);
        selectedProductsInfo.configFingerprint = configFingerprint;

        // 序列化packageDetail
        confirmData.packageDetail = JSON.stringify(selectedProductsInfo);

        // 添加一个可识别的字段用于购物车比较
        (confirmData as any).packageConfigId = configFingerprint;
      } else {
        confirmData.packageDetail = JSON.stringify({
          selectedProducts: [],
          optionalGroups: freshOptionalGroups,
          productVOList: this._state.value.packageData.productVOList || []
        });
      }

      return confirmData as unknown as ProductPackageVO;
    },

    // 更新已选商品状态
    updateSelectedProduct: (product: SelectedPackageProduct) => {
      const selectedProducts = this._state.value.selectedPackageProducts;
      const existingIndex = selectedProducts.findIndex(p => p.id === product.id);

      if (existingIndex >= 0) {
        // 更新现有商品
        selectedProducts[existingIndex] = product;
      } else {
        // 添加新商品
        selectedProducts.push(product);
      }
    },

    // 移除已选商品
    removeSelectedProduct: (productId: string) => {
      this._state.value.selectedPackageProducts = this._state.value.selectedPackageProducts.filter(p => p.id !== productId);
    }
  };
}

// 导出组合式函数
export function usePackageDialog(initialData?: ExtendedProductPackageVO): IPackageDialogViewModel {
  console.log('usePackageDialog', initialData);
  return new PackageDialogPresenter(initialData);
}
