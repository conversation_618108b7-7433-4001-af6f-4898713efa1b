import type { IBuyoutBillingState, ProcessedProduct, ProcessedProductList, BuyoutOption, TimeRange, RoomBill } from './viewmodel';
import type { BuyoutPricePlanVO } from '@/modules/room/entity/buyoutPricePlan';
import type { AreaPrice, HolidayPrice } from '@/modules/room/entity/pricePlanConfig';
import { formatYuan } from '@/utils/priceUtils';
import { filterDisplayablePlans, isPlanClickable } from '@/modules/room/components/BuyoutBilling/converter/pricePlanConverter';

export class BuyoutBillingConverter {
  // 创建视图模型初始状态
  static createInitialState(): IBuyoutBillingState {
    return {
      selectedPlan: null
    };
  }

  // 格式化时间范围
  static formatTimeRange(plan: BuyoutPricePlanVO): string {
    // 获取时间配置，提供默认值
    const availableTimeStart = plan.timeConfig?.availableTimeStart || '00:00';
    const availableTimeEnd = plan.timeConfig?.availableTimeEnd || '23:59';

    return `${availableTimeStart}-${availableTimeEnd}`;
  }

  // 格式化显示标签
  static formatDisplayLabel(
    plan: BuyoutPricePlanVO,
    baseRoomFee: number
  ): {
    name: string;
    priceDisplay: string;
    fullLabel: string;
  } {
    const priceText = formatYuan(baseRoomFee);
    const hours = (plan.duration || 0) / 60; // 转换分钟为小时
    return {
      name: plan.name || '', // 方案名称，如"方案A"
      priceDisplay: `${priceText}/${Math.round(hours)}小时`, // 价格/时长，如"268/3小时"
      fullLabel: `${plan.name || ''} - ${priceText}元` // 完整标签，保留原格式
    };
  }

  // 检查当前日期/星期是否在方案范围内（仅检查日期/星期，不检查时间）
  static isWithinDateRange(plan: BuyoutPricePlanVO, currentTime: number): boolean {
    if (!plan.timeConfig) {
      return false;
    }

    const now = new Date(currentTime * 1000);

    // 如果是weekdayConfig，检查当前是否是设置的星期几
    if (plan.timeConfig.timeType === 'weekday' && plan.timeConfig.weekdayConfig) {
      const dayOfWeek = now.getDay(); // 0-6，0代表周日
      // 调整为1-7，1是周一，7是周日
      const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
      const weekDays = plan.timeConfig.weekdayConfig.weeks || [];
      return weekDays.includes(adjustedDayOfWeek);
    }

    // 如果是dateConfig，检查当前日期是否在范围内
    if (plan.timeConfig.timeType === 'date' && plan.timeConfig.dateConfig) {
      const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      return today >= plan.timeConfig.dateConfig.dayStart && today <= plan.timeConfig.dateConfig.dayEnd;
    }

    return false;
  }

  // 检查方案是否可点击（根据当前时间和提前禁用时长）
  static isPlanClickable(plan: BuyoutPricePlanVO, currentTime: number): boolean {
    if (!plan.timeConfig || !plan.timeConfig.availableTimeEnd || !plan.timeConfig.availableTimeStart) return false;

    const now = new Date(currentTime * 1000);
    let currentMinutes = now.getHours() * 60 + now.getMinutes();

    // 解析开始和结束时间
    const [startHour, startMinute] = plan.timeConfig.availableTimeStart.split(':').map(Number);
    const [endHour, endMinute] = plan.timeConfig.availableTimeEnd.split(':').map(Number);

    const startMinutes = startHour * 60 + startMinute;
    let endMinutes = endHour * 60 + endMinute;

    // 新增：如果是全天可用 (start === end)，则始终可点击，忽略 advanceDisableDuration
    if (startMinutes === endMinutes) {
      return true;
    }

    // 处理跨天情况 - 如果结束时间小于开始时间
    // 注意：这里用 <= 是因为如果 start 和 end 相等，上面已经处理了。
    // 如果 end < start，则需要加一天。
    if (endMinutes < startMinutes) {
      endMinutes += 24 * 60; // 加上24小时（1440分钟）
      // 如果当前时间在跨天后的时间段内 (例如 00:00 到 endMinutes)，也需要调整 currentMinutes
      // 使得比较基准一致 (都相对于 startMinutes 所在的"第一天")
      // 例如：plan 是 23:00-06:00, 当前是 01:00 (60), start=1380, end=360+1440=1800
      // disableThreshold = 60 + adv. 如果不调整，60+adv 永远 < 1800
      // 应该将 01:00 也视为"第二天"的时间，即 60 + 1440 = 1500
      // 这样比较 1500 + adv < 1800 才正确
      if (currentMinutes < startMinutes) {
        // 仅当当前时间小于开始时间（即在第二天凌晨）时调整
        currentMinutes += 24 * 60;
      }
    }

    // 计算当前时间加上提前禁用时长后的时间（分钟）
    // 注意：这里的 currentMinutes 可能已经在跨天逻辑中被调整了
    const disableThresholdMinutes = currentMinutes + (plan.advanceDisableDuration || 0);

    // 如果当前时间+提前禁用时长 >= 结束时间，则不可点击
    return disableThresholdMinutes < endMinutes;
  }

  // 检查时间是否在买断范围内 (保留兼容旧版本的逻辑)
  static isWithinBuyoutTimeRange(plan: BuyoutPricePlanVO, currentTime: number): boolean {
    // 首先确认方案是否启用

    if (plan.isEnabled === false) {
      return false;
    }

    // 检查日期或星期是否在范围内
    if (!this.isWithinDateRange(plan, currentTime)) {
      return false;
    }

    return true;
  }

  // 处理产品列表
  static processProductList(planProducts: any, pricePlanSubProductVO: any[]): ProcessedProductList {
    const result: ProcessedProductList = {
      standardProducts: [],
      optionalProducts: { type: 'ByPlan', count: 0, products: [] },
      freeProducts: [],
      optionalFreeProducts: { type: 'ByPlan', count: 0, products: [] }
    };

    if (!planProducts) return result;

    const products = typeof planProducts === 'string' ? JSON.parse(planProducts) : planProducts;
    console.log('[BuyoutBillingConverter] 1-1 products', products);
    // 遍历所有产品组
    if (Array.isArray(products)) {
      products.forEach((group: any) => {
        const isPaid = group.billType === 'paid';
        const isOptional = !!group.optionType;

        // 处理标准付费产品
        if (isPaid && !isOptional) {
          result.standardProducts = this.processProducts(group.products, pricePlanSubProductVO);
        }
        // 处理可选付费产品
        else if (isPaid && isOptional) {
          result.optionalProducts = {
            type: group.optionType === 'by_plan' ? 'ByPlan' : 'ByCount',
            count: group.optionCount || 0,
            products: this.processProducts(group.products, pricePlanSubProductVO, true)
          };
        }
        // 处理标准免费产品
        else if (!isPaid && !isOptional) {
          result.freeProducts = this.processProducts(group.products, pricePlanSubProductVO, false, true);
        }
        // 处理可选免费产品
        else if (!isPaid && isOptional) {
          result.optionalFreeProducts = {
            type: group.optionType === 'by_plan' ? 'ByPlan' : 'ByCount',
            count: group.optionCount || 0,
            products: this.processProducts(group.products, pricePlanSubProductVO, true, true)
          };
        }
      });
    }

    return result;
  }

  // 处理产品
  private static processProducts(products: any[], pricePlanSubProductVO: any[], isOptional: boolean = false, isFree: boolean = false): ProcessedProduct[] {
    if (!Array.isArray(products)) return [];

    // 判断是否已导入productStore
    let productStore: any;
    try {
      // 使用any类型强制转换
      const win = window as any;
      productStore = win.productStore || null;
    } catch (e) {
      console.warn('获取productStore失败，价格信息可能不完整', e);
      productStore = null;
    }

    return products
      .map(product => {
        const productDetails = pricePlanSubProductVO?.find(p => p.id === product.id);
        const details = productDetails || product; // 如果未找到，则退回到传入的product本身

        if (!productDetails) {
          console.warn(`【警告】未在pricePlanSubProductVO中找到商品ID: ${product.id}, 商品名称: ${product.name || '未知'}，将使用传入数据计算`);
        }

        const _productCount = product.count || details.quantity || 0;

        // 确定商品最终是否免费
        // 如果商品所在的组是免费的(isFree=true)，或者API明确指出该商品是免费的(productDetails.isFree=true)
        const finalIsFree = isFree || details.isFree === true;

        let priceValue = 0;

        if (finalIsFree) {
          // 免费商品：price为0，但保留original/currentPrice用于显示打折信息
          const displayPrice = details.currentPrice && details.currentPrice > 0 ? details.currentPrice : details.price && details.price > 0 ? details.price : 0;

          priceValue = 0; // 实际收费价格

          // 覆盖 details.currentPrice 用于后续返回
          details.currentPrice = displayPrice;
        } else {
          // 收费商品的价格确定逻辑
          if (details.currentPrice && details.currentPrice > 0) {
            priceValue = details.currentPrice;
          } else if (details.price && details.price > 0) {
            priceValue = details.price;
          } else if (productStore && productStore.getStandardPrice) {
            try {
              const standardPrice = productStore.getStandardPrice(product.id);
              if (standardPrice && standardPrice > 0) {
                priceValue = standardPrice;
              } else {
                console.warn(`【警告】商品 "${details.name}" (ID: ${product.id}) 从productStore获取价格失败或为0，返回值: ${standardPrice}`);
              }
            } catch (e) {
              console.error(`【错误】商品 "${details.name}" (ID: ${product.id}) 从productStore获取价格时出错:`, e);
            }
          }

          if (priceValue === 0) {
            console.warn(`【警告】收费商品 "${details.name}" (ID: ${product.id}) 未能确定有效价格，最终价格为0。`);
          }
        }

        return {
          id: product.id,
          name: details.name,
          flavor: '',
          count: _productCount,
          unit: details.unit,
          price: priceValue,
          currentPrice: finalIsFree ? details.currentPrice : priceValue, // 免费商品保留原价，收费商品与price一致
          totalAmount: finalIsFree ? 0 : priceValue * (isOptional ? 0 : _productCount),
          isFree: finalIsFree
        };
      })
      .filter((p): p is ProcessedProduct => p !== null);
  }

  // 计算当前日期时间
  static getCurrentDateTime(currentTime: number): { date: string; time: string } {
    const now = new Date(currentTime ? currentTime * 1000 : Date.now());
    return {
      date: `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,
      time: `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`
    };
  }

  // 计算结束时间
  static calculateEndTime(startTime: string, duration: number, planStartTime: string = '00:00', planEndTime: string = '23:59'): string {
    // 确保所有参数都有值
    startTime = startTime || '00:00';
    planStartTime = planStartTime || '00:00';
    planEndTime = planEndTime || '23:59';

    const start = new Date(`1970-01-01T${startTime}:00`);
    let end = new Date(start.getTime() + duration * 60000);

    // 解析计划结束时间
    const [endHour, endMinute] = planEndTime.split(':').map(Number);
    const [startHour, startMinute] = planStartTime.split(':').map(Number);

    // 创建计划的结束时间对象
    let planEnd = new Date(`1970-01-01T${planEndTime}:00`);

    // 处理跨天情况 - 如果计划的结束时间小于开始时间，说明是跨天的时间段
    if (endHour * 60 + endMinute < startHour * 60 + startMinute) {
      planEnd.setDate(planEnd.getDate() + 1);
    }

    // 如果计算出的结束时间超过了计划允许的结束时间，则使用计划结束时间
    if (end > planEnd) end = planEnd;

    return `${String(end.getHours()).padStart(2, '0')}:${String(end.getMinutes()).padStart(2, '0')}`;
  }

  // 计算持续时间（分钟）
  static calculateDurationInMinutes(startTime: string, endTime: string): number {
    // 确保参数有值
    startTime = startTime || '00:00';
    endTime = endTime || '23:59';

    const start = new Date(`1970-01-01T${startTime}:00`);

    // 解析开始和结束时间
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    // 创建结束时间对象
    let end = new Date(`1970-01-01T${endTime}:00`);

    // 处理跨天情况 - 如果结束时间小于开始时间，说明是跨天的时间段
    if (endHour * 60 + endMinute < startHour * 60 + startMinute) {
      end.setDate(end.getDate() + 1);
    }

    return Math.round((end.getTime() - start.getTime()) / 60000);
  }

  // 新增：检查当前时间是否在方案的可用时间段内
  static isWithinAvailableTime(plan: BuyoutPricePlanVO, currentTime: number): boolean {
    if (!plan.timeConfig || !plan.timeConfig.availableTimeStart || !plan.timeConfig.availableTimeEnd) {
      // 如果没有配置时间，根据业务需求决定，这里假设默认在范围内
      console.warn(`方案 ${plan.id} 缺少有效的 availableTimeStart 或 availableTimeEnd 配置，默认视为在时间范围内`);
      return true;
    }

    const now = new Date(currentTime * 1000);
    const currentMinutes = now.getHours() * 60 + now.getMinutes();

    const [startHour, startMinute] = plan.timeConfig.availableTimeStart.split(':').map(Number);
    const [endHour, endMinute] = plan.timeConfig.availableTimeEnd.split(':').map(Number);

    let startMinutes = startHour * 60 + startMinute;
    let endMinutes = endHour * 60 + endMinute;

    // 处理跨天情况 (e.g., 23:00 - 06:00) 或全天 (e.g., 06:00 - 06:00)
    if (endMinutes <= startMinutes) {
      // 如果是全天 (06:00 - 06:00)，直接返回 true
      if (startMinutes === endMinutes) {
        return true;
      }
      // 如果是跨天，当前时间需 >= 开始时间 或 < 结束时间（视为次日）
      if (currentMinutes >= startMinutes || currentMinutes < endMinutes) {
        return true;
      }
    } else {
      // 不跨天情况 (e.g., 13:00 - 17:00)
      // 当前时间需 >= 开始时间 且 < 结束时间 (左闭右开区间)
      if (currentMinutes >= startMinutes && currentMinutes < endMinutes) {
        return true;
      }
    }

    return false;
  }
}
