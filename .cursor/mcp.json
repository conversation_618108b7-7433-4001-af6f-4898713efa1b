{"mcpServers": {"vue-mcp": {"url": "http://localhost:5173/__mcp/sse"}, "erp api": {"command": "npx", "args": ["-y", "apifox-mcp-server@latest", "--project=5553010"], "env": {"APIFOX_ACCESS_TOKEN": "APS-fdciTr8a6Fc1wawGtQwyuzcrR3t2Ka4B"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "browser-tools": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp"]}, "mastergo-magic-mcp": {"command": "npx", "args": ["-y", "@mastergo/magic-mcp", "--token=mg_ad00f9f70f0b48adb8b8066496d5984e", "--url=https://mastergo.com"], "env": {"NPM_CONFIG_REGISTRY": "https://registry.npmjs.org/"}}}}