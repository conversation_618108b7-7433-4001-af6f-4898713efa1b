/**
 * 商品账单和点单相关工具函数
 */
import { nextTick } from 'vue';
import { getNormalizedOptionType, isOptionalByPlan, autoSelectOptionalGroup as utilAutoSelectOptionalGroup } from '@/utils/productPackageUtils';
import _ from 'lodash';
import { OpenTableConverter } from './converter';
import type { ProductEntity } from '../../entity/OpenTableEntity';

/**
 * 消费模式枚举
 */
export enum ConsumptionMode {
  BUYOUT = 'buyout', // 包厢买断
  HOURLY = 'hourly', // 按时收费
  FREE = 'free', // 免费
  STANDARD = 'standard' // 标准
}

/**
 * 订单类型枚举
 */
export enum OrderType {
  OPEN = 'open', // 开台
  PRE = 'pre', // 预定
  STAGE = 'stage' // 分时段
}

/**
 * 房间状态枚举
 */
export enum RoomStatus {
  FREE = 'free', // 空闲
  USING = 'using', // 使用中
  RESERVED = 'reserved', // 已预订
  LOCKED = 'locked' // 锁定
}

/**
 * 时间范围接口
 */
export interface TimeRange {
  start: string; // 开始时间
  end: string; // 结束时间
  duration?: number; // 单位：分钟
  [key: string]: unknown;
}

/**
 * 产品接口定义
 */
export interface Product {
  id: string;
  name?: string;
  productName?: string;
  price: number;
  currentPrice: number;
  unit: string;
  quantity?: number;
  totalAmount?: number;
  flavors?: string;
  isPackage?: boolean;
  packageDetail?: string | PackageDetail;
  isFree?: boolean;
  tempQuantity?: number;
  count?: number;
  originalPrice?: number;
  [key: string]: unknown;
}

/**
 * 套餐详情接口
 */
export interface PackageDetail {
  packageProducts?: string | PackageProduct[];
  productVOList?: ProductVO[];
  selectedProducts?: SelectedProduct[];
  detailString?: string;
  optionalConfig?: { count?: number };
  [key: string]: unknown;
}

/**
 * 套餐产品接口
 */
export interface PackageProduct {
  id: string;
  count: number;
  [key: string]: unknown;
}

/**
 * 产品VO接口
 */
export interface ProductVO {
  id: string;
  name: string;
  [key: string]: unknown;
}

/**
 * 已选产品接口
 */
export interface SelectedProduct {
  id: string;
  name: string;
  count: number;
  [key: string]: unknown;
}

/**
 * 可选组产品接口
 */
export interface OptionalProduct extends Product {
  isFree: boolean;
  count?: number;
  [key: string]: unknown;
}

/**
 * 可选组接口
 */
export interface OptionalGroupData {
  products: OptionalProduct[];
  count?: number;
  optionCount?: number;
  selectedQuantity?: number;
  type?: string;
  optionType?: string;
  isFree?: boolean;
  [key: string]: unknown;
}

/**
 * 房间账单接口
 */
export interface RoomBill {
  roomFee?: number; // 房费
  serviceFee?: number; // 服务费
  minimumCharge?: number; // 最低消费
  [key: string]: unknown;
}

/**
 * 账单数据接口
 */
export interface MarketBill {
  standardProducts?: Product[];
  freeProducts?: Product[];
  optionalProducts?: OptionalGroupData;
  optionalFreeProducts?: OptionalGroupData;
  [key: string]: unknown;
}

/**
 * 完整账单接口
 */
export interface CompleteBill {
  roombill: RoomBill;
  marketBill: MarketBill;
  consumptionMode: ConsumptionMode;
  minimumCharge: number;
  timeRange: TimeRange;
  [key: string]: unknown;
}

/**
 * 处理后的账单数据接口
 */
export interface ProcessedMarketBill {
  value: {
    outOrderProducts?: Product[];
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

// 表格行项目接口定义
export interface TableRowItem {
  id: string;
  productName: string;
  parentId?: string;
  quantity: number;
  selectedQuantity?: number;
  unit: string;
  price: number;
  currentPrice: number;
  totalAmount: number;
  uniqueId: string;
  isDetail?: boolean;
  isOptionalGroup?: boolean;
  isPackage?: boolean;
  isFree?: boolean;
  isInitialOrder?: boolean;
  packageDetail?: unknown;
  optionType?: string;
  type?: string;
  optionCount?: number;
  optionalProducts?: OptionalProduct[];
  details?: string;
  [key: string]: unknown;
}

/**
 * 获取商品原价的通用函数
 *
 * @param product 商品对象
 * @returns 商品原价
 */
export const getProductOriginalPrice = (product: OptionalProduct): number => {
  // 记录原始数据，帮助调试
  // console.log(`[价格数据] 获取商品 "${product.name || product.productName}" 原价，API返回数据:`, {
  //   price: product.price,
  //   currentPrice: product.currentPrice,
  //   originalPrice: product.originalPrice || '未定义',
  //   isFree: product.isFree
  // });

  // 1. 优先使用后端返回的currentPrice（应该包含原价信息）
  if (product.currentPrice !== undefined && product.currentPrice > 0) {
    // console.log(`[价格数据] 使用API返回的currentPrice: ${product.currentPrice}`);
    return product.currentPrice;
  }

  // 2. 其次使用originalPrice字段（如果存在）
  if (typeof product.originalPrice === 'number' && product.originalPrice > 0) {
    // console.log(`[价格数据] 使用originalPrice: ${product.originalPrice}`);
    return product.originalPrice;
  }

  // 3. 再次尝试使用price字段（如果大于0）
  if (product.price !== undefined && product.price > 0) {
    // console.log(`[价格数据] 使用price: ${product.price}`);
    return product.price;
  }

  // 4. 尝试从产品服务或缓存获取价格（具体实现需要另外添加）
  // TODO: 集成产品服务API调用，获取商品标准价格
  // const standardPrice = await productService.getStandardPrice(product.id);
  // if (standardPrice > 0) return standardPrice;

  // 无法获取价格时记录并返回0
  console.warn(`[价格数据] 警告: 无法获取商品 "${product.name || product.productName}" (id=${product.id}) 的原价，将使用0`);
  return 0;
};

// 生成详情文本的辅助函数
export const generateDetailsText = (item: OptionalGroupData | TableRowItem): string => {
  if (!item) return '';

  // 获取选择模式
  const normalizedType = getNormalizedOptionType(item);
  const isByPlan = normalizedType === 'ByPlan';

  // 计算选中的商品总数量 - 始终使用总数量
  let totalSelectedQuantity = 0;
  if (item.optionalProducts && Array.isArray(item.optionalProducts)) {
    totalSelectedQuantity = item.optionalProducts.reduce((sum: number, p: OptionalProduct) => sum + (p.quantity || 0), 0);
  }

  // 计算选择的不同商品种类数（仅用于ByPlan模式的显示）
  let selectedTypeCount = 0;
  if (item.optionalProducts && Array.isArray(item.optionalProducts) && isByPlan) {
    selectedTypeCount = item.optionalProducts.filter((p: OptionalProduct) => (p.quantity || 0) > 0).length;
  }

  // 根据是否为免费商品构建不同文本
  const itemType = item.isFree ? '赠品' : '商品';
  const optionCount = item.optionCount || 0;

  if (isByPlan) {
    return `可选 ${optionCount} 种${itemType}，已选 ${selectedTypeCount} 种 (共${totalSelectedQuantity}件)`;
  } else {
    return `可选 ${optionCount} 件${itemType}，已选 ${totalSelectedQuantity} 件`;
  }
};

/**
 * 处理商品账单和点单逻辑，生成表格数据
 * @param marketBill 商品账单数据
 * @param processedMarketBill 处理后的商品账单数据
 * @param autoSelectCallback 自动选择可选组的回调函数
 * @returns 处理后的表格数据
 */
export const processProductBillData = (marketBill: MarketBill, processedMarketBill: ProcessedMarketBill, autoSelectCallback?: () => void): TableRowItem[] => {
  console.log('=== processProductBillData 开始 ===');
  console.log('marketBill:', marketBill);
  console.log('processedMarketBill:', processedMarketBill);

  if (!processedMarketBill || !processedMarketBill.value) {
    console.log('processedMarketBill 为空，返回空数组');
    return [];
  }

  const result: TableRowItem[] = [];
  const _marketBill = marketBill;

  // 处理商品数据
  if (_marketBill) {
    // 添加标准商品
    if (_marketBill.standardProducts && _marketBill.standardProducts.length > 0) {
      _marketBill.standardProducts.forEach((item: Product, index: number) => {
        // 确保price字段存在
        const price = item.price || 0;

        // 检查是否为套餐
        const isPackage = !!item.isPackage;
        if (isPackage) {
          // 创建套餐项
          const packageItem: TableRowItem = {
            ...item,
            productName: item.name || item.productName || '',
            price: item.price,
            currentPrice: item.currentPrice,
            uniqueId: `standard-${index}`,
            isPackage: isPackage, // 明确设置套餐标识
            packageDetail: item.packageDetail, // 保留套餐详情
            isInitialOrder: true, // 开台方案商品标记
            quantity: item.quantity || 1,
            totalAmount: item.totalAmount || item.price * (item.quantity || 1),
            unit: item.unit || '份'
          };

          result.push(packageItem);

          // 添加套餐明细项
          if (item.packageDetail) {
            try {
              // 尝试解析packageDetail
              const packageDetail: PackageDetail =
                typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : (item.packageDetail as PackageDetail);

              // 生成套餐详情文本
              const detailTexts: string[] = [];

              // 处理固定商品
              if (packageDetail.packageProducts) {
                const defaultProducts: PackageProduct[] =
                  typeof packageDetail.packageProducts === 'string'
                    ? JSON.parse(packageDetail.packageProducts)
                    : (packageDetail.packageProducts as PackageProduct[]);

                if (Array.isArray(defaultProducts)) {
                  defaultProducts.forEach(product => {
                    // 查找产品详情
                    const productInfo = packageDetail.productVOList?.find((p: ProductVO) => p.id === product.id);
                    if (productInfo) {
                      detailTexts.push(`${productInfo.name}×${product.count || 1}`);
                    }
                  });
                }
              }

              // 处理已选商品
              if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts)) {
                packageDetail.selectedProducts.forEach((product: SelectedProduct) => {
                  if (product.count > 0) {
                    detailTexts.push(`${product.name}×${product.count || 1}`);
                  }
                });
              }

              // 设置detailString
              if (detailTexts.length > 0) {
                const detailString = detailTexts.join('，');

                // 更新packageDetail
                if (typeof packageItem.packageDetail === 'string') {
                  try {
                    const detailObj = JSON.parse(packageItem.packageDetail);
                    detailObj.detailString = detailString;
                    packageItem.packageDetail = JSON.stringify(detailObj);
                  } catch (e) {
                    packageItem.packageDetail = {
                      detailString
                    };
                  }
                } else if (typeof packageItem.packageDetail === 'object' && packageItem.packageDetail !== null) {
                  (packageItem.packageDetail as PackageDetail).detailString = detailString;
                }

                // 创建一个合并的明细行
                result.push({
                  id: `${item.id}-detail`,
                  productName: detailString,
                  parentId: item.id,
                  quantity: 1, // 数量设为1，因为这是一个合并项
                  unit: '',
                  price: 0,
                  currentPrice: 0,
                  totalAmount: 0,
                  isDetail: true,
                  isInitialOrder: true, // 开台方案商品的明细行
                  uniqueId: `standard-${index}-detail`
                });
              }
            } catch (e) {
              console.error('解析套餐详情失败:', e);
            }
          }
        } else {
          // 普通商品，直接添加
          result.push({
            ...item,
            productName: item.name || item.productName || '',
            price: item.price,
            currentPrice: item.currentPrice,
            uniqueId: `standard-${index}`,
            isInitialOrder: true, // 开台方案商品标记
            quantity: item.quantity || 1,
            totalAmount: item.totalAmount || item.price * (item.quantity || 1),
            unit: item.unit || '份'
          });
        }
      });
    }

    // 添加可选组商品（如果存在）
    if (_marketBill.optionalProducts && _marketBill.optionalProducts.products && _marketBill.optionalProducts.products.length > 0) {
      // 可选组商品合并为一行显示
      const optionalGroup = _marketBill.optionalProducts;
      const groupProducts = optionalGroup.products || [];

      // 先定义变量，稍后使用 processedProducts 再计算总价
      let totalPrice = 0;

      // 获取已选择的商品总数量（如果存在selectedQuantity则直接使用）
      const totalSelectedQuantity =
        optionalGroup.selectedQuantity !== undefined
          ? optionalGroup.selectedQuantity
          : groupProducts.length > 0
            ? _.sumBy(groupProducts, p => (p.quantity !== undefined && p.quantity !== null ? p.quantity : p.count || 0))
            : 0;

      // 使用数据中的 count 字段，不再硬编码
      const optionCount = optionalGroup.count || 0;

      // 处理服务端type和前端optionType的差异
      const normalizedType = getNormalizedOptionType(optionalGroup);

      // 根据optionType生成不同的详情文本
      let detailsText = '';

      if (normalizedType === 'ByPlan') {
        // 计算选择的商品种类数
        const selectedTypeCount = _.filter(groupProducts, (p: OptionalProduct) => (p.quantity || 0) > 0).length;
        detailsText = `可选 ${optionCount} 种商品，已选 ${selectedTypeCount} 种 (共${totalSelectedQuantity}件)`;
      } else {
        detailsText = `可选 ${optionCount} 件商品，已选 ${totalSelectedQuantity} 件`;
      }

      // 确保每个产品有正确的属性
      const processedProducts = groupProducts.map((product: OptionalProduct) => {
        // 查找商品的默认原价
        const defaultPrice = getProductOriginalPrice(product);

        const _quantity = product.quantity !== undefined && product.quantity !== null ? product.quantity : product.count || 0;
        const productData: OptionalProduct = {
          ...product,
          isFree: false, // 确保每个产品都有isFree属性
          quantity: _quantity,
          // 使用getProductOriginalPrice获取原价，不再依赖硬编码映射表
          currentPrice: getProductOriginalPrice(product),
          // 普通商品有实际金额
          totalAmount: product.totalAmount || (getProductOriginalPrice(product) || product.price || 0) * _quantity,
          // 普通商品保留原始price
          price: product.price || 0
        };

        // 如果是by_count类型，移除count字段，因为该字段在此模式下无效
        if (normalizedType === 'ByCount' && 'count' in productData) {
          delete productData.count;
        }

        // 调试日志，帮助排查问题
        // console.log(`可选商品处理后 ${productData.name}: price=${productData.price}, currentPrice=${productData.currentPrice}, isFree=${productData.isFree}`);

        return productData;
      });

      // 重新计算总价，确保使用已经纠正数量的 processedProducts 数据
      totalPrice = _.sumBy(processedProducts, p => p.totalAmount || 0);

      // 创建可选组商品行
      const optionalGroupItem: TableRowItem = {
        id: 'optional-group',
        productName: '可选商品',
        quantity: optionCount, // 这里quantity代表可选总数量
        selectedQuantity: totalSelectedQuantity, // 使用计算的实际选择数量，不强制设为0
        totalAmount: totalPrice,
        // 保留子商品的原始价格信息
        currentPrice: processedProducts.length > 0 ? processedProducts[0].currentPrice || processedProducts[0].price || 0 : 0,
        price: 0,
        unit: '组',
        uniqueId: 'optional-group',
        isOptionalGroup: true,
        isInitialOrder: true, // 可选组是开台方案的一部分
        optionType: optionalGroup.type || optionalGroup.optionType, // 优先使用服务端的type字段
        type: optionalGroup.type, // 保留服务端的type字段
        optionCount: optionCount, // 使用实际数据中的 count
        optionalProducts: processedProducts,
        details: detailsText
      };

      result.push(optionalGroupItem);

      // 添加可选组明细行
      if (optionalGroup.products && optionalGroup.products.length > 0) {
        const selectedProducts = _.filter(optionalGroup.products, (p: OptionalProduct) => (p.quantity || 0) > 0);
        if (selectedProducts.length > 0) {
          // 构建明细文本
          const detailItems = selectedProducts.map((product: OptionalProduct) => {
            // 对于by_plan类型，需要考虑商品的count字段
            const displayQuantity = normalizedType === 'ByPlan' && product.count ? product.count * (product.quantity || 1) : product.quantity;
            return `${product.name || product.productName}×${displayQuantity}`;
          });
          const detailString = detailItems.join('，');

          // 创建明细行
          result.push({
            id: `optional-group-detail`,
            productName: detailString,
            parentId: 'optional-group',
            quantity: 1,
            unit: '',
            price: 0,
            // 明细行不需要显示价格，但为了保持数据完整性，使用父项的currentPrice
            currentPrice: 0,
            totalAmount: 0,
            isDetail: true,
            isInitialOrder: true, // 可选组明细行也是开台方案的一部分
            uniqueId: `optional-group-detail`
          });
        }
      }
    }

    // 添加免费商品
    if (_marketBill.freeProducts && _marketBill.freeProducts.length > 0) {
      _marketBill.freeProducts.forEach((item: Product, index: number) => {
        // 设置price为0（赠品）
        const price = 0;
        result.push({
          ...item,
          productName: item.name || item.productName || '',
          price: price,
          // 不覆盖currentPrice，保留原始值
          currentPrice: item.currentPrice,
          isFree: true,
          uniqueId: `free-${index}`,
          isInitialOrder: true, // 开台方案商品标记
          quantity: item.quantity || 1,
          totalAmount: 0, // 赠品总金额为0
          unit: item.unit || '份'
        });
      });
    }

    // 添加可选免费组商品（如果存在）
    if (_marketBill.optionalFreeProducts && _marketBill.optionalFreeProducts.products && _marketBill.optionalFreeProducts.products.length > 0) {
      // 可选免费组商品合并为一行显示
      const optionalFreeGroup = _marketBill.optionalFreeProducts;
      const groupProducts = optionalFreeGroup.products || [];

      // 使用数据中的 count 字段，不再硬编码
      const optionCount = optionalFreeGroup.count || 0;

      // 处理服务端type和前端optionType的差异
      const normalizedType = getNormalizedOptionType(optionalFreeGroup);

      // 确保每个产品有正确的属性
      const processedProducts = groupProducts.map((product: OptionalProduct) => {
        // 查找商品的默认原价
        const defaultPrice = getProductOriginalPrice(product);

        const _quantity = product.quantity !== undefined && product.quantity !== null ? product.quantity : product.count || 0;
        const productData: OptionalProduct = {
          ...product,
          isFree: true, // 免费可选组中的商品标记为赠品
          quantity: _quantity,
          // 使用getProductOriginalPrice获取原价，不再依赖硬编码映射表
          currentPrice: getProductOriginalPrice(product),
          // 普通商品有实际金额
          totalAmount: product.totalAmount || (getProductOriginalPrice(product) || product.price || 0) * _quantity,
          // 普通商品保留原始price
          price: product.price || 0
        };

        // 如果是by_count类型，移除count字段，因为该字段在此模式下无效
        if (normalizedType === 'ByCount' && 'count' in productData) {
          delete productData.count;
        }

        // 调试日志，帮助排查问题
        // console.log(
        //   `处理${productData.isFree ? '赠品' : '商品'} "${productData.name}": 初始price=${productData.price}, currentPrice=${productData.currentPrice}, 获取的原价=${defaultPrice}`
        // );

        return productData;
      });

      // 获取已选择的商品总数量（如果存在selectedQuantity则直接使用）
      const totalSelectedQuantity =
        optionalFreeGroup.selectedQuantity !== undefined
          ? optionalFreeGroup.selectedQuantity
          : processedProducts.length > 0
            ? _.sumBy(processedProducts, p => (p.quantity !== undefined && p.quantity !== null ? p.quantity : p.count || 0))
            : 0;

      // 根据optionType生成不同的详情文本
      let detailsText = '';

      if (normalizedType === 'ByPlan') {
        // 计算选择的商品种类数
        const selectedTypeCount = _.filter(processedProducts, (p: OptionalProduct) => (p.quantity || 0) > 0).length;
        detailsText = `可选 ${optionCount} 种赠品，已选 ${selectedTypeCount} 种 (共${totalSelectedQuantity}件)`;
      } else {
        detailsText = `可选 ${optionCount} 件赠品，已选 ${totalSelectedQuantity} 件`;
      }

      // 创建可选免费组商品行
      const optionalFreeGroupItem: TableRowItem = {
        id: 'optional-free-group',
        productName: '可选赠品',
        quantity: optionCount, // 这里quantity代表可选总数量
        selectedQuantity: totalSelectedQuantity, // 使用计算的实际选择数量，不强制设为0
        totalAmount: 0, // 总金额为0是合理的，因为是免费商品
        price: 0, // 价格为0也是合理的
        // 使用第一个子商品的原价作为组的currentPrice，方便UI显示，如果没有子商品则为0
        currentPrice: processedProducts.length > 0 ? processedProducts[0].currentPrice || 0 : 0,
        unit: '组',
        uniqueId: 'optional-free-group',
        isOptionalGroup: true,
        isFree: true,
        isInitialOrder: true, // 免费可选组也是开台方案的一部分
        optionType: optionalFreeGroup.type || optionalFreeGroup.optionType, // 优先使用服务端的type字段
        type: optionalFreeGroup.type, // 保留服务端的type字段
        optionCount: optionCount, // 使用实际数据中的 count
        optionalProducts: processedProducts,
        details: detailsText
      };
      result.push(optionalFreeGroupItem);

      // 添加免费可选组明细行
      if (optionalFreeGroup.products && optionalFreeGroup.products.length > 0) {
        const selectedProducts = _.filter(optionalFreeGroup.products, (p: OptionalProduct) => (p.quantity || 0) > 0);
        if (selectedProducts.length > 0) {
          // 构建明细文本
          const detailItems = selectedProducts.map((product: OptionalProduct) => {
            // 对于by_plan类型，需要考虑商品的count字段
            const displayQuantity = normalizedType === 'ByPlan' && product.count ? product.count * (product.quantity || 1) : product.quantity;
            return `${product.name || product.productName}×${displayQuantity}`;
          });
          const detailString = detailItems.join('，');

          // 创建明细行
          result.push({
            id: `optional-free-group-detail`,
            productName: detailString,
            parentId: 'optional-free-group',
            quantity: 1,
            unit: '',
            price: 0,
            // 明细行不需要显示价格，但为了保持数据完整性，使用父项的currentPrice
            currentPrice: 0,
            totalAmount: 0,
            isDetail: true,
            isInitialOrder: true, // 可选组明细行也是开台方案的一部分
            uniqueId: `optional-free-group-detail`
          });
        }
      }
    }
  }

  // 添加账单外商品（从购物车添加的商品）
  console.log('检查 outOrderProducts:', processedMarketBill.value.outOrderProducts);
  if (processedMarketBill.value.outOrderProducts && processedMarketBill.value.outOrderProducts.length > 0) {
    console.log('处理 outOrderProducts，数量:', processedMarketBill.value.outOrderProducts.length);
    processedMarketBill.value.outOrderProducts.forEach((item: Product, index: number) => {
      // 普通商品或赠品（outOrderProducts 里可能存在赠品）
      const isFree = item.isFree === true;

      // 检查是否为套餐
      const isPackage = !!item.isPackage;
      if (isPackage) {
        // 创建套餐项
        const packageItem: TableRowItem = {
          ...item,
          productName: item.name || item.productName || '',
          price: isFree ? 0 : item.price,
          currentPrice: item.currentPrice,
          isFree,
          uniqueId: `outorder-${index}`,
          isPackage: isPackage, // 明确设置套餐标识
          packageDetail: item.packageDetail, // 保留套餐详情
          isInitialOrder: false, // 从购物车添加的商品标记为非开台方案商品
          quantity: item.quantity || 1,
          totalAmount: isFree ? 0 : item.totalAmount || item.price * (item.quantity || 1),
          unit: item.unit || '份'
        };

        result.push(packageItem);

        // 添加套餐明细项
        if (item.packageDetail) {
          try {
            // 尝试解析packageDetail
            const packageDetail: PackageDetail =
              typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : (item.packageDetail as PackageDetail);

            // 生成套餐详情文本
            let detailString = '';

            // 优先使用已有的detailString
            if (packageDetail.detailString) {
              detailString = packageDetail.detailString;
            } else {
              // 如果没有detailString，尝试从其他字段生成
              const detailTexts: string[] = [];

              // 处理固定商品
              if (packageDetail.packageProducts) {
                const defaultProducts: PackageProduct[] =
                  typeof packageDetail.packageProducts === 'string'
                    ? JSON.parse(packageDetail.packageProducts)
                    : (packageDetail.packageProducts as PackageProduct[]);

                if (Array.isArray(defaultProducts)) {
                  defaultProducts.forEach(product => {
                    // 查找产品详情
                    const productInfo = packageDetail.productVOList?.find((p: ProductVO) => p.id === product.id);
                    if (productInfo) {
                      detailTexts.push(`${productInfo.name}×${product.count || 1}`);
                    }
                  });
                }
              }

              // 处理已选商品
              if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts)) {
                packageDetail.selectedProducts.forEach((product: SelectedProduct) => {
                  if (product.count > 0) {
                    detailTexts.push(`${product.name}×${product.count || 1}`);
                  }
                });
              }

              detailString = detailTexts.join('，');
            }

            // 如果有明细文本，创建明细行
            if (detailString) {
              // 更新packageDetail中的detailString
              if (typeof packageItem.packageDetail === 'string') {
                try {
                  const detailObj = JSON.parse(packageItem.packageDetail);
                  detailObj.detailString = detailString;
                  packageItem.packageDetail = JSON.stringify(detailObj);
                } catch (e) {
                  packageItem.packageDetail = {
                    detailString
                  };
                }
              } else if (typeof packageItem.packageDetail === 'object' && packageItem.packageDetail !== null) {
                (packageItem.packageDetail as PackageDetail).detailString = detailString;
              }

              // 创建一个合并的明细行
              result.push({
                id: `${item.id}-detail`,
                productName: detailString,
                parentId: item.id,
                quantity: 1, // 数量设为1，因为这是一个合并项
                unit: '',
                price: 0,
                currentPrice: 0,
                totalAmount: 0,
                isDetail: true,
                isInitialOrder: false, // 从购物车添加的商品标记为非开台方案商品
                uniqueId: `outorder-${index}-detail`
              });
            }
          } catch (e) {
            console.error('解析套餐详情失败:', e);
          }
        }
      } else {
        // 普通商品，直接添加
        result.push({
          ...item,
          price: isFree ? 0 : item.price,
          currentPrice: item.currentPrice,
          isFree,
          uniqueId: `outorder-${index}`,
          isInitialOrder: false, // 从购物车添加的商品标记为非开台方案商品
          productName: item.name || item.productName || '',
          quantity: item.quantity || 1,
          totalAmount: isFree ? 0 : item.totalAmount || item.price * (item.quantity || 1),
          unit: item.unit || '份'
        });
      }
    });
  }

  // 数据生成完成后，确保自动选择已应用
  nextTick(() => {
    // 如果有可选组但未选择商品，触发自动选择
    const hasOptionalGroup = result.some(item => item.isOptionalGroup);
    const hasNoSelection = result.some(
      item =>
        item.isOptionalGroup &&
        (item.selectedQuantity === undefined || item.selectedQuantity === 0) &&
        item.optionalProducts &&
        item.optionalProducts.every((p: OptionalProduct) => !p.quantity || p.quantity === 0)
    );

    if (hasOptionalGroup && hasNoSelection && autoSelectCallback) {
      autoSelectCallback();
    }
  });

  console.log('=== processProductBillData 结束 ===');
  console.log('最终结果 result:', result);
  console.log('开台方案商品数量:', result.filter(item => item.isInitialOrder === true).length);
  console.log('额外点单商品数量:', result.filter(item => item.isInitialOrder === false).length);

  return result;
};

/**
 * 初始化可选组商品数量为0
 * 确保所有可选组商品的quantity值被正确初始化，保留已有数量
 * @param marketBill 商品账单数据
 */
export const initializeZeroQuantities = (marketBill: MarketBill): void => {
  if (!marketBill) {
    return;
  }

  // 初始化付费可选组商品数量
  if (marketBill.optionalProducts && marketBill.optionalProducts.products) {
    // 初始化所有产品数量 - 但只初始化undefined/null的商品，保留已有数量
    marketBill.optionalProducts.products.forEach((product: OptionalProduct) => {
      // 关键：首次加载时，确保删除tempQuantity字段
      if ('tempQuantity' in product) {
        delete product.tempQuantity;
      }

      // 只有当quantity未定义时才初始化为0
      if (product.quantity === undefined || product.quantity === null) {
        product.quantity = 0;
      }
    });

    // 更新selectedQuantity为当前选择的总数，而不是重置为0
    if (marketBill.optionalProducts) {
      const totalQuantity = _.sumBy(marketBill.optionalProducts.products, (product: OptionalProduct) => product.quantity || 0);
      (marketBill.optionalProducts as any).selectedQuantity = totalQuantity;
    }
  }

  // 初始化免费可选组商品数量 - 类似的逻辑
  if (marketBill.optionalFreeProducts && marketBill.optionalFreeProducts.products) {
    // 初始化所有产品数量 - 但只初始化undefined/null的商品，保留已有数量
    marketBill.optionalFreeProducts.products.forEach((product: OptionalProduct) => {
      // 关键：首次加载时，确保删除tempQuantity字段
      if ('tempQuantity' in product) {
        delete product.tempQuantity;
      }

      // 只有当quantity未定义时才初始化为0
      if (product.quantity === undefined || product.quantity === null) {
        product.quantity = 0;
      }
    });

    // 更新selectedQuantity为当前选择的总数，而不是重置为0
    if (marketBill.optionalFreeProducts) {
      const totalQuantity = _.sumBy(marketBill.optionalFreeProducts.products, (product: OptionalProduct) => product.quantity || 0);
      (marketBill.optionalFreeProducts as any).selectedQuantity = totalQuantity;
    }
  }
};

// 处理可选组商品
const processOptionalGroupProducts = (group: OptionalGroupData, isFree: boolean) => {
  if (!group || !group.products) {
    return [];
  }

  const normalizedType = getNormalizedOptionType(group);
  // console.log(`处理${isFree ? '赠品' : '商品'}组，类型: ${normalizedType}, 商品数量: ${group.products.length}`);

  return group.products.map((product: OptionalProduct) => {
    // 保存原始价格信息用于日志
    const originalPriceInfo = {
      name: product.name || product.productName,
      originalPrice: product.price,
      originalCurrentPrice: product.currentPrice
    };

    // 查找商品的默认原价
    const defaultPrice = getProductOriginalPrice(product);

    // 调试日志
    // console.log(
    //   `处理${isFree ? '赠品' : '商品'} "${product.name}": 初始price=${product.price}, currentPrice=${product.currentPrice}, 获取的原价=${defaultPrice}`
    // );

    const _quantity = product.quantity !== undefined && product.quantity !== null ? product.quantity : product.count || 0;
    const productData: OptionalProduct = {
      ...product,
      isFree: isFree, // 设置是否为免费商品
      quantity: _quantity,
      // 使用getProductOriginalPrice获取原价，不再依赖硬编码映射表
      currentPrice: getProductOriginalPrice(product),
      // 普通商品有实际金额
      totalAmount: product.totalAmount || (getProductOriginalPrice(product) || product.price || 0) * _quantity,
      // 普通商品保留原始price
      price: product.price || 0
    };

    // 免费商品的情况
    if (isFree) {
      // 免费商品实际价格为0，但保留原价用于显示
      productData.totalAmount = 0;
      productData.price = 0;
    } else {
      // 正常商品保留原价和总金额
      productData.price = product.price || 0;
      productData.totalAmount = product.totalAmount || productData.price * (productData.quantity || 0);
    }

    // 如果是by_count类型，移除count字段，因为该字段在此模式下无效
    if (normalizedType === 'ByCount' && 'count' in productData) {
      delete productData.count;
    }

    // 调试日志，帮助排查问题
    // console.log(
    //   `${isFree ? '赠品' : '商品'}处理后 "${productData.name}": price(实际价格)=${productData.price}, currentPrice(原价)=${productData.currentPrice}, isFree=${productData.isFree}`
    // );

    return productData;
  });
};

/**
 * 验证单个可选组的选择是否有效
 * @param optionalGroup 可选组数据
 * @returns boolean 选择是否有效
 */
export function validateOptionalGroup(optionalGroup: OptionalGroupData | any): boolean {
  const products: OptionalProduct[] = optionalGroup.products || [];
  const requiredCount: number = optionalGroup.count || optionalGroup.optionCount || 0;

  // 如果要求选择数量大于0，但可选组内没有任何商品，则无法满足，视为无效
  if (requiredCount > 0 && products.length === 0) {
    return false;
  }

  // 如果不要求选择数量 (requiredCount === 0)，则无论有无商品或有无选择，均视为有效
  if (requiredCount === 0) {
    return true;
  }

  const totalSelectedQuantity = _.sumBy(products, (product: OptionalProduct) => product.quantity || 0);
  const selectedTypeCount = _.filter(products, (product: OptionalProduct) => (product.quantity || 0) > 0).length;

  if (isOptionalByPlan(optionalGroup)) {
    // 按方案：需要选择的种类数 大于等于 要求数量
    return selectedTypeCount >= requiredCount;
  } else {
    // 按数量（默认）：需要选择的总数量 大于等于 要求数量
    return totalSelectedQuantity >= requiredCount;
  }
}

/**
 * 验证所有套餐商品的选择是否有效
 * @param products 商品列表，包含可能的套餐商品
 * @returns boolean 所有套餐商品是否都有效
 */
export function validateAllPackageProducts(products: Product[]): boolean {
  const packageProducts = _.filter(products, (product: Product) => product.isPackage);

  if (packageProducts.length === 0) {
    return true; // 没有套餐商品，默认有效
  }

  for (const packageProduct of packageProducts) {
    const currentPackage = packageProduct as Product; // Explicitly cast to Product

    if (!currentPackage.packageDetail) {
      // 如果套餐没有详情，根据业务需求，这里可能视为有效或无效
      // 原始逻辑是跳过，我们暂时保持一致，视为有效（不进行进一步的子项校验）
      continue;
    }

    let packageDetailObj: PackageDetail;
    try {
      packageDetailObj =
        typeof currentPackage.packageDetail === 'string' ? JSON.parse(currentPackage.packageDetail) : (currentPackage.packageDetail as PackageDetail);

      // 检查套餐是否有选择要求 (optionalConfig)
      if (packageDetailObj.optionalConfig) {
        const optionalConfig = packageDetailObj.optionalConfig as { count?: number }; // Type assertion
        const selectedProductsArray = (packageDetailObj.selectedProducts || []) as SelectedProduct[]; // Type assertion

        const requiredCount = optionalConfig.count || 0;
        const totalSelectedCount = _.sumBy(selectedProductsArray, (p: SelectedProduct) => p.count || 0);

        if (totalSelectedCount < requiredCount) {
          // ElMessage.warning(`${currentPackage.name || currentPackage.productName || '套餐'}需要选择${requiredCount}件商品，当前已选${totalSelectedCount}件`);
          return false; // 当前套餐不满足要求
        }
      }
    } catch (error) {
      console.error(`解析套餐详情失败: ${currentPackage.name || currentPackage.productName}`, error);
      return false; // 解析失败视为无效
    }
  }

  return true; // 所有套餐商品都通过验证
}

/**
 * 对 MarketBill 应用可选组的自动选择逻辑
 * @param marketBill 商品账单数据
 * @returns 更新后的 MarketBill 对象，如果输入为 null 则返回 null
 */
export function applyAutoSelectionsToMarketBill(marketBill: MarketBill | null): MarketBill | null {
  if (!marketBill) {
    return null;
  }

  const updatedMarketBill = { ...marketBill }; // 创建副本以避免直接修改原始对象

  // 1. 处理付费可选组
  if (updatedMarketBill.optionalProducts && updatedMarketBill.optionalProducts.products && updatedMarketBill.optionalProducts.products.length > 0) {
    const hasExistingSelections = updatedMarketBill.optionalProducts.products.some((p: any) => p.quantity && p.quantity > 0);
    if (!hasExistingSelections) {
      updatedMarketBill.optionalProducts = utilAutoSelectOptionalGroup(updatedMarketBill.optionalProducts);
      // console.log('[BillUtils/applyAutoSelections] 已自动选择付费可选商品（简单选择第一项）');
    }
  }

  // 2. 处理免费可选组
  if (updatedMarketBill.optionalFreeProducts && updatedMarketBill.optionalFreeProducts.products && updatedMarketBill.optionalFreeProducts.products.length > 0) {
    const hasExistingSelections = updatedMarketBill.optionalFreeProducts.products.some((p: any) => p.quantity && p.quantity > 0);
    if (!hasExistingSelections) {
      updatedMarketBill.optionalFreeProducts = utilAutoSelectOptionalGroup(updatedMarketBill.optionalFreeProducts);
      // console.log('[BillUtils/applyAutoSelections] 已自动选择免费可选赠品（简单选择第一项）');
    }
  }
  return updatedMarketBill;
}

/**
 * 生成处理后的商品账单数据，用于前端展示和计算
 * @param marketBill 原始商品账单数据
 * @param addedProducts 用户后续添加的商品列表
 * @returns 包含处理后的订单内商品和订单外商品的对象
 */
export function generateProcessedMarketBillData(
  marketBill: MarketBill | null,
  addedProducts: ProductEntity[] | any[]
): { inOrderProducts: ProductEntity[]; outOrderProducts: ProductEntity[] } {
  let inOrderProductsProcessed: ProductEntity[] = [];
  let outOrderProductsProcessed: ProductEntity[] = [];

  // 处理订单内商品 (from marketBill)
  if (marketBill) {
    const rawInOrderProducts: any[] = [];

    // 1. 处理标准商品
    if (marketBill.standardProducts) {
      rawInOrderProducts.push(...marketBill.standardProducts);
    }

    // 2. 处理可选组商品 - 作为套餐商品处理
    if (marketBill.optionalProducts?.products) {
      const optionalGroup = marketBill.optionalProducts;
      const selectedProducts = optionalGroup.products.filter((p: any) => (p.quantity || 0) > 0);

      if (selectedProducts.length > 0) {
        // 创建可选组的套餐商品
        const optionalGroupPackage = {
          id: 'optional-group-package',
          productName: '可选商品',
          name: '可选商品',
          quantity: 1,
          unit: '组',
          price: selectedProducts.reduce((sum: number, p: any) => sum + (p.price || 0) * (p.quantity || 0), 0),
          currentPrice: selectedProducts.reduce((sum: number, p: any) => sum + (p.currentPrice || p.price || 0) * (p.quantity || 0), 0),
          totalAmount: selectedProducts.reduce((sum: number, p: any) => sum + (p.currentPrice || p.price || 0) * (p.quantity || 0), 0),
          isPackage: true,
          isFree: false,
          isInitialOrder: true,
          // 保存可选组的详细信息用于结账弹窗显示
          packageProductInfo: selectedProducts.map((product: any) => ({
            id: product.id,
            name: product.name || product.productName,
            count: product.count || 1,
            quantity: product.quantity || 0,
            price: product.currentPrice || product.price || 0
          })),
          // 添加可选组明细字符串
          details: selectedProducts
            .map((product: any) => {
              const normalizedType = getNormalizedOptionType(optionalGroup);
              const displayQuantity = normalizedType === 'ByPlan' && product.count ? product.count * (product.quantity || 1) : product.quantity;
              return `${product.name || product.productName}×${displayQuantity}`;
            })
            .join('，')
        };

        rawInOrderProducts.push(optionalGroupPackage);
      }
    }

    // 3. 处理免费商品
    if (marketBill.freeProducts) {
      rawInOrderProducts.push(...marketBill.freeProducts);
    }

    // 4. 处理可选免费组商品 - 作为套餐商品处理
    if (marketBill.optionalFreeProducts?.products) {
      const optionalFreeGroup = marketBill.optionalFreeProducts;
      const selectedProducts = optionalFreeGroup.products.filter((p: any) => (p.quantity || 0) > 0);

      if (selectedProducts.length > 0) {
        // 创建可选免费组的套餐商品
        const optionalFreeGroupPackage = {
          id: 'optional-free-group-package',
          productName: '可选赠品',
          name: '可选赠品',
          quantity: 1,
          unit: '组',
          price: 0, // 免费商品价格为0
          currentPrice: selectedProducts.reduce((sum: number, p: any) => sum + (p.currentPrice || p.price || 0) * (p.quantity || 0), 0),
          totalAmount: 0, // 免费商品总额为0
          isPackage: true,
          isFree: true,
          isInitialOrder: true,
          // 保存可选免费组的详细信息用于结账弹窗显示
          packageProductInfo: selectedProducts.map((product: any) => ({
            id: product.id,
            name: product.name || product.productName,
            count: product.count || 1,
            quantity: product.quantity || 0,
            price: product.currentPrice || product.price || 0
          })),
          // 添加可选免费组明细字符串
          details: selectedProducts
            .map((product: any) => {
              const normalizedType = getNormalizedOptionType(optionalFreeGroup);
              const displayQuantity = normalizedType === 'ByPlan' && product.count ? product.count * (product.quantity || 1) : product.quantity;
              return `${product.name || product.productName}×${displayQuantity}`;
            })
            .join('，')
        };

        rawInOrderProducts.push(optionalFreeGroupPackage);
      }
    }

    // 转换为标准格式
    inOrderProductsProcessed = _.map(rawInOrderProducts, product => OpenTableConverter.convertProduct(product as ProductEntity)) as any;
  }

  // 处理订单外商品 (addedProducts)
  outOrderProductsProcessed = _.map(addedProducts, product => OpenTableConverter.convertAddedProduct(product as any)) as any;

  return {
    inOrderProducts: inOrderProductsProcessed,
    outOrderProducts: outOrderProductsProcessed
  };
}
