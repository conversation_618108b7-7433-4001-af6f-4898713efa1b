/**
 * AddProductDialog 数据转换工具函数
 * 确保数据转换的准确性，避免类型模糊和兜底处理
 */

import {
  ApiProductVO,
  ApiProductPackageVO,
  ExtendedCartItem,
  PackageProductItem,
  PackageDetail,
  OptionalProductItem,
  ProductToCartItemParams,
  ConversionResult,
  PackageDetailParseResult,
  OptionalGroupDetailParseResult,
  DisplayProduct,
  OptionType
} from './types';

// ==================== 数据验证函数 ====================

/**
 * 验证商品ID是否有效
 */
export function isValidProductId(id: any): id is string {
  return typeof id === 'string' && id.trim().length > 0;
}

/**
 * 验证价格是否有效
 */
export function isValidPrice(price: any): price is number {
  return typeof price === 'number' && price >= 0 && !isNaN(price);
}

/**
 * 验证数量是否有效
 */
export function isValidQuantity(quantity: any): quantity is number {
  return typeof quantity === 'number' && quantity > 0 && !isNaN(quantity);
}

/**
 * 验证商品名称是否有效
 */
export function isValidProductName(name: any): name is string {
  return typeof name === 'string' && name.trim().length > 0;
}

// ==================== 套餐数据处理 ====================

/**
 * 解析套餐商品JSON字符串
 */
export function parsePackageProducts(packageProductsJson: string, productVOList?: any[]): ConversionResult<PackageProductItem[]> {
  try {
    if (!packageProductsJson || packageProductsJson.trim() === '') {
      return { success: true, data: [] };
    }

    const parsed = JSON.parse(packageProductsJson);

    if (!Array.isArray(parsed)) {
      return { success: false, error: '套餐商品数据格式错误：不是数组' };
    }

    const items: PackageProductItem[] = parsed.map((item: any, index: number) => {
      if (!isValidProductId(item.id)) {
        throw new Error(`套餐商品[${index}]的ID无效`);
      }

      // 🔥 修复：如果商品没有name字段，从productVOList中查找
      let productName = item.name;
      if (!productName && productVOList && Array.isArray(productVOList)) {
        const productInfo = productVOList.find((p: any) => p.id === item.id);
        productName = productInfo?.name;
      }

      if (!isValidProductName(productName)) {
        throw new Error(`套餐商品[${index}]的名称无效，ID: ${item.id}`);
      }

      // 🔥 修复：支持count字段作为quantity的别名
      const quantity = item.quantity || item.count;
      if (!isValidQuantity(quantity)) {
        throw new Error(`套餐商品[${index}]的数量无效`);
      }

      return {
        id: item.id,
        name: productName,
        quantity: quantity,
        unit: item.unit || '份',
        currentPrice: isValidPrice(item.currentPrice) ? item.currentPrice : 0
      };
    });

    return { success: true, data: items };
  } catch (error) {
    return {
      success: false,
      error: `解析套餐商品失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

/**
 * 生成套餐明细展示字符串
 */
export function generatePackageDetailString(items: PackageProductItem[]): string {
  if (!items || items.length === 0) {
    return '套餐暂无商品';
  }

  return items.map(item => `${item.name}×${item.quantity}`).join('，');
}

/**
 * 创建套餐明细对象
 */
export function createPackageDetail(packageData: ApiProductPackageVO): ConversionResult<PackageDetail> {
  try {
    // 解析套餐商品
    const packageProductsResult = parsePackageProducts(packageData.packageProducts || '', packageData.productVOList || []);
    if (!packageProductsResult.success) {
      return { success: false, error: packageProductsResult.error };
    }

    const packageProducts = packageProductsResult.data || [];
    const detailString = generatePackageDetailString(packageProducts);

    const packageDetail: PackageDetail = {
      packageProducts: packageData.packageProducts || '',
      optionalGroups: packageData.optionalGroups,
      productVOList: packageData.productVOList || [],
      detailString
    };

    return { success: true, data: packageDetail };
  } catch (error) {
    return {
      success: false,
      error: `创建套餐明细失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

// ==================== 可选组数据处理 ====================

/**
 * 解析可选组JSON字符串
 */
export function parseOptionalGroups(optionalGroupsJson: string): ConversionResult<any[]> {
  try {
    if (!optionalGroupsJson || optionalGroupsJson.trim() === '') {
      return { success: true, data: [] };
    }

    const parsed = JSON.parse(optionalGroupsJson);

    if (!Array.isArray(parsed)) {
      return { success: false, error: '可选组数据格式错误：不是数组' };
    }

    return { success: true, data: parsed };
  } catch (error) {
    return {
      success: false,
      error: `解析可选组失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

/**
 * 生成可选组明细展示字符串
 */
export function generateOptionalGroupDetailString(optionalProducts: OptionalProductItem[], optionType: OptionType = 'ByCount'): OptionalGroupDetailParseResult {
  try {
    if (!optionalProducts || optionalProducts.length === 0) {
      return {
        success: true,
        detailString: '可选组暂无商品',
        selectedProducts: [],
        selectedCount: 0
      };
    }

    // 获取已选择的商品
    const selectedProducts = optionalProducts.filter(product => isValidQuantity(product.quantity));

    if (selectedProducts.length === 0) {
      return {
        success: true,
        detailString: '未选择任何商品',
        selectedProducts: [],
        selectedCount: 0
      };
    }

    // 生成明细文本
    const detailItems = selectedProducts.map(product => `${product.name}×${product.quantity}`);
    const detailString = detailItems.join('，');

    // 计算已选数量
    const selectedCount = selectedProducts.reduce((sum, product) => sum + product.quantity, 0);

    return {
      success: true,
      detailString,
      selectedProducts,
      selectedCount
    };
  } catch (error) {
    return {
      success: false,
      error: `生成可选组明细失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

// ==================== 数据转换函数 ====================

/**
 * 将API商品数据转换为显示商品
 */
export function convertApiProductToDisplayProduct(apiProduct: ApiProductVO): ConversionResult<DisplayProduct> {
  try {
    if (!isValidProductId(apiProduct.id)) {
      return { success: false, error: '商品ID无效' };
    }
    if (!isValidProductName(apiProduct.name)) {
      return { success: false, error: '商品名称无效' };
    }
    if (!isValidPrice(apiProduct.currentPrice)) {
      return { success: false, error: '商品价格无效' };
    }

    const displayProduct: DisplayProduct = {
      id: apiProduct.id,
      name: apiProduct.name,
      stock: apiProduct.calculateInventory ? apiProduct.lowStockThreshold || 0 : -1,
      tag: apiProduct.isPromotion ? '促销' : '',
      currentPrice: apiProduct.currentPrice,
      unit: apiProduct.unit || '份',
      isPackage: false,
      isSoldOut: apiProduct.isSoldOut || false,
      quantity: 0
    };

    return { success: true, data: displayProduct };
  } catch (error) {
    return {
      success: false,
      error: `转换商品数据失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

/**
 * 将API套餐数据转换为显示商品
 */
export function convertApiPackageToDisplayProduct(apiPackage: ApiProductPackageVO): ConversionResult<DisplayProduct> {
  try {
    if (!isValidProductId(apiPackage.id)) {
      return { success: false, error: '套餐ID无效' };
    }
    if (!isValidProductName(apiPackage.name)) {
      return { success: false, error: '套餐名称无效' };
    }
    if (!isValidPrice(apiPackage.currentPrice)) {
      return { success: false, error: '套餐价格无效' };
    }

    // 解析套餐商品
    const packageProductsResult = parsePackageProducts(apiPackage.packageProducts || '', apiPackage.productVOList || []);
    const packageProducts = packageProductsResult.success ? packageProductsResult.data : [];

    // 解析可选组
    const optionalGroupsResult = parseOptionalGroups(apiPackage.optionalGroups || '');
    const optionalGroups = optionalGroupsResult.success ? optionalGroupsResult.data : [];

    const displayProduct: DisplayProduct = {
      id: apiPackage.id,
      name: apiPackage.name,
      stock: -1,
      tag: apiPackage.isPromotion ? '促销' : '',
      currentPrice: apiPackage.currentPrice,
      unit: '套餐',
      isPackage: true,
      isSoldOut: apiPackage.isSoldOut || false,
      quantity: 0,
      packageProducts,
      optionalGroups: optionalGroups || [],
      productVOList: apiPackage.productVOList,
      hasOptionalGroups: (optionalGroups || []).length > 0
    };

    return { success: true, data: displayProduct };
  } catch (error) {
    return {
      success: false,
      error: `转换套餐数据失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

/**
 * 将商品参数转换为购物车项
 */
export function convertProductToCartItem(params: ProductToCartItemParams): ConversionResult<ExtendedCartItem> {
  try {
    // 验证必需字段
    if (!isValidProductId(params.id)) {
      return { success: false, error: '商品ID无效' };
    }

    const productName = params.name || params.productName;
    if (!isValidProductName(productName)) {
      return { success: false, error: '商品名称无效' };
    }

    const currentPrice = params.currentPrice || params.price || 0;
    if (!isValidPrice(currentPrice)) {
      return { success: false, error: '商品价格无效' };
    }

    if (!isValidQuantity(params.quantity)) {
      return { success: false, error: '商品数量无效' };
    }

    // 创建基础购物车项
    const cartItem: ExtendedCartItem = {
      id: params.id,
      name: productName!,
      currentPrice,
      quantity: params.quantity,
      flavors: params.flavors || '',
      unit: params.unit || '份',
      isPackage: params.isPackage || false
    };

    // 处理套餐相关数据
    if (params.isPackage && params.packageDetail) {
      cartItem.packageDetail = params.packageDetail;
    }

    // 处理可选组相关数据
    if (params.isOptionalGroup) {
      cartItem.isOptionalGroup = true;
      cartItem.optionalProducts = params.optionalProducts || [];
      cartItem.optionType = (params.optionType as OptionType) || 'ByCount';
      cartItem.optionCount = params.optionCount || 1;
      cartItem.selectedQuantity = params.selectedQuantity || 0;
      cartItem.isFree = params.isFree || false;

      // 生成明细文本
      const detailResult = generateOptionalGroupDetailString(cartItem.optionalProducts, cartItem.optionType);
      cartItem.details = detailResult.success ? detailResult.detailString : '明细生成失败';
    }

    return { success: true, data: cartItem };
  } catch (error) {
    return {
      success: false,
      error: `转换购物车项失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

// ==================== 明细行处理 ====================

/**
 * 创建明细行
 */
export function createDetailRow(item: ExtendedCartItem): ConversionResult<ExtendedCartItem> {
  try {
    // 验证输入参数
    if (!item || !item.id) {
      return {
        success: false,
        error: '无效的商品项'
      };
    }

    // 创建基础明细行
    const detailRow: ExtendedCartItem = {
      id: `detail-${item.id}`,
      name: '',
      currentPrice: 0,
      quantity: 0,
      unit: '',
      flavors: '',
      isPackage: false,
      isDetail: true,
      parentId: item.id
    };

    // 根据商品类型设置明细内容
    if (item.isPackage && item.packageDetail) {
      // 套餐明细行
      detailRow.isPackage = true;

      // 🔥 重要修复：优先使用selectedProducts生成动态明细
      if (
        (item.packageDetail as any).selectedProducts &&
        Array.isArray((item.packageDetail as any).selectedProducts) &&
        (item.packageDetail as any).selectedProducts.length > 0
      ) {
        // 根据当前套餐数量动态计算明细
        const parentQuantity = item.quantity || 1;
        const detailItems = (item.packageDetail as any).selectedProducts.map((product: any) => {
          const productCount = (product.count || 1) * parentQuantity;
          return `${product.name}×${productCount}`;
        });
        detailRow.name = detailItems.join('，');

        console.log(`🔧 创建套餐明细行(使用selectedProducts):`, {
          parentId: item.id,
          parentName: item.name,
          parentQuantity: parentQuantity,
          selectedProductsCount: (item.packageDetail as any).selectedProducts.length,
          detailString: detailRow.name
        });
      } else {
        // 兜底：使用静态detailString
        detailRow.name = item.packageDetail.detailString || '套餐明细';
        console.log(`🔧 创建套餐明细行(使用detailString):`, {
          parentId: item.id,
          parentName: item.name,
          detailString: detailRow.name
        });
      }
    } else if (item.isOptionalGroup) {
      // 可选组明细行 - 优先使用已计算的details字段
      detailRow.isOptionalGroup = true;

      if (item.details && item.details.trim() !== '') {
        // 🔥 重要修复：如果有selectedProducts，根据当前数量动态计算
        if (
          (item.packageDetail as any)?.selectedProducts &&
          Array.isArray((item.packageDetail as any).selectedProducts) &&
          (item.packageDetail as any).selectedProducts.length > 0
        ) {
          const parentQuantity = item.quantity || 1;
          const detailItems = (item.packageDetail as any).selectedProducts.map((product: any) => {
            const productCount = (product.count || 1) * parentQuantity;
            return `${product.name}×${productCount}`;
          });
          detailRow.name = detailItems.join('，');

          console.log(`🔧 创建可选组明细行(使用selectedProducts):`, {
            parentId: item.id,
            parentName: item.name,
            parentQuantity: parentQuantity,
            selectedProductsCount: (item.packageDetail as any).selectedProducts.length,
            detailString: detailRow.name,
            source: 'selectedProducts_dynamic'
          });
        } else {
          // 使用已经计算好的明细文本
          detailRow.name = item.details;
          console.log(`🔧 创建可选组明细行(使用已有details):`, {
            parentId: item.id,
            parentName: item.name,
            detailString: detailRow.name,
            source: 'existing_details'
          });
        }
      } else if (item.optionalProducts && item.optionalProducts.length > 0) {
        // 重新计算明细文本（兜底方案）
        const detailResult = generateOptionalGroupDetailString(item.optionalProducts, item.optionType);
        if (detailResult.success) {
          detailRow.name = detailResult.detailString || '可选组明细';
          console.log(`🔧 创建可选组明细行(重新计算):`, {
            parentId: item.id,
            parentName: item.name,
            detailString: detailRow.name,
            selectedProducts: detailResult.selectedProducts?.length || 0,
            source: 'recalculated'
          });
        } else {
          detailRow.name = '明细计算失败';
          console.warn(`🔧 创建可选组明细行失败:`, {
            parentId: item.id,
            parentName: item.name,
            error: detailResult.error,
            source: 'calculation_failed'
          });
        }
      } else {
        detailRow.name = '未选择任何商品';
        console.log(`🔧 创建可选组明细行(无数据):`, {
          parentId: item.id,
          parentName: item.name,
          detailString: detailRow.name,
          source: 'no_data'
        });
      }
    } else {
      return {
        success: false,
        error: '商品不需要明细行或缺少明细数据'
      };
    }

    return {
      success: true,
      data: detailRow
    };
  } catch (error) {
    return {
      success: false,
      error: `创建明细行失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

/**
 * 判断是否需要创建明细行
 */
export function shouldCreateDetailRow(item: ExtendedCartItem): boolean {
  // 套餐商品需要明细行
  if (item.isPackage && item.packageDetail) {
    return true;
  }

  // 可选组商品需要明细行
  if (item.isOptionalGroup && item.optionalProducts && item.optionalProducts.length > 0) {
    return true;
  }

  return false;
}

// ==================== 数据清理函数 ====================

/**
 * 清理无效的购物车项
 */
export function cleanupCartItems(cartItems: ExtendedCartItem[]): ExtendedCartItem[] {
  return cartItems.filter(item => {
    // 明细行不需要验证数量
    if (item.isDetail) {
      return true;
    }

    // 普通商品需要验证数量
    return isValidQuantity(item.quantity);
  });
}

/**
 * 验证购物车项的完整性
 */
export function validateCartItem(item: ExtendedCartItem): ConversionResult<boolean> {
  if (item.isDetail) {
    // 明细行验证
    if (!item.parentId) {
      return { success: false, error: '明细行缺少父项ID' };
    }
    return { success: true, data: true };
  }

  // 普通商品验证
  if (!isValidProductId(item.id)) {
    return { success: false, error: '商品ID无效' };
  }
  if (!isValidProductName(item.name)) {
    return { success: false, error: '商品名称无效' };
  }
  if (!isValidPrice(item.currentPrice)) {
    return { success: false, error: '商品价格无效' };
  }
  if (!isValidQuantity(item.quantity)) {
    return { success: false, error: '商品数量无效' };
  }

  return { success: true, data: true };
}
