<template>
  <AppDialog v-model="dialogVisible" title="账单详情" :destroy-on-close="true" class="bill-detail-dialog !w-[864px]">
    <div class="p-4">
      <!-- 左侧应收应付信息 -->
      <div class="flex justify-between mb-6">
        <div class="w-1/2 border-r pr-4">
          <div class="flex justify-between mb-2 items-center">
            <div class="text-[16px] font-bold">应收金额</div>
            <PriceDisplay :amountInFen="shouldFee" />
          </div>
          <!-- 优惠信息列表 -->
          <!-- <div v-for="(item, index) in discountList" :key="index" class="flex justify-between text-sm mt-2">
            <div class="text-gray-500">{{ item.name }}</div>
            <PriceDisplay :amountInFen="item.amount" />
          </div> -->
        </div>

        <div class="w-1/2 pl-4">
          <div class="flex justify-between mb-2 items-center">
            <div class="text-[16px] font-bold">实收金额</div>
            <PriceDisplay :amountInFen="totalReceived" />
          </div>
          <!-- 支付方式列表 -->
          <div v-for="(item, index) in paymentList" :key="index" class="flex justify-between text-sm mt-2">
            <div class="text-gray-500">{{ item.isRefund ? `${item.name}已退` : item.name }}</div>
            <PriceDisplay :amountInFen="item.amount" />
          </div>
        </div>
      </div>

      <!-- 账单列表 -->
      <div class="mt-4">
        <div class="text-lg font-bold mb-4">订单信息</div>
        <el-table v-loading="loading" :data="mergedRecordList" @selection-change="handleSelectionChange" stripe class="w-full" ref="recordTableRef">
          <el-table-column type="selection" width="48px" />
          <el-table-column label="订单编号" prop="billId">
            <template #default="scope">
              <div class="flex items-center">
                <!-- 如果是还原记录，显示"账单还原"标记，并给订单号添加横线样式 -->
                <template v-if="scope.row.isBack && scope.row.direction !== 'refund'">
                  <div class="flex flex-col">
                    <span class="line-through">{{ formatBillId(scope.row.billId || '') }}</span>
                    <span class="text-[#B52E2E] text-[14px]">账单已还原</span>
                  </div>
                </template>
                <!-- 正常记录 -->
                <template v-else>
                  <span>{{ formatBillId(scope.row.billId || '') }}</span>
                </template>
                <el-tag v-if="scope.row.direction === 'refund'" size="small" type="danger" class="ml-[4px]">退</el-tag>
                <el-tag v-if="scope.row.isFree" size="small" type="info" class="ml-[4px]">赠</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="employeeName" width="100" />
          <el-table-column label="金额" prop="totalFee" align="right" max-width="280">
            <template #default="scope">
              <!-- 退款记录只显示退款金额 -->
              <div v-if="scope.row.direction === 'refund'" class="flex justify-between items-center">
                <span class="text-gray-500">已退：</span>
                <span class="text-red-500 font-bold">¥ {{ (scope.row.totalFee || 0) / 100 }}</span>
              </div>

              <!-- 正常支付记录显示详细信息 -->
              <div v-else class="flex flex-col gap-1">
                <div class="flex justify-between">
                  <span class="text-gray-500">应收：</span>
                  <span>¥ {{ (scope.row.shouldFee || 0) / 100 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">实收：</span>
                  <span>¥ {{ (scope.row.totalFee || 0) / 100 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">优惠金额：</span>
                  <span class="text-red-500">-¥ {{ Math.max((scope.row.shouldFee || 0) - (scope.row.totalFee || 0), 0) / 100 }}</span>
                </div>
                <div class="border-t border-gray-200 pt-1 mt-1">
                  <!-- 显示多个支付方式 -->
                  <div v-for="(payment, idx) in scope.row.paymentMethods" :key="idx" class="flex justify-between mt-1">
                    <span class="text-gray-500">{{ getPayTypeText(payment.payType || '') }}：</span>
                    <span class="font-bold">¥ {{ (payment.amount || 0) / 100 }}</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center w-full">
        <button class="btn-default" @click="handlePrint">账单打印</button>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { AppDialog } from '@/components/Dialog';
import { useOrderApi } from '@/modules/order/api/index';
import { formatDateTime } from '@/utils/dateUtils';
import type { BaseResponse } from '@/types/baseResponse';
import { PayBillVO, PayRecordVO } from '@/api/autoGenerated/shared/types';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { payTypeMap, PayType } from '@/utils/constant/payTyps';
import type { ElTable } from 'element-plus';
import { DialogManager } from '@/utils/dialog';

// 表格引用
const recordTableRef = ref<InstanceType<typeof ElTable>>();

// 接收props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  sessionId: {
    type: String,
    required: true
  },
  billId: {
    type: String,
    default: ''
  }
});

// 定义emit
const emit = defineEmits(['update:modelValue', 'print', 'error', 'close']);

// 组件状态
const loading = ref(false);
const billList = ref<PayBillVO[]>([]);
const payRecordList = ref<PayRecordVO[]>([]);
const selectedRecords = ref<any[]>([]); // 使用any类型以支持合并记录
const isInitialized = ref(false);
const employeeMap = ref<Record<string, string>>({});
// 添加选择处理标记，防止无限循环
const isSelectionHandling = ref(false);

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue || props.visible,
  set: val => {
    emit('update:modelValue', val);
  }
});

// API服务
const { billApi } = useOrderApi();

// 获取账单数据
const fetchBillData = async () => {
  if (!props.sessionId) return;

  loading.value = true;
  try {
    const response = await billApi.queryBillView({ sessionId: props.sessionId });
    if (response.code === 0 && response.data) {
      // 获取所有账单和支付记录
      const allBills = response.data.payBills || [];
      const allRecords = response.data.payRecords || [];

      // 如果指定了billId，先过滤出相关账单
      if (props.billId) {
        // 构建关联账单ID集合
        const relatedBillIds = new Set<string>();
        relatedBillIds.add(props.billId); // 添加当前账单ID

        // 找出所有与当前billId相关的账单ID（退款、还原等）
        allBills.forEach(bill => {
          // 添加退款关联的原单ID
          if (bill.billPid === props.billId) {
            relatedBillIds.add(bill.billId || '');
          }

          // 添加原单ID关联的退款
          if (bill.billId === props.billId && bill.billPid) {
            relatedBillIds.add(bill.billPid);
          }
        });

        console.log(`[BillDetailDialog] 相关账单ID: ${Array.from(relatedBillIds).join(', ')}`);

        // 过滤账单和支付记录
        billList.value = allBills.filter(bill => relatedBillIds.has(bill.billId || '') || relatedBillIds.has(bill.billPid || ''));

        payRecordList.value = allRecords.filter(record => relatedBillIds.has(record.billId || ''));
      } else {
        // 不指定billId时显示所有
        billList.value = allBills;
        payRecordList.value = allRecords;
      }

      console.log(`[BillDetailDialog] 过滤后账单数: ${billList.value.length}, 支付记录数: ${payRecordList.value.length}`);

      // 构建员工映射
      const responseData = response.data as any;
      const employees = responseData.employeeVOs || [];
      employees.forEach((employee: any) => {
        if (employee && employee.id) {
          employeeMap.value[employee.id] = employee.name || '';
        }
      });

      console.log('[BillDetailDialog] 已获取账单数据:', {
        bills: billList.value,
        records: payRecordList.value,
        employees: employeeMap.value
      });
    } else {
      console.error('获取账单数据失败:', response.message);
      emit('error', new Error(response.message || '获取账单数据失败'));
    }
  } catch (error) {
    console.error('获取账单数据异常:', error);
    emit('error', error);
  } finally {
    loading.value = false;
  }
};

// 监听参数变化
watch(
  () => [dialogVisible.value, props.sessionId],
  async ([visible, sessionId]) => {
    console.log('[BillDetailDialog] 监听参数变化:', {
      visible,
      sessionId
    });

    if (visible && !isInitialized.value && sessionId) {
      try {
        isInitialized.value = true;
        await fetchBillData();
      } catch (error) {
        console.error('初始化账单数据失败:', error);
        emit('error', error);
      }
    }

    if (!visible) {
      // 关闭对话框时重置状态
      selectedRecords.value = [];
      isInitialized.value = false;
    }
  },
  { immediate: true }
);

// 格式化单号
const formatBillId = (billId: string) => {
  if (!billId) return '';
  return billId.length > 12 ? `${billId.substring(0, 6)}***${billId.substring(billId.length - 6)}` : billId;
};

// 构建合并的记录列表（将账单和支付记录合并）
const mergedRecordList = computed(() => {
  // 创建账单ID到支付记录的映射
  const paymentMap = new Map<string, PayRecordVO[]>();
  payRecordList.value.forEach((record: PayRecordVO) => {
    if (!paymentMap.has(record.billId || '')) {
      paymentMap.set(record.billId || '', []);
    }
    paymentMap.get(record.billId || '')?.push(record);
  });

  // 基于账单列表构建记录
  const allRecords = billList.value.map((bill: PayBillVO) => {
    // 获取关联的支付记录
    const payments = paymentMap.get(bill.billId || '') || [];

    // 构建支付方式列表
    const paymentMethods = payments.map((payment: PayRecordVO) => ({
      payType: payment.payType || '',
      amount: payment.totalFee || 0,
      payId: payment.payId || '',
      id: payment.id || ''
    }));

    return {
      ...bill,
      id: bill.id || '',
      employeeName: employeeMap.value[bill.employeeId || ''] || bill.employeeId || '',
      // 支付记录信息
      paymentMethods: paymentMethods,
      // 添加退款关联关系所需字段
      payPid: bill.billPid ? payments[0]?.payPid || '' : '',
      payId: payments[0]?.payId || '',
      // 保留原始isBack属性
      isBack: bill.isBack || false
    };
  });

  // 如果设置了指定的 billId，过滤掉与其无关的数据
  let filteredRecords = allRecords;
  if (props.billId) {
    filteredRecords = allRecords.filter((record: any) => {
      // 保留匹配 billId 的记录
      if (record.billId === props.billId) {
        return true;
      }

      // 保留与该 billId 相关的退款记录 (billPid 匹配)
      if (record.billPid === props.billId) {
        return true;
      }

      return false;
    });

    console.log(`[BillDetailDialog] 已过滤账单记录，原始记录数: ${allRecords.length}, 过滤后记录数: ${filteredRecords.length}, billId: ${props.billId}`);
  }

  // 过滤掉同时是"退款"和"还原"的记录
  return filteredRecords.filter((record: any) => !(record.direction === 'refund' && record.isBack));
});

// 根据退款关系构建记录关系图
const recordRelationMap = computed(() => {
  const result = new Map<string, string[]>();

  // 建立 billPid -> bill.id 的映射关系
  mergedRecordList.value.forEach((record: any) => {
    if (record.billPid) {
      const parentRecords = mergedRecordList.value.filter((r: any) => r.billId === record.billPid);
      if (parentRecords.length > 0) {
        // 找到了父记录
        const parentId = parentRecords[0].id || '';
        if (parentId) {
          if (!result.has(parentId)) {
            result.set(parentId, []);
          }
          const childIds = result.get(parentId);
          if (childIds) {
            childIds.push(record.id || '');
          }
        }
      }
    }
  });

  return result;
});

// 计算应收金额 (考虑退款、还原等情况)
const shouldFee = computed(() => {
  // 按billId分组合并应收金额
  const billGroups = new Map<string, number>();

  // 处理所有账单
  billList.value.forEach(bill => {
    if (bill.direction === 'normal') {
      // 正常账单的应收
      billGroups.set(bill.billId || '', (billGroups.get(bill.billId || '') || 0) + (bill.shouldFee || 0));
    } else if (bill.direction === 'refund' && bill.billPid) {
      // 退款账单，减少原始账单的净应收
      billGroups.set(bill.billPid, (billGroups.get(bill.billPid) || 0) - (bill.shouldFee || 0));
    }
  });

  // 对于已还原的账单(isBack=true)，设置为0
  billList.value.forEach(bill => {
    if (bill.isBack && billGroups.has(bill.billId || '')) {
      billGroups.set(bill.billId || '', 0);
    }
  });

  // 计算总净应收
  let total = 0;
  billGroups.forEach(amount => {
    total += amount;
  });

  console.log(`[BillDetailDialog] 计算应收金额: ${total / 100}元`);
  return total;
});

// 计算总实收金额 (考虑退款、还原等情况)
const totalReceived = computed(() => {
  // 按billId分组合并实收金额
  const billGroups = new Map<string, number>();

  // 处理所有账单实收
  billList.value.forEach(bill => {
    if (bill.direction === 'normal') {
      // 正常账单的实收
      billGroups.set(bill.billId || '', (billGroups.get(bill.billId || '') || 0) + (bill.totalFee || 0));
    } else if (bill.direction === 'refund' && bill.billPid) {
      // 退款账单，减少原始账单的净实收
      billGroups.set(bill.billPid, (billGroups.get(bill.billPid) || 0) - (bill.totalFee || 0));
    }
  });

  // 对于已还原的账单(isBack=true)，设置为0
  billList.value.forEach(bill => {
    if (bill.isBack && billGroups.has(bill.billId || '')) {
      billGroups.set(bill.billId || '', 0);
    }
  });

  // 计算总净实收
  let total = 0;
  billGroups.forEach(amount => {
    total += amount;
  });

  console.log(`[BillDetailDialog] 计算实收金额: ${total / 100}元`);
  return total;
});

// 计算已选金额
const selectedAmount = computed(() => {
  return selectedRecords.value.reduce((sum, record) => {
    const amount = record.totalFee || 0;
    // 如果是退款记录（通过检查是否有payPid字段判断）
    if (record.payPid) {
      return sum - amount; // 退款记录作为负值计入
    }
    return sum + amount;
  }, 0);
});

// 判断是否为退款操作（所有选中的记录都是退款记录）
const isRefundOperation = computed(() => {
  // 如果没有选中记录，默认为false
  if (selectedRecords.value.length === 0) return false;

  // 检查是否所有选中记录都是退款记录
  return selectedRecords.value.every((record: any) => record.direction === 'refund');
});

// 计算选中的账单ID列表
const selectedBillIds = computed(() => {
  // 所有记录的ID都需要被传递，不再过滤退款记录
  return selectedRecords.value.map(record => record.billId || '').filter(id => id !== '');
});

// 支付方式列表
const paymentList = computed(() => {
  // 只有当有实收金额时才显示支付方式
  if (totalReceived.value <= 0) {
    return [];
  }

  const paymentMap = new Map<string, { name: string; amount: number; isRefund: boolean }>();

  // 过滤掉已还原的支付记录
  const validRecords = mergedRecordList.value.filter((record: any) => {
    // 找到记录对应的账单
    const bill = billList.value.find((b: any) => b.billId === record.billId);
    // 如果账单已还原，则跳过该支付记录
    return !bill || !bill.isBack;
  });

  // 处理每个有效记录的支付方式
  validRecords.forEach((record: any) => {
    if (record.paymentMethods && Array.isArray(record.paymentMethods)) {
      record.paymentMethods.forEach((payment: any) => {
        const payTypeName = getPayTypeText(payment.payType || '');
        const isRefund = record.direction === 'refund';
        const key = `${payTypeName}-${isRefund ? 'refund' : 'normal'}`;

        if (paymentMap.has(key)) {
          const existing = paymentMap.get(key)!;
          existing.amount += payment.amount || 0;
        } else {
          paymentMap.set(key, {
            name: payTypeName,
            amount: payment.amount || 0,
            isRefund
          });
        }
      });
    }
  });

  return Array.from(paymentMap.values()).filter(item => item.amount > 0);
});

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  // 如果正在处理选择，则直接返回
  if (isSelectionHandling.value) return;

  // 获取之前的选中项ID集合
  const previousSelectedIds = new Set(selectedRecords.value.map(item => item.id));
  // 当前选中项ID集合
  const currentSelectedIds = new Set(selection.map(item => item.id));

  // 判断是选中操作还是取消选中操作
  // 找出被取消选中的项目
  const unselectedItems = [...previousSelectedIds].filter(id => !currentSelectedIds.has(id));
  // 找出新选中的项目
  const newlySelectedItems = [...currentSelectedIds].filter(id => !previousSelectedIds.has(id));

  // 更新选择状态
  selectedRecords.value = selection;

  // 联动选择逻辑
  nextTick(() => {
    // 使用最新的选中项集合
    const selectedIds = new Set(selection.map(item => item.id));
    const newSelection = new Set(selectedIds);
    let selectionChanged = false;

    // 处理取消选择的联动逻辑（自动取消关联项）
    if (unselectedItems.length > 0) {
      unselectedItems.forEach(unselectedId => {
        const record = mergedRecordList.value.find(r => r.id === unselectedId);
        if (!record) return;

        // 如果取消选择了父记录，也取消选择其所有子记录
        const childIds = recordRelationMap.value.get(unselectedId) || [];
        childIds.forEach(childId => {
          if (newSelection.has(childId)) {
            newSelection.delete(childId);
            selectionChanged = true;
          }
        });

        // 如果取消选择了子记录，检查是否应该取消选择父记录
        if (record.payPid) {
          const parentId = findParentId(record);
          if (!parentId) return;

          // 检查该父记录的所有子记录是否都已取消选择
          const allChildIds = recordRelationMap.value.get(parentId) || [];
          const hasSelectedChild = allChildIds.some(childId => childId !== unselectedId && newSelection.has(childId));

          // 如果没有子记录被选中，且父记录已被选中，则取消选择父记录
          if (!hasSelectedChild && newSelection.has(parentId)) {
            newSelection.delete(parentId);
            selectionChanged = true;
          }
        }
      });
    }

    // 处理新选中的联动逻辑（自动选中关联项）
    if (newlySelectedItems.length > 0) {
      newlySelectedItems.forEach(selectedId => {
        const record = mergedRecordList.value.find(r => r.id === selectedId);
        if (!record) return;

        // 如果选中了退款记录，也选中其父记录
        if (record.payPid) {
          const parentId = findParentId(record);
          if (parentId && !newSelection.has(parentId)) {
            newSelection.add(parentId);
            selectionChanged = true;
          }
        }

        // 如果选中了有子记录的记录，也选中其所有子记录
        const childIds = recordRelationMap.value.get(selectedId) || [];
        childIds.forEach(childId => {
          if (!newSelection.has(childId)) {
            newSelection.add(childId);
            selectionChanged = true;
          }
        });
      });
    }

    // 如果选择发生了变化，更新表格选择
    if (selectionChanged) {
      const table = recordTableRef.value;
      if (table) {
        try {
          // 设置标记，防止无限循环
          isSelectionHandling.value = true;

          // 清除当前选择
          table.clearSelection();

          // 重新选择所有应选项
          mergedRecordList.value.forEach(record => {
            if (newSelection.has(record.id)) {
              table.toggleRowSelection(record, true);
            }
          });

          // 更新选择结果
          selectedRecords.value = mergedRecordList.value.filter(record => newSelection.has(record.id || ''));
        } finally {
          // 确保总是重置标记
          setTimeout(() => {
            isSelectionHandling.value = false;
          }, 0);
        }
      }
    }
  });
};

// 辅助函数：查找记录的父记录ID
const findParentId = (record: any): string => {
  if (!record || !record.billPid) return '';

  const parentRecords = mergedRecordList.value.filter((r: any) => r.billId === record.billPid);
  if (parentRecords.length > 0) {
    return parentRecords[0].id || '';
  }

  return '';
};

// 打印处理
const handlePrint = () => {
  if (selectedRecords.value.length === 0) return;
  const selectedBillIds = selectedRecords.value.map(record => record.billId).filter(Boolean);
  console.log('[BillDetailDialog] 打印账单:', selectedBillIds, selectedRecords.value);
  emit('print', selectedBillIds);
};

// 格式化日期
const formatDate = (timestamp: number) => {
  return formatDateTime(timestamp.toString());
};

// 获取支付方式文本
const getPayTypeText = (payType: string) => {
  return payTypeMap[payType as PayType] || payType;
};
</script>

<style scoped>
.bill-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}
</style>
