import { getShiftReportDaily, submitShiftHandover, getShiftReportById } from '../../api/employeeShift';
import type { ShiftReportDailyResponse, ShiftReportDaily } from '../../entity/EmployeeShiftEntity';
import { AxiosResponse } from 'axios';
import { PrintingService } from '@/application/printingService';
import { ElMessage } from 'element-plus';
import { postApiPrintRecordCheckoutPayBillId, postApiPrintRecordCheckoutCreate } from '@/api/autoGenerated';
import type { GetCheckoutPrintRecordsByPayBillIdReqDto, CreateCheckoutPrintRecordReqDto, CheckoutPrintRecordVO } from '@/api/autoGenerated';
import { useVenueStore } from '@/stores/venueStore';

/**
 * 员工交班业务交互层
 */
export class EmployeeShiftInteractor {
  private static printingService = new PrintingService();
  /**
   * 获取交班报告数据
   * @param date 日期（时间戳）
   * @param employeeId 员工ID
   * @param venueId 场地ID
   * @returns 交班报告响应
   */
  static async getShiftReport(date: number, employeeId: string, venueId: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportDaily({
      date,
      employeeId,
      venueId
    });
  }

  /**
   * 通过交班单ID获取交班报告数据
   * @param shiftId 交班单ID
   * @param handNo 交班单号（可选）
   * @returns 交班报告响应
   */
  static async getShiftReportById(shiftId: string, handNo?: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportById(shiftId, handNo);
  }

  /**
   * 提交交班上报
   * @param date 日期
   * @param employeeId 员工ID
   * @param venueId 场馆ID
   * @param shiftReportData 交班数据
   * @returns 上报结果
   */
  static async submitShiftHandover(
    date: number,
    employeeId: string,
    venueId: string,
    shiftReportData: ShiftReportDaily[]
  ): Promise<any> {
    return await submitShiftHandover({
      date,
      employeeId,
      venueId,
      shiftReportDaily: shiftReportData
    });
  }

  /**
   * 打印交班报告
   * 这里可以实现具体的打印业务逻辑
   */
  static printShiftReport(reportData: any): void {
    // 打印业务逻辑实现
    // 可能需要调用打印服务API或使用浏览器打印功能
    console.log('打印交班报告:', reportData);
  }

  /**
   * 导出交班报告
   * 这里可以实现具体的导出业务逻辑
   */
  static exportShiftReport(reportData: any): void {
    // 导出业务逻辑实现
    // 可能需要生成Excel或CSV文件并触发下载
    console.log('导出交班报告:', reportData);
  }

  /**
   * 打印结账单
   * @param billIds 账单ID列表
   * @param sessionId 会话ID
   * @returns 打印结果
   */
  static async printCheckoutBills(billIds: string[], sessionId: string): Promise<void> {
    if (!billIds || billIds.length === 0) {
      throw new Error('没有可打印的账单');
    }

    if (!sessionId) {
      throw new Error('会话ID不能为空');
    }

    console.log('[EmployeeShiftInteractor] 准备打印账单:', {
      billIds,
      sessionId,
      count: billIds.length
    });

    // 获取场馆ID
    const venueStore = useVenueStore();
    const venueId = venueStore.venueId;

    if (!venueId) {
      throw new Error('无法获取场馆ID');
    }

    // 为每个账单ID处理打印逻辑
    const printPromises = billIds.map(async (billId) => {
      try {
        console.log(`[EmployeeShiftInteractor] 开始处理账单: ${billId}`);

        // 1. 先查询是否已有打印记录
        const existingRecords = await this.queryExistingPrintRecords(billId, venueId);

        let printRecord: CheckoutPrintRecordVO;

        if (existingRecords && existingRecords.length > 0) {
          // 2. 如果有记录，直接使用第一条记录
          printRecord = existingRecords[0];
          console.log(`[EmployeeShiftInteractor] 账单 ${billId} 已有打印记录，直接打印:`, printRecord.id);
        } else {
          // 3. 如果没有记录，创建新的打印记录
          console.log(`[EmployeeShiftInteractor] 账单 ${billId} 无打印记录，创建新记录`);
          printRecord = await this.createPrintRecord(billId, sessionId, venueId);
          console.log(`[EmployeeShiftInteractor] 账单 ${billId} 创建打印记录成功:`, printRecord.id);
        }

        // 4. 执行实际打印
        const success = await this.executePrint(printRecord);

        if (success) {
          console.log(`[EmployeeShiftInteractor] 账单 ${billId} 打印成功`);
          return { billId, success: true };
        } else {
          console.warn(`[EmployeeShiftInteractor] 账单 ${billId} 打印失败`);
          return { billId, success: false, error: '打印失败' };
        }
      } catch (error) {
        console.error(`[EmployeeShiftInteractor] 账单 ${billId} 打印异常:`, error);
        return {
          billId,
          success: false,
          error: error instanceof Error ? error.message : '打印异常'
        };
      }
    });

    // 等待所有打印任务完成
    const results = await Promise.all(printPromises);

    // 统计打印结果
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    console.log('[EmployeeShiftInteractor] 打印结果统计:', {
      total: results.length,
      success: successCount,
      fail: failCount,
      results
    });

    // 显示打印结果
    if (failCount === 0) {
      ElMessage.success(`成功打印 ${successCount} 张账单`);
    } else if (successCount === 0) {
      throw new Error(`所有账单打印失败`);
    } else {
      ElMessage.warning(`成功打印 ${successCount} 张账单，失败 ${failCount} 张`);
    }
  }

  /**
   * 查询已有的打印记录
   * @param payBillId 账单ID
   * @param venueId 场馆ID
   * @returns 打印记录列表
   */
  private static async queryExistingPrintRecords(payBillId: string, venueId: string): Promise<CheckoutPrintRecordVO[]> {
    try {
      const params: GetCheckoutPrintRecordsByPayBillIdReqDto = {
        payBillId,
        venueId
      };

      console.log(`[EmployeeShiftInteractor] 查询账单 ${payBillId} 的打印记录:`, params);
      const response = await postApiPrintRecordCheckoutPayBillId(params);

      if (response && response.code === 0 && response.data) {
        console.log(`[EmployeeShiftInteractor] 账单 ${payBillId} 查询到 ${response.data.length} 条打印记录`);
        return response.data;
      } else {
        console.log(`[EmployeeShiftInteractor] 账单 ${payBillId} 无打印记录`);
        return [];
      }
    } catch (error) {
      console.error(`[EmployeeShiftInteractor] 查询账单 ${payBillId} 打印记录失败:`, error);
      return [];
    }
  }

  /**
   * 创建新的打印记录
   * @param payBillId 账单ID
   * @param sessionId 会话ID
   * @param venueId 场馆ID
   * @returns 打印记录
   */
  private static async createPrintRecord(payBillId: string, sessionId: string, venueId: string): Promise<CheckoutPrintRecordVO> {
    const params: CreateCheckoutPrintRecordReqDto = {
      payBillId,
      sessionId,
      venueId,
      orderNos: [] // 可选参数，这里传空数组
    };

    console.log(`[EmployeeShiftInteractor] 创建账单 ${payBillId} 的打印记录:`, params);
    const response = await postApiPrintRecordCheckoutCreate(params);

    if (response && response.data && response.data) {
      console.log(`[EmployeeShiftInteractor] 账单 ${payBillId} 创建打印记录成功:`, response.data.id);
      return response.data;
    } else {
      throw new Error(`创建打印记录失败: ${response?.statusText || '未知错误'}`);
    }
  }

  /**
   * 执行实际打印
   * @param printRecord 打印记录
   * @returns 打印是否成功
   */
  private static async executePrint(printRecord: CheckoutPrintRecordVO): Promise<boolean> {
    try {
      if (!printRecord.checkoutBillData) {
        console.warn('[EmployeeShiftInteractor] 打印记录缺少账单数据');
        return false;
      }

      console.log(`[EmployeeShiftInteractor] 执行打印，记录ID: ${printRecord.id}`);

      // 使用打印服务执行实际打印
      // 这里可以根据具体的打印需求调用不同的打印方法
      // 例如：调用打印机打印、生成PDF、发送到打印队列等

      // 目前先模拟打印成功
      // 在实际项目中，可以根据需要实现具体的打印逻辑
      console.log(`[EmployeeShiftInteractor] 打印数据:`, {
        printRecordId: printRecord.id,
        payBillId: printRecord.payBillId,
        sessionId: printRecord.sessionId,
        printTime: printRecord.printTime,
        hasCheckoutData: !!printRecord.checkoutBillData
      });

      return true; // 模拟打印成功
    } catch (error) {
      console.error('[EmployeeShiftInteractor] 执行打印失败:', error);
      return false;
    }
  }
}