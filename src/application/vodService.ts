import { useVod } from '@/domains/vod';
import { defaultVodConfig } from '@/domains/vod';
import { useDeviceStore } from '@/stores/deviceStore';
import type { VodResponse } from '@/domains/vod/vodInterface';
import { useStageStore } from '@/stores/stageStore';
import type { VodConfig } from '@/domains/vod/vodConfig';
import { useVenueStore } from '@/stores/venueStore';
import { now10, now13 } from '@/utils/dateUtils';
// 定时提醒类型
export enum TimeoutType {
  TIMEOUT = 1, // 到时提醒
  EXPIRED = 5 // 已经超时
}

// 定义队列中的操作项接口
interface QueueItem {
  roomId: string;
  deviceIp: string;
  endTime?: number; // 截止时间（秒）
  operation: 'open' | 'close' | 'timeout';
  timeoutType?: TimeoutType;
  timeoutTime?: string;
  executeTime?: number; // 执行时间（毫秒）
}

// VOD队列管理器
class VodQueueManager {
  // 存储房间操作队列，key是roomId
  private roomQueues: Map<string, QueueItem[]> = new Map();
  // 存储每个房间的定时器，key是roomId
  private roomTimers: Map<string, number[]> = new Map();
  // 记录房间的截止时间，key是roomId
  private roomEndTimes: Map<string, number> = new Map();
  // 操作执行器
  private operationHandler: {
    open: (roomId: string, deviceIp: string, endTime?: number) => Promise<void>;
    close: (roomId: string, deviceIp: string) => Promise<void>;
    timeout: (roomId: string, deviceIp: string, type: TimeoutType, time: string) => Promise<void>;
  };

  constructor(operationHandler: {
    open: (roomId: string, deviceIp: string, endTime?: number) => Promise<void>;
    close: (roomId: string, deviceIp: string) => Promise<void>;
    timeout: (roomId: string, deviceIp: string, type: TimeoutType, time: string) => Promise<void>;
  }) {
    this.operationHandler = operationHandler;
  }

  // 添加操作到队列
  addToQueue(item: QueueItem): void {
    const queue = this.roomQueues.get(item.roomId) || [];
    queue.push(item);
    this.roomQueues.set(item.roomId, queue);

    // 如果是开台或更新截止时间操作，记录截止时间
    if (item.endTime && (item.operation === 'open' || item.operation === 'timeout')) {
      this.updateRoomEndTime(item.roomId, item.endTime, item.deviceIp);
    }

    // 如果是关台操作，清空该房间的定时器
    if (item.operation === 'close') {
      this.clearRoomTimers(item.roomId);
    }

    // 处理队列
    this.processQueue(item.roomId);
  }

  // 更新房间截止时间
  updateRoomEndTime(roomId: string, endTime: number, deviceIp?: string): void {
    const currentEndTime = this.roomEndTimes.get(roomId);
    const currentQueue = this.roomQueues.get(roomId) || [];
    const now = now10(); // 使用修正后的当前时间（秒）

    // 检查队列中是否有该房间的记录
    const hasQueueRecords = currentQueue.length > 0;

    // 情况1：队列中没有记录，且截止时间未到，需要执行开台操作
    if (!hasQueueRecords && endTime > now && deviceIp) {
      console.log(`[VodQueueManager] 房间 ${roomId} 无队列记录且截止时间未到，执行开台操作`);
      this.addToQueue({
        roomId,
        deviceIp,
        operation: 'open',
        endTime
      });
    }

    // 情况2：有记录且截止时间已到，需要执行关台操作
    if (hasQueueRecords && endTime <= now && deviceIp) {
      console.log(`[VodQueueManager] 房间 ${roomId} 有队列记录且截止时间已到，执行关台操作`);
      this.addToQueue({
        roomId,
        deviceIp,
        operation: 'close'
      });
      return; // 关台后不需要继续处理定时器
    }

    // 如果截止时间变化，清空定时器并重新计划
    if (currentEndTime !== endTime) {
      this.roomEndTimes.set(roomId, endTime);
      this.clearRoomTimers(roomId);

      // 只有截止时间未到才需要重新安排定时器
      if (endTime > now && deviceIp) {
        this.scheduleTasks(roomId, endTime, deviceIp);
      }
    }
  }

  // 清空房间的所有定时器
  clearRoomTimers(roomId: string): void {
    const timers = this.roomTimers.get(roomId) || [];
    timers.forEach(timer => window.clearTimeout(timer));
    this.roomTimers.set(roomId, []);
  }

  // 处理队列中的操作
  private async processQueue(roomId: string): Promise<void> {
    const queue = this.roomQueues.get(roomId) || [];
    if (queue.length === 0) return;

    // 获取并执行第一个操作
    const item = queue[0];

    try {
      // 根据操作类型调用对应的处理函数
      switch (item.operation) {
        case 'open':
          await this.operationHandler.open(item.roomId, item.deviceIp, item.endTime);
          break;
        case 'close':
          await this.operationHandler.close(item.roomId, item.deviceIp);
          break;
        case 'timeout':
          if (item.timeoutType && item.timeoutTime) {
            await this.operationHandler.timeout(item.roomId, item.deviceIp, item.timeoutType, item.timeoutTime);
          }
          break;
      }
      console.log(`[VodQueueManager] 执行操作成功: ${item.operation} - 房间ID: ${item.roomId}`);
    } catch (error) {
      console.error(`[VodQueueManager] 执行操作失败: ${item.operation} - 房间ID: ${item.roomId}`, error);
    }

    // 移除已处理的操作
    queue.shift();
    this.roomQueues.set(roomId, queue);

    // 继续处理队列中的下一个操作
    if (queue.length > 0) {
      await this.processQueue(roomId);
    }
  }

  // 安排房间的超时提醒和关台任务
  scheduleTasks(roomId: string, endTimeSeconds: number, deviceIp?: string): void {
    if (!deviceIp) return;

    // 转换为毫秒
    const endTimeMs = endTimeSeconds * 1000;
    const now = now13();
    const timeRemaining = endTimeMs - now;

    if (timeRemaining <= 0) return;

    const timers: number[] = [];

    // 1. 设置提前10分钟超时提醒
    if (timeRemaining > 10 * 60 * 1000) {
      const tenMinutesBeforeDelay = timeRemaining - 10 * 60 * 1000;
      const tenMinTimer = window.setTimeout(() => {
        // 在这里调用实际的超时提醒方法
        this.addToQueue({
          roomId,
          deviceIp,
          operation: 'timeout',
          timeoutType: TimeoutType.EXPIRED,
          timeoutTime: '10'
        });
      }, tenMinutesBeforeDelay);
      timers.push(tenMinTimer);
    }

    // 2. 设置提前5分钟超时提醒
    if (timeRemaining > 5 * 60 * 1000) {
      const fiveMinutesBeforeDelay = timeRemaining - 5 * 60 * 1000;
      const fiveMinTimer = window.setTimeout(() => {
        // 在这里调用实际的超时提醒方法
        this.addToQueue({
          roomId,
          deviceIp,
          operation: 'timeout',
          timeoutType: TimeoutType.EXPIRED,
          timeoutTime: '5'
        });
      }, fiveMinutesBeforeDelay);
      timers.push(fiveMinTimer);
    }

    // 3. 设置超时关台
    const delay = timeRemaining + 30 * 1000; // 超时30秒后关台
    const timer = window.setTimeout(() => {
      // 在这里调用实际的关台方法
      this.addToQueue({
        roomId,
        deviceIp,
        operation: 'close'
      });
    }, delay);
    timers.push(timer);

    // 保存该房间的定时器
    const existingTimers = this.roomTimers.get(roomId) || [];
    this.roomTimers.set(roomId, [...existingTimers, ...timers]);

    // console.log('[VodQueueManager] 房间ID:', roomId, '定时器:', timers);
  }

  // 检查房间是否可以发送超时提醒
  canSendTimeoutRemind(roomId: string): boolean {
    // 如果房间有定时器，说明可以发送超时提醒
    const timers = this.roomTimers.get(roomId) || [];
    return timers.length > 0;
  }

  // 检查房间是否有队列记录
  hasRoomQueue(roomId: string): boolean {
    const queue = this.roomQueues.get(roomId) || [];
    return queue.length > 0;
  }

  // 获取房间当前的截止时间
  getRoomEndTime(roomId: string): number | undefined {
    return this.roomEndTimes.get(roomId);
  }
}

export class VodService {
  private vod;
  private queueManager: VodQueueManager;

  constructor() {
    const venue = useVenueStore().venue;
    const vodSettings = useVenueStore().vodSettings;
    const config = { ...defaultVodConfig };
    config.appid = venue?.appId || '';
    config.appkey = venue?.appKey || '';

    // 如果存在VOD设置，使用VOD设置中的服务器IP
    if (vodSettings?.vodServerIP) {
      config.host = vodSettings.vodServerIP;
    }

    console.log('VodService constructor config', config, 'venue', venue, 'vodSettings', vodSettings);
    this.vod = useVod(config);

    // 创建操作处理器
    const operationHandler = {
      open: this.handleOpen.bind(this),
      close: this.handleClose.bind(this),
      timeout: this.handleTimeout.bind(this)
    };

    // 初始化队列管理器，传入操作处理器
    this.queueManager = new VodQueueManager(operationHandler);
  }

  // 内部操作处理方法
  private async handleOpen(roomId: string, deviceIp: string, endTime?: number): Promise<void> {
    try {
      await this.openRoom(deviceIp);
      console.log(`[VodService] 执行开台成功 - 房间ID: ${roomId}, IP: ${deviceIp}`);
    } catch (error) {
      console.error(`[VodService] 执行开台失败 - 房间ID: ${roomId}`, error);
    }
  }

  private async handleClose(roomId: string, deviceIp: string): Promise<void> {
    try {
      await this.closeRoom(deviceIp);
      console.log(`[VodService] 执行关台成功 - 房间ID: ${roomId}, IP: ${deviceIp}`);
    } catch (error) {
      console.error(`[VodService] 执行关台失败 - 房间ID: ${roomId}`, error);
    }
  }

  private async handleTimeout(roomId: string, deviceIp: string, type: TimeoutType, time: string): Promise<void> {
    try {
      await this.timeout(deviceIp, type, time);
      console.log(`[VodService] 执行超时提醒成功 - 房间ID: ${roomId}, IP: ${deviceIp}, 时间: ${time}分钟`);
    } catch (error) {
      console.error(`[VodService] 执行超时提醒失败 - 房间ID: ${roomId}`, error);
    }
  }

  // 检查是否为主收银机
  private isMainCashier(): boolean {
    const deviceStore = useDeviceStore();
    return deviceStore.cashierMachine?.isMain === true;
  }

  updateVodConfig(config: VodConfig) {
    console.log('updateVodConfig', config);
    this.vod.updateVodConfig(config);
  }

  /**
   * 根据房间ID获取设备IP
   */
  private getRoomIpByRoomId(roomId: string): string {
    const stage = useStageStore().getStageByRoomId(roomId);
    return stage?.roomVO.deviceIp || '';
  }

  /**
   * 内部调用：开启房间
   */
  private async openRoom(roomIp: string): Promise<VodResponse> {
    try {
      console.log('[VodService] 开启房间', roomIp);
      return await this.vod.openRoom(roomIp);
    } catch (error) {
      console.error('开台操作失败:', error);
      throw error;
    }
  }

  /**
   * 开启房间点播系统
   * @param roomId 房间ID
   * @param endTime 截止时间（秒）
   * @returns 操作结果，如果不是主收银机则返回null
   */
  async open(roomId: string, endTime?: number) {
    // 如果不是主收银机，则不执行操作
    if (!this.isMainCashier()) {
      console.log('[VodService] 非主收银机，不执行开台操作');
      return null;
    }

    const roomIp = this.getRoomIpByRoomId(roomId);
    if (!roomIp) {
      console.error('[VodService] 房间信息不存在');
      throw new Error('房间信息不存在');
    }

    // 添加到队列
    this.queueManager.addToQueue({
      roomId,
      deviceIp: roomIp,
      operation: 'open',
      endTime
    });

    // 如果有截止时间，安排任务
    if (endTime) {
      this.queueManager.scheduleTasks(roomId, endTime, roomIp);
    }
  }

  /**
   * 内部调用：关闭房间
   */
  private async closeRoom(roomIp: string): Promise<VodResponse> {
    try {
      console.log('[VodService] 关闭房间', roomIp);
      return await this.vod.closeRoom(roomIp);
    } catch (error) {
      console.error('关台操作失败:', error);
      throw error;
    }
  }

  async switchRoom(srcRoomId: string, desRoomId: string) {
    try {
      const srcIp = this.getRoomIpByRoomId(srcRoomId);
      const desIp = this.getRoomIpByRoomId(desRoomId);
      console.log('[VodService] 切换房间', srcIp, desIp);
      return await this.vod.switchRoom(srcIp, desIp);
    } catch (error) {
      console.error('Failed to switch room:', error);
      throw error;
    }
  }

  /**
   * 关闭房间点播系统
   * @param roomId 房间ID
   * @returns 操作结果，如果不是主收银机则返回null
   */
  async close(roomId: string) {
    // 如果不是主收银机，则不执行操作
    if (!this.isMainCashier()) {
      console.log('[VodService] 非主收银机，不执行关台操作');
      return null;
    }

    const roomIp = this.getRoomIpByRoomId(roomId);
    if (!roomIp) {
      console.error('[VodService] 房间信息不存在');
      throw new Error('房间信息不存在');
    }

    // 添加到队列，清除定时器
    this.queueManager.addToQueue({
      roomId,
      deviceIp: roomIp,
      operation: 'close'
    });
  }

  /**
   * 内部调用：发送超时提醒
   */
  private async timeout(roomIp: string, type: TimeoutType, time: string): Promise<VodResponse> {
    try {
      console.log('[VodService] 发送超时提醒', roomIp, type, time);
      return await this.vod.timeout(roomIp, type, time);
    } catch (error) {
      console.error('超时提醒失败:', error);
      throw error;
    }
  }

  /**
   * 更新房间截止时间
   * @param roomId 房间ID
   * @param endTime 截止时间（秒）
   */
  updateEndTime(roomId: string, endTime: number): void {
    // 如果不是主收银机，则不执行操作
    if (!this.isMainCashier()) {
      console.log('[VodService] 非主收银机，不执行更新截止时间操作');
      return;
    }

    const roomIp = this.getRoomIpByRoomId(roomId);
    if (!roomIp) {
      console.error('[VodService] 房间信息不存在');
      return;
    }

    // 更新房间截止时间并重新安排任务
    this.queueManager.updateRoomEndTime(roomId, endTime, roomIp);
  }
}

let _vodService: VodService | null = null;
export function useVodService() {
  if (!_vodService) _vodService = new VodService();
  return _vodService;
}
