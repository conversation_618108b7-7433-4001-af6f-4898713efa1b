import NatsService from '@/services/nats-service';
import { watch } from 'vue';
import router from '@/router';
import { ElMessage } from 'element-plus';
// 统一导入所需的store
import { useNatsStore } from '@/stores/natsStore';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { useStageStore } from '@/stores/stageStore';
import { useNotificationStore } from '@/modules/notification/stores/notificationStore';

class NatsListenerService {
  private nats: NatsService;
  private unsubscribe?: () => void;
  private isListening = false;
  private statusListenerActive = false;
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private maxReconnectAttempts = 5;
  private reconnectAttempts = 0;
  private reconnectInterval = 5000; // 5秒重试间隔

  // 添加store属性
  private userStore: ReturnType<typeof useUserStore> | null = null;
  private deviceStore: ReturnType<typeof useDeviceStore> | null = null;
  private venueStore: ReturnType<typeof useVenueStore> | null = null;
  private natsStore: ReturnType<typeof useNatsStore> | null = null;
  private stageStore: ReturnType<typeof useStageStore> | null = null;
  private notificationStore: ReturnType<typeof useNotificationStore> | null = null;

  constructor() {
    this.nats = NatsService.getInstance();
  }

  public async startListening(): Promise<void> {
    // 统一初始化所有store
    this.userStore = useUserStore();
    this.deviceStore = useDeviceStore();
    this.natsStore = useNatsStore();
    this.stageStore = useStageStore();
    this.notificationStore = useNotificationStore();

    // console.log('NatsListenerService - userStore:', this.userStore);
    // console.log('NatsListenerService - deviceStore:', this.deviceStore);
    // console.log('NatsListenerService - notificationStore:', this.notificationStore);

    // 添加NATS连接状态监听器
    if (!this.statusListenerActive) {
      this.nats.addConnectionListener(status => {
        // console.log('NatsListenerService - NATS连接状态变更:', status);
        this.natsStore?.updateConnectionStatus(status);

        // 如果连接断开，尝试重连
        if (status === 'disconnected' || status === 'error' || status === 'closed') {
          this.scheduleReconnect(this.venueStore?.venueId || '', this.deviceStore?.macAddress || '');
        } else if (status === 'connected') {
          // 连接成功，重置重连计数
          this.reconnectAttempts = 0;
          if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
          }
        }
      });
      this.statusListenerActive = true;
    }

    // 尝试立即更新一次连接状态
    this.natsStore?.updateConnectionStatus(this.nats.getStatus());

    // 使用响应式监听
    this.unsubscribe = watch(
      () => ({
        venueId: this.venueStore?.venueId,
        macAddress: this.deviceStore?.macAddress
      }),
      async ({ venueId, macAddress }) => {
        // console.log('NatsListenerService - Watch triggered:', { venueId, macAddress });

        if (venueId && macAddress) {
          // 每当设备信息更新，重置监听状态，确保设置
          this.isListening = false;
          // console.log('NatsListenerService - Starting NATS listener...');
          await this.setupNatsListener(venueId, macAddress);
          this.isListening = true;
          // console.log('NatsListenerService - NATS listener started successfully');
        }
      },
      { immediate: true, deep: true }
    );
  }

  // 安排重连尝试
  private scheduleReconnect(venueId: string, macAddress: string): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    // 检查是否已达到最大重试次数
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      // console.warn(`NatsListenerService - 已达到最大重连尝试次数(${this.maxReconnectAttempts})，停止自动重连`);
      return;
    }

    // console.log(`NatsListenerService - 计划在${this.reconnectInterval}ms后进行第${this.reconnectAttempts + 1}次重连尝试`);

    this.reconnectTimer = setTimeout(() => {
      if (venueId && macAddress) {
        this.reconnectAttempts++;
        // console.log(`NatsListenerService - 执行第${this.reconnectAttempts}次重连尝试`);
        this.setupNatsListener(venueId, macAddress).catch(err => {
          // console.error('NatsListenerService - 重连失败:', err);
          // 失败后继续尝试重连
          this.scheduleReconnect(venueId, macAddress);
        });
      }
    }, this.reconnectInterval);
  }

  private async setupNatsListener(venueId: string, macAddress: string) {
    try {
      if (this.nats.getStatus() !== 'connected') {
        // console.log('NatsListenerService - 尝试连接NATS服务器...');

        // 记录NATS服务器URL（不包含token等敏感信息）
        const natsServer = import.meta.env.VITE_NATS_SERVER || '未配置';
        // console.log(`NatsListenerService - NATS服务器: ${natsServer}`);

        await this.nats.connect();

        // 连接后启动心跳检测
        await this.nats.startHeartbeat();
        await this.nats.initHeartbeatCheck();
        // console.log('NatsListenerService - NATS连接和心跳检测已启动');
      } else {
        // console.log('NatsListenerService - NATS已连接，无需重新连接');
      }

      const subject = `bc_${venueId}_${macAddress}`;
      const subject_venue = `bc_${venueId}`;
      console.log(`NatsListenerService - 订阅NATS主题: ${subject}`);
      await this.nats.subscribe(subject, this.messageHandler);
      console.log(`NatsListenerService - 订阅NATS主题: ${subject_venue}`);
      await this.nats.subscribe(subject_venue, this.messageHandler);
    } catch (error) {
      // console.error('NATS监听初始化失败:', error);
      // 连接失败时，主动安排重连
      this.scheduleReconnect(venueId, macAddress);
      throw error; // 重新抛出错误，让调用者知道失败了
    }
  }

  // 手动触发重连
  public async triggerReconnect(): Promise<boolean> {
    try {
      // 确保deviceStore已初始化
      if (!this.deviceStore) {
        this.deviceStore = useDeviceStore();
      }
      if (!this.venueStore) {
        this.venueStore = useVenueStore();
      }
      const venueId = this.venueStore?.venueId;
      const macAddress = this.deviceStore?.macAddress;

      if (!venueId || !macAddress) {
        console.warn('NatsListenerService - 设备信息不完整，无法重连');
        return false;
      }

      console.log('NatsListenerService - 手动触发重连, venueId:', venueId, 'macAddress:', macAddress);
      // 重置重连计数
      this.reconnectAttempts = 0;
      await this.setupNatsListener(venueId, macAddress);
      return true;
    } catch (error) {
      // console.error('NatsListenerService - 手动重连失败:', error);
      return false;
    }
  }

  private messageHandler = async (data: any) => {
    // console.log('NatsListenerService - Received message:', data);

    let promptMsg: string = '';
    let url = 'login';

    // 根据消息类型处理不同的业务逻辑
    switch (data.type) {
      case 'cashier_machine_logout_with_main':
        promptMsg = '您的主收银发生变更，收银机自动退出登录，请重新登录。';
        break;
      case 'cashier_machine_logout':
        promptMsg = '您的账户已被强制登出，请重新登录。';
        break;
      case 'cashier_machine_unbind':
        promptMsg = '您的账户已被解绑，请联系客服。';
        url = 'auth';
        break;

      // 处理包厢状态变更消息
      case 'room_status_changed':
        // console.log('NatsListenerService - 收到包厢状态变更消息:', data);
        try {
          // 确保stageStore已初始化
          if (!this.stageStore) {
            this.stageStore = useStageStore();
          }

          // 刷新包厢状态
          const refreshed = await this.stageStore.refreshStagesFromNats();
          console.log('包厢状态自动刷新', refreshed ? '成功' : '跳过');
        } catch (error) {
          console.error('刷新包厢状态失败:', error);
        }
        return; // 处理完毕，不需要后续登出逻辑

      // 处理包厢通知消息
      case 'call_message':
        // console.log('[notification] NatsListenerService - 收到房间通知信号:', data);
        try {
          // 确保notificationStore已初始化
          if (!this.notificationStore) {
            this.notificationStore = useNotificationStore();
          }

          // 收到通知信号后，获取最新的未读通知列表
          await this.notificationStore.fetchAndUpdateUnreadNotifications();
          console.log('通知列表自动刷新成功');
        } catch (error) {
          console.error('获取最新通知列表失败:', error);
        }
        return; // 处理完毕，不需要后续登出逻辑
    }

    if (promptMsg) {
      // 弹出提示信息
      ElMessage.error(promptMsg);

      // 确保userStore已初始化
      if (!this.userStore) {
        this.userStore = useUserStore();
      }

      // 调用登出动作退出登录
      await this.userStore.logoutAction();

      // 使用 replace 替代 push，并跳转到登录页
      await router.replace({
        path: url,
        query: { redirect: router.currentRoute.value.fullPath }
      });
    }
  };

  public cleanup() {
    this.unsubscribe?.();
    this.isListening = false;

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 清理store引用
    this.userStore = null;
    this.deviceStore = null;
    this.natsStore = null;
    this.stageStore = null;
    this.notificationStore = null;
  }
}

export default new NatsListenerService();
