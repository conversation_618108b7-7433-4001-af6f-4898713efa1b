import request from '@/utils/request';
import { BaseResponse } from '@/types/baseResponse';
import { BookingVO } from '@/types/projectobj';

/** 添加预订请求 */
interface AddBookingRequest {
  /** 场馆ID */
  venueId: string;
  /** 房间ID */
  roomId: string;
  /** 客户名称 */
  customerName: string;
  /** 客户电话 */
  customerPhone: string;
  /** 客户来源 */
  customerSource: string;
  /** 性别 0 男 1 女 3 未知 */
  gender: string;
  /** 会员卡 */
  memberCard?: string;
  /** 会员卡id */
  memberCardId?: string;
  /** 开台方案 */
  openTablePlan?: string;
  /** 备注 */
  remark?: string;
  /** 预抵时间 */
  arrivalTime: number;
}

/** 更新预订请求 */
interface UpdateBookingRequest extends AddBookingRequest {
  /** 预订ID */
  id: string;
}

/** 查询预订请求 */
export interface QueryBookingRequest {
  /** 预抵时间 */
  arrivalTime?: number;
  /** 预抵时间开始时间 */
  arrivalTimeStart?: number;
  /** 预抵时间结束时间 */
  arrivalTimeEnd?: number;
  /** 客户名称 */
  customerName?: string;
  /** 客户电话 */
  customerPhone?: string;
  /** 客户来源 */
  customerSource?: string;
  /** 性别 0 男 1 女 3 未知 */
  gender?: string;
  /** ID */
  id?: string;
  /** 会员卡 */
  memberCard?: string;
  /** 会员卡id */
  memberCardId?: string;
  /** 开台方案 */
  openTablePlan?: string;
  /** 页码 */
  pageNum?: number;
  /** 每页记录数 */
  pageSize?: number;
  /** 房间ID */
  roomId?: string;
  /** 房间名称 */
  roomName?: string;
  /** 场地ID */
  venueId?: string;
}

export class BookingApi {
  /**
   * 添加预订
   * @param params - 添加预订参数
   */
  static async addBooking(params: AddBookingRequest): Promise<BaseResponse<BookingVO>> {
    return request.post('/api/booking/add', params);
  }

  /**
   * 更新预订信息
   * @param params - 更新预订参数
   */
  static async updateBooking(params: UpdateBookingRequest): Promise<BaseResponse<BookingVO>> {
    return request.post('/api/booking/update', params);
  }

  /**
   * 删除预订
   * @param id - 预订ID
   */
  static async deleteBooking(id: string): Promise<BaseResponse<void>> {
    return request.post('/api/booking/delete', { id });
  }

  /**
   * 查询预订列表
   * @param params - 查询参数
   */
  static async queryBookings(params: QueryBookingRequest): Promise<BaseResponse<BookingVO[]>> {
    return request.post('/api/booking/query', params);
  }

  /**
   * 获取预订列表
   * @param params - 查询参数
   */
  static async getBookingList(params: QueryBookingRequest): Promise<BaseResponse<BookingVO[]>> {
    return request.post('/api/booking/list', params);
  }

  /**
   * 取消预订
   * @param bookingId - 预订ID
   * @param cancelReason - 取消原因
   */
  static async cancelBooking(bookingId: string, cancelReason: string): Promise<BaseResponse<void>> {
    return request.post('/api/booking/cancel', {
      bookingId,
      cancelReason
    });
  }
}
