import request from "@/utils/request";
import * as Types from "../shared/types";

/** 创建出品单打印任务 根据订单号创建出品单打印任务，返回PrintTask格式数据 POST /api/print-record/product-out/create */
export async function postApiPrintRecordProductOutCreate (body: Types.CreateProductOutPrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOutPrintTaskVO[]>('/api/print-record/product-out/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据会话ID获取出品单打印记录 根据会话ID获取出品单打印记录 POST /api/print-record/product-out/session */
export async function postApiPrintRecordProductOutSession (body: Types.GetProductOutPrintRecordsBySessionIdReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOutPrintRecordVO[]>('/api/print-record/product-out/session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建交班单打印记录 创建交班单打印记录 POST /api/print-record/shift-change/create */
export async function postApiPrintRecordShiftChangeCreate (body: Types.CreateShiftChangePrintRecordReq, options?: { [key: string]: any }) {
  return request<Types.ShiftChangePrintRecordVO>('/api/print-record/shift-change/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据交班单号获取交班单打印记录 根据交班单号获取交班单打印记录 POST /api/print-record/shift-change/hand-no */
export async function postApiPrintRecordShiftChangeHandNo (body: Types.GetShiftChangePrintRecordsByHandNoReqDto, options?: { [key: string]: any }) {
  return request<Types.ShiftChangePrintRecordVO[]>('/api/print-record/shift-change/hand-no', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建结账单打印记录 创建结账单打印记录 POST /api/print-record/checkout/create */
export async function postApiPrintRecordCheckoutCreate (body: Types.CreateCheckoutPrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.CheckoutPrintRecordVO>('/api/print-record/checkout/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据账单号获取结账单打印记录 根据账单号获取结账单打印记录 POST /api/print-record/checkout/pay-bill-id */
export async function postApiPrintRecordCheckoutPayBillId (body: Types.GetCheckoutPrintRecordsByPayBillIdReqDto, options?: { [key: string]: any }) {
  return request<Types.CheckoutPrintRecordVO[]>('/api/print-record/checkout/pay-bill-id', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据会话ID获取结账单打印记录 根据会话ID获取结账单打印记录 POST /api/print-record/checkout/session */
export async function postApiPrintRecordCheckoutSession (body: Types.GetCheckoutPrintRecordsBySessionIdReqDto, options?: { [key: string]: any }) {
  return request<Types.CheckoutPrintRecordVO[]>('/api/print-record/checkout/session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建开台单打印记录 创建开台单打印记录 POST /api/print-record/open-table/create */
export async function postApiPrintRecordOpenTableCreate (body: Types.CreateOpenTablePrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.OpenTablePrintRecordVO>('/api/print-record/open-table/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据会话ID获取开台单打印记录 根据会话ID获取开台单打印记录 POST /api/print-record/open-table/session */
export async function postApiPrintRecordOpenTableSession (body: Types.GetOpenTablePrintRecordsBySessionIdReqDto, options?: { [key: string]: any }) {
  return request<Types.OpenTablePrintRecordVO[]>('/api/print-record/open-table/session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建续房单打印记录 创建续房单打印记录 POST /api/print-record/room-extension/create */
export async function postApiPrintRecordRoomExtensionCreate (body: Types.CreateRoomExtensionPrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExtensionPrintRecordVO>('/api/print-record/room-extension/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据会话ID获取续房单打印记录 根据会话ID获取续房单打印记录 POST /api/print-record/room-extension/session */
export async function postApiPrintRecordRoomExtensionSession (body: Types.GetRoomExtensionPrintRecordsBySessionIdReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExtensionPrintRecordVO[]>('/api/print-record/room-extension/session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
