import { ref, computed } from 'vue';
import type { IPackageDialogViewModel, SelectedPackageProduct } from './viewmodel';
import type { ProductPackageVO } from '@/types/projectobj';
import { usePackageDialogConverter } from './converter';
import { isOptionalByCount, isOptionalByPlan } from '@/utils/productPackageUtils';

// 引入扩展接口
interface ExtendedProductPackageVO extends ProductPackageVO {
  packageDetail?: string | any;
}

// 定义可选组视图模型类型，用于组件视图层
export interface OptionalGroupViewModel {
  name: string;
  optionCount: number;
  optionType: 'by_count' | 'by_plan';
  products: Array<{
    id: string;
    name: string;
    currentPrice: number;
    count: number;
    selected_count: number;
    // 对于by_plan类型，保存原始的count作为基数
    originalCount?: number;
    // 标记是否为by_plan类型的商品
    isPlanProduct?: boolean;
  }>;
}

// 扩展类型以处理packageDetail属性
interface PackageConfirmData extends ExtendedProductPackageVO {
  packageDetail?: string;
}

export class PackageDialogPresenter implements IPackageDialogViewModel {
  private converter = usePackageDialogConverter();

  // 内部状态
  private _state = ref({
    packageData: null as ExtendedProductPackageVO | null,
    visible: false,
    packageQuantity: 1,
    // 新增：维护响应式的可选组数据
    optionalGroupsData: [] as OptionalGroupViewModel[],
    // 新增：跟踪套餐内所选商品的状态
    selectedPackageProducts: [] as SelectedPackageProduct[]
  });

  constructor(initialData?: ExtendedProductPackageVO) {
    if (initialData) {
      this._state.value.packageData = initialData;
      // 初始化可选组数据
      this._state.value.optionalGroupsData = this.converter.getOptionalGroups(initialData as any);
      // 初始化已选商品状态
      this.initializeSelectedProducts();
    }
  }

  // 初始化已选商品状态
  private initializeSelectedProducts() {
    if (!this._state.value.packageData) return;

    const selectedProducts: SelectedPackageProduct[] = [];

    // 添加默认商品
    const defaultProducts = this.converter.getDefaultProducts(this._state.value.packageData as any);
    defaultProducts.forEach((product: any) => {
      selectedProducts.push({
        id: product.id,
        name: product.name,
        count: product.count,
        currentPrice: product.currentPrice
      });
    });

    // 添加已选的可选组商品
    this._state.value.optionalGroupsData.forEach((group, groupIndex) => {
      group.products.forEach((product: any, productIndex: number) => {
        if (product.selected_count > 0) {
          selectedProducts.push({
            id: product.id,
            name: product.name,
            count: product.selected_count,
            currentPrice: product.currentPrice,
            groupIndex,
            productIndex
          });
        }
      });
    });

    this._state.value.selectedPackageProducts = selectedProducts;
  }

  // 生成套餐配置唯一标识
  private generateConfigFingerprint(selectedProducts: SelectedPackageProduct[]): string {
    // 按ID排序以确保相同选择但顺序不同的产品生成相同的指纹
    const sortedProducts = [...selectedProducts].sort((a, b) => {
      if (a.id === b.id) {
        // 如果ID相同，比较数量
        return a.count - b.count;
      }
      return a.id.localeCompare(b.id);
    });

    // 转换为简化的字符串表示
    const productsStr = sortedProducts.map(p => `${p.id}:${p.count}${p.groupIndex !== undefined ? `:${p.groupIndex}` : ''}`).join('|');

    return productsStr;
  }

  // 计算属性
  public computed = {
    // 计算套餐总价
    totalPrice: computed(() => {
      if (!this._state.value.packageData || this._state.value.packageData.currentPrice === undefined) return 0;

      // 套餐价格是固定的，直接取套餐的 currentPrice
      const packagePrice = this._state.value.packageData.currentPrice || 0;

      // 乘以套餐数量
      return packagePrice * this._state.value.packageQuantity;
    }),

    // 获取默认商品列表
    defaultProducts: computed(() => {
      if (!this._state.value.packageData) return [];
      return this.converter.getDefaultProducts(this._state.value.packageData as any);
    }),

    // 获取可选组列表 - 直接返回响应式数据
    optionalGroups: computed(() => {
      return this._state.value.optionalGroupsData;
    }),

    // 获取已选的可选商品列表 - 用于左侧栏显示
    selectedOptionalItems: computed(() => {
      // 基于optionalGroupsData过滤出有已选商品的组
      return (
        this._state.value.optionalGroupsData
          .map(group => ({
            ...group,
            // 只包含已选数量大于0的商品
            products: group.products.filter(p => p.selected_count > 0)
          }))
          // 过滤掉没有选中任何商品的组
          .filter(group => group.products.length > 0)
      );
    }),

    // 验证是否可以提交
    isValid: computed(() => {
      if (!this._state.value.packageData) return false;
      return this.converter.validate(this._state.value.packageData as any);
    }),

    // 计算所有已选商品
    allSelectedProducts: computed(() => {
      return this._state.value.selectedPackageProducts;
    })
  };

  // 暴露状态
  public get state() {
    return this._state.value;
  }

  // 动作实现
  public actions = {
    updateProductCount: (groupIndex: number, productIndex: number, count: number) => {
      if (!this._state.value.packageData) return;

      // 1. 更新响应式数据
      const group = this._state.value.optionalGroupsData[groupIndex];
      if (!group) return;

      // 计算当前总数（不包括当前要修改的商品）
      const currentTotal = group.products.reduce((sum: number, p: any, idx: number) => {
        if (idx === productIndex) return sum;
        return sum + p.count * (p.selected_count || 0);
      }, 0);

      const product = group.products[productIndex];
      if (!product) return;

      if (isOptionalByCount(group)) {
        // 计算剩余可选数量
        const remainingCount = group.optionCount - currentTotal;
        const maxAllowed = Math.floor(remainingCount / product.count);
        // 限制不超过最大允许数量
        count = Math.min(count, maxAllowed);
      } else if (isOptionalByPlan(group)) {
        // by_plan 的逻辑保持不变
        count = count > 0 ? 1 : 0;
        // ... 其他 by_plan 逻辑
      }

      // 更新选择数量
      product.selected_count = count;

      // 2. 同步更新到 packageData
      if (this._state.value.packageData) {
        this._state.value.packageData.optionalGroups = JSON.stringify(this._state.value.optionalGroupsData);
      }

      // 3. 更新已选商品状态
      if (count > 0) {
        // 更新或添加到已选商品列表
        this.actions.updateSelectedProduct({
          id: product.id,
          name: product.name,
          count: product.selected_count,
          currentPrice: product.currentPrice,
          groupIndex,
          productIndex
        });
      } else {
        // 如果数量为0，从已选商品列表中移除
        this.actions.removeSelectedProduct(product.id);
      }
    },

    // 修改为不需要显式传递参数的版本，直接使用内部状态
    convertToConfirmData: () => {
      if (!this._state.value.packageData) return {} as ProductPackageVO;

      // 确保在转换数据时包含所选商品的信息
      const confirmData = this.converter.convertToConfirmData(
        this._state.value.packageData as any,
        this._state.value.packageQuantity
      ) as unknown as PackageConfirmData;

      // 添加已选商品明细
      if (this._state.value.selectedPackageProducts.length > 0) {
        // 在原有packageDetail基础上添加选中商品信息
        const selectedProductsInfo = {
          selectedProducts: this._state.value.selectedPackageProducts
        };

        try {
          // 尝试解析现有的packageDetail
          const existingDetail = confirmData.packageDetail ? JSON.parse(confirmData.packageDetail) : {};
          // 合并选中商品信息
          const mergedDetail = { ...existingDetail, ...selectedProductsInfo };

          // 生成配置指纹，用于识别相同配置的套餐
          const configFingerprint = this.generateConfigFingerprint(this._state.value.selectedPackageProducts);
          mergedDetail.configFingerprint = configFingerprint;

          // 重新序列化
          confirmData.packageDetail = JSON.stringify(mergedDetail);

          // 添加一个可识别的字段用于购物车比较
          (confirmData as any).packageConfigId = configFingerprint;
        } catch (e) {
          // 如果解析失败，直接使用新的选中商品信息
          const configFingerprint = this.generateConfigFingerprint(this._state.value.selectedPackageProducts);
          const newDetail = {
            ...selectedProductsInfo,
            configFingerprint
          };
          confirmData.packageDetail = JSON.stringify(newDetail);
          (confirmData as any).packageConfigId = configFingerprint;
        }
      }

      return confirmData as unknown as ProductPackageVO;
    },

    // 更新已选商品状态
    updateSelectedProduct: (product: SelectedPackageProduct) => {
      const selectedProducts = this._state.value.selectedPackageProducts;
      const existingIndex = selectedProducts.findIndex(p => p.id === product.id);

      if (existingIndex >= 0) {
        // 更新现有商品
        selectedProducts[existingIndex] = product;
      } else {
        // 添加新商品
        selectedProducts.push(product);
      }
    },

    // 移除已选商品
    removeSelectedProduct: (productId: string) => {
      this._state.value.selectedPackageProducts = this._state.value.selectedPackageProducts.filter(p => p.id !== productId);
    }
  };
}

// 导出组合式函数
export function usePackageDialog(initialData?: ExtendedProductPackageVO): IPackageDialogViewModel {
  console.log('usePackageDialog', initialData);
  return new PackageDialogPresenter(initialData);
}
