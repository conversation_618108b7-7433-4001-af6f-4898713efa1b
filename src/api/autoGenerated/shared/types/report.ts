// report相关类型定义
import { BusinessOverview, PayTypeData, BusinessData } from './other';
import { PayBillVO, MemberCardPayData, MemberCardRechargeData } from './member';

export type AddFinancialReportReqDto = {
    /** 赠品金额(JSON) */
    giftAmount?: string;
    /** 赠品数量(JSON) */
    giftQuantity?: string;
    /** 积分兑换金额(JSON) */
    pointsExchangeAmount?: string;
    /** 积分兑换数量(JSON) */
    pointsExchangeQuantity?: string;
    /** 产品实收款(JSON) */
    productActualReceived?: string;
    /** 产品应收款(JSON) */
    productReceivable?: string;
    /** 产品销售额(JSON) */
    productSalesAmount?: string;
    /** 产品销售数量(JSON) */
    productSalesQuantity?: string;
    /** 充值收入 */
    rechargeIncome?: number;
    /** 充值退款 */
    rechargeRefund?: number;
    /** 核销明细(JSON) */
    redemptionDetails?: string;
    /** 报表日期 */
    reportDate?: number;
    /** 报表ID */
    reportId?: string;
    /** 报表类型 */
    reportType?: string;
    /** 预订收入 */
    reservationIncome?: number;
    /** 预订退款 */
    reservationRefund?: number;
    /** 房间实收款(JSON) */
    roomActualReceived?: string;
    /** 房间应收款(JSON) */
    roomReceivable?: string;
  };

export type AddReportReqDto = {
    /** 生成日期 */
    generationDate?: number;
    /** 报表ID */
    reportId?: string;
    /** 类型 */
    type?: string;
  };

export type AddReportTypeReqDto = {
    /** 描述 */
    description?: string;
    /** 频率 */
    frequency?: string;
    /** 报表类型名称 */
    name?: string;
  };

export type AddShiftReportReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 赠送产品列表(JSON) */
    giftedProducts?: string;
    /** 赠送产品总金额 */
    giftedProductsAmount?: number;
    /** 会员消费金额 */
    memberConsumption?: number;
    /** 会员充值金额 */
    memberRecharge?: number;
    /** 开台金额 */
    openedAmount?: number;
    /** 开台数量 */
    openedTables?: number;
    /** 支付方式统计(JSON) */
    paymentMethodStats?: string;
    /** 退货产品列表(JSON) */
    returnedProducts?: string;
    /** 退货产品总金额 */
    returnedProductsAmount?: number;
    /** 已结算金额 */
    settledAmount?: number;
    /** 已结算台数 */
    settledTables?: number;
    /** 班次时间 */
    shiftTime?: number;
    /** 销售产品列表(JSON) */
    soldProducts?: string;
    /** 销售产品总金额 */
    soldProductsAmount?: number;
    /** 未结算金额 */
    unsettledAmount?: number;
    /** 未结算台数 */
    unsettledTables?: number;
    /** 门店ID */
    venueId?: string;
  };

export type DailyShiftReportVO = {
    incomePaied?: {
      actualIncome?: number;
      businessIncome?: number;
      chargebackActual?: number;
      chargebackDiscount?: number;
      chargebackIncome?: number;
      memberCardPayment?: number;
      memberDiscount?: number;
      merchantDiscount?: number;
      minConsumeAdjust?: number;
      netIncome?: number;
      pendingTotal?: number;
      roundDown?: number;
    };
    payTypePaied?: {
      alipay?: number;
      bankCard?: number;
      cash?: number;
      coupon?: number;
      generalBonus?: number;
      koubei?: number;
      meituan?: number;
      memberCardPrincipal?: number;
      productBonus?: number;
      roomBonus?: number;
      wechat?: number;
    };
    tableOpened?: { closedTables?: number; openTables?: number; settledOrders?: number; totalTables?: number; unsettledOrders?: number };
  };

export type DeleteFinancialReportReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteReportReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteReportTypeReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteShiftReportReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type FinancialReportVO = {
    /** 创建时间 */
    ctime?: number;
    /** 赠品金额(JSON) */
    giftAmount?: string;
    /** 赠品数量(JSON) */
    giftQuantity?: string;
    /** 唯一id */
    id?: string;
    /** 积分兑换金额(JSON) */
    pointsExchangeAmount?: string;
    /** 积分兑换数量(JSON) */
    pointsExchangeQuantity?: string;
    /** 产品实收款(JSON) */
    productActualReceived?: string;
    /** 产品应收款(JSON) */
    productReceivable?: string;
    /** 产品销售额(JSON) */
    productSalesAmount?: string;
    /** 产品销售数量(JSON) */
    productSalesQuantity?: string;
    /** 充值收入 */
    rechargeIncome?: number;
    /** 充值退款 */
    rechargeRefund?: number;
    /** 核销明细(JSON) */
    redemptionDetails?: string;
    /** 报表日期 */
    reportDate?: number;
    /** 报表ID */
    reportId?: string;
    /** 报表类型 */
    reportType?: string;
    /** 预订收入 */
    reservationIncome?: number;
    /** 预订退款 */
    reservationRefund?: number;
    /** 房间实收款(JSON) */
    roomActualReceived?: string;
    /** 房间应收款(JSON) */
    roomReceivable?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type PageVOArrayVoFinancialReportVO = {
    data?: FinancialReportVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoReportVO = {
    data?: ReportVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoShiftReportVO = {
    data?: ShiftReportVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type QueryDailyShiftReportReqDto = {
    /** 结束时间 */
    endTime: number;
    /** 开始时间 */
    startTime: number;
    /** 门店ID */
    venueId: string;
  };

export type QueryFinancialReportReqDto = {
    /** 唯一id */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 报表日期 */
    reportDate?: number;
    /** 报表ID */
    reportId?: string;
    /** 报表类型 */
    reportType?: string;
  };

export type QueryReportReqDto = {
    /** 生成日期 */
    generationDate?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 报表ID */
    reportId?: string;
    /** 类型 */
    type?: string;
  };

export type QueryReportTypeReqDto = {
    /** 描述 */
    description?: string;
    /** 频率 */
    frequency?: string;
    /** 唯一id */
    id?: string;
    /** 报表类型名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryShiftReportBillDetailReqDto = {
    /** 账单ID */
    billId?: string;
    /** 员工ID */
    employeeId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryShiftReportGetIncomeDailyReqDto = {
    /** 日期 */
    date?: number;
    /** 员工ID */
    employeeId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryShiftReportGetIncomeReportReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryShiftReportGetPayBillsReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryShiftReportHandOverReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryShiftReportReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 赠送产品列表(JSON) */
    giftedProducts?: string;
    /** 赠送产品总金额 */
    giftedProductsAmount?: number;
    /** 唯一ID */
    id?: string;
    /** 会员消费金额 */
    memberConsumption?: number;
    /** 会员充值金额 */
    memberRecharge?: number;
    /** 开台金额 */
    openedAmount?: number;
    /** 开台数量 */
    openedTables?: number;
    /** 排序字段 */
    orderBy?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 支付方式统计(JSON) */
    paymentMethodStats?: string;
    /** 退货产品列表(JSON) */
    returnedProducts?: string;
    /** 退货产品总金额 */
    returnedProductsAmount?: number;
    /** 已结算金额 */
    settledAmount?: number;
    /** 已结算台数 */
    settledTables?: number;
    /** 班次时间 */
    shiftTime?: number;
    /** 销售产品列表(JSON) */
    soldProducts?: string;
    /** 销售产品总金额 */
    soldProductsAmount?: number;
    /** 开始时间 */
    startTime?: number;
    /** 未结算金额 */
    unsettledAmount?: number;
    /** 未结算台数 */
    unsettledTables?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ReportTypeVO = {
    /** 创建时间 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** 频率 */
    frequency?: string;
    /** 唯一id */
    id?: string;
    /** 报表类型名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type ReportVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 生成日期 */
    generationDate?: number;
    /** ID */
    id?: string;
    /** 报表ID */
    reportId?: string;
    /** 状态值 */
    state?: number;
    /** 类型 */
    type?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type ResultArrayVoFinancialReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: FinancialReportVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoReportTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ReportTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ReportVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoShiftReportDaily = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftReportDaily[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoShiftReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftReportVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoDailyShiftReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: DailyShiftReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoFinancialReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: FinancialReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoFinancialReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoFinancialReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoShiftReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoShiftReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoReportTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ReportTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoShiftReportBillDetailVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftReportBillDetailVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoShiftReportGetIncomeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftReportGetIncomeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoShiftReportVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ShiftReportVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ShiftReportBillDetailVO = {
    /** 账单ID */
    billId?: string;
    /** 营业概况 */
    businessOverview?: BusinessOverview;
    /** 员工ID */
    employeeId?: string;
    /** 支付单VO */
    payBillVO?: PayBillVO;
    /** 支付方式数据 */
    payTypeData?: PayTypeData;
    /** 门店ID */
    venueId?: string;
  };

export type ShiftReportDaily = {
    /** 账单日期 */
    billDate?: string;
    /** 营业数据 */
    businessData?: BusinessData;
    /** 营业概况 */
    businessOverview?: BusinessOverview;
    /** 员工ID */
    employeeId?: string;
    endTime?: number;
    /** 交班时间 */
    handTime?: number;
    /** 会员卡支付数据 消费/退款 */
    memberCardPayData?: MemberCardPayData;
    /** 会员卡充值数据 充值/退款 */
    memberCardRechargeData?: MemberCardRechargeData;
    /** 收款单VO */
    payBillVOs?: PayBillVO[];
    /** 支付方式数据 */
    payTypeData?: PayTypeData;
    startTime?: number;
    /** 门店营业结束时间 */
    venueEndHour?: string;
    /** 门店ID */
    venueId?: string;
  };

export type ShiftReportGetIncomeVO = {
    /** 支付宝支付 */
    alipayPayAmount?: number;
    /** 银行卡支付 */
    bankCardPayAmount?: number;
    /** 营业实收 */
    businessActual?: number;
    /** 营业净收 */
    businessNet?: number;
    /** 营业应收 */
    businessReceivable?: number;
    /** 现金支付 */
    cashPayAmount?: number;
    /** 招待券支付 */
    couponPayAmount?: number;
    /** 挂账 */
    creditAmount?: number;
    /** 带客批数 */
    customerBatches?: number;
    /** 日期 */
    dt?: string;
    /** 员工id */
    employeeId?: string;
    /** 通用赠金 */
    generalGiftAmount?: number;
    /** 口碑支付 */
    koubeiPayAmount?: number;
    /** 美团支付 */
    meituanPayAmount?: number;
    /** 会员卡支付 */
    memberCardPay?: number;
    /** 会员卡本金 */
    memberCardPrincipalAmount?: number;
    /** 会员优惠 */
    memberDiscount?: number;
    /** 商家优惠 */
    merchantDiscount?: number;
    /** 低消差额调整 */
    minimumAdjustment?: number;
    /** 商品赠金 */
    productGiftAmount?: number;
    /** 充值金额 */
    rechargeAmount?: number;
    /** 充值赠送 */
    rechargeGift?: number;
    /** 包厢赠金 */
    roomGiftAmount?: number;
    /** 员工商品赠送 */
    staffGifts?: number;
    /** 营收情况 */
    venueId?: string;
    /** 付款方式 */
    wechatPayAmount?: number;
    /** 冲账实收 */
    writeOffActual?: number;
    /** 冲账优惠 */
    writeOffDiscount?: number;
    /** 冲账应收 */
    writeOffReceivable?: number;
  };

export type ShiftReportVO = {
    /** 创建时间 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** 赠送产品列表(JSON) */
    giftedProducts?: string;
    /** 赠送产品总金额 */
    giftedProductsAmount?: number;
    /** 唯一ID */
    id?: string;
    /** 会员消费金额 */
    memberConsumption?: number;
    /** 会员充值金额 */
    memberRecharge?: number;
    /** 开台金额 */
    openedAmount?: number;
    /** 开台数量 */
    openedTables?: number;
    /** 支付方式统计(JSON) */
    paymentMethodStats?: string;
    /** 退货产品列表(JSON) */
    returnedProducts?: string;
    /** 退货产品总金额 */
    returnedProductsAmount?: number;
    /** 已结算金额 */
    settledAmount?: number;
    /** 已结算台数 */
    settledTables?: number;
    /** 班次时间 */
    shiftTime?: number;
    /** 销售产品列表(JSON) */
    soldProducts?: string;
    /** 销售产品总金额 */
    soldProductsAmount?: number;
    /** 状态 */
    state?: number;
    /** 未结算金额 */
    unsettledAmount?: number;
    /** 未结算台数 */
    unsettledTables?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type UpdateFinancialReportReqDto = {
    /** 赠品金额(JSON) */
    giftAmount?: string;
    /** 赠品数量(JSON) */
    giftQuantity?: string;
    /** 唯一id */
    id?: string;
    /** 积分兑换金额(JSON) */
    pointsExchangeAmount?: string;
    /** 积分兑换数量(JSON) */
    pointsExchangeQuantity?: string;
    /** 产品实收款(JSON) */
    productActualReceived?: string;
    /** 产品应收款(JSON) */
    productReceivable?: string;
    /** 产品销售额(JSON) */
    productSalesAmount?: string;
    /** 产品销售数量(JSON) */
    productSalesQuantity?: string;
    /** 充值收入 */
    rechargeIncome?: number;
    /** 充值退款 */
    rechargeRefund?: number;
    /** 核销明细(JSON) */
    redemptionDetails?: string;
    /** 报表日期 */
    reportDate?: number;
    /** 报表ID */
    reportId?: string;
    /** 报表类型 */
    reportType?: string;
    /** 预订收入 */
    reservationIncome?: number;
    /** 预订退款 */
    reservationRefund?: number;
    /** 房间实收款(JSON) */
    roomActualReceived?: string;
    /** 房间应收款(JSON) */
    roomReceivable?: string;
  };

export type UpdateReportReqDto = {
    /** 生成日期 */
    generationDate?: number;
    /** ID */
    id?: string;
    /** 报表ID */
    reportId?: string;
    /** 类型 */
    type?: string;
  };

export type UpdateReportTypeReqDto = {
    /** 描述 */
    description?: string;
    /** 频率 */
    frequency?: string;
    /** 唯一id */
    id?: string;
    /** 报表类型名称 */
    name?: string;
  };

export type UpdateShiftReportReqDto = {
    /** 赠送产品列表(JSON) */
    giftedProducts?: string;
    /** 赠送产品总金额 */
    giftedProductsAmount?: number;
    /** 唯一ID */
    id?: string;
    /** 会员消费金额 */
    memberConsumption?: number;
    /** 会员充值金额 */
    memberRecharge?: number;
    /** 开台金额 */
    openedAmount?: number;
    /** 开台数量 */
    openedTables?: number;
    /** 支付方式统计(JSON) */
    paymentMethodStats?: string;
    /** 退货产品列表(JSON) */
    returnedProducts?: string;
    /** 退货产品总金额 */
    returnedProductsAmount?: number;
    /** 已结算金额 */
    settledAmount?: number;
    /** 已结算台数 */
    settledTables?: number;
    /** 班次时间 */
    shiftTime?: number;
    /** 销售产品列表(JSON) */
    soldProducts?: string;
    /** 销售产品总金额 */
    soldProductsAmount?: number;
    /** 未结算金额 */
    unsettledAmount?: number;
    /** 未结算台数 */
    unsettledTables?: number;
  };

export type V3QueryBuisinessReportReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type V3QueryShiftReportHandOverDetailReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 交班记录ID */
    handNo?: string;
    /** 主键ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3ShiftReportHandOverReqDto = {
    /** 日期 */
    date?: number;
    /** 员工ID */
    employeeId?: string;
    /** 日报信息 */
    shiftReportDaily?: ShiftReportDaily[];
    /** 门店ID */
    venueId?: string;
  };

export type VenueBusinessReportVO = {
    /** 营业概览 */
    businessOverview?: VenueBusinessReportVOBusinessOverview;
    /** 营业结束时间 */
    endHour?: string;
    /** 结束时间 */
    endTime?: number;
    /** 会员概览 */
    memberOverview?: VenueBusinessReportVOMemberOverview;
    /** 场次概览 */
    sessionOverview?: VenueBusinessReportVOSessionOverview;
    /** 营业开始时间 */
    startHour?: string;
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
  };

export type VenueBusinessReportVOBusinessOverview = {
    /** 总营收 */
    totalFee?: number;
    /** 商品营收 */
    totalFeeProduct?: number;
    /** 包房营收 */
    totalFeeRoom?: number;
  };

export type VenueBusinessReportVOMemberOverview = {
    /** 新增会员数 */
    newMemberCount?: number;
    /** 充值金额 */
    rechargeAmount?: number;
    /** 总会员数 */
    totalMemberCount?: number;
  };

export type VenueBusinessReportVOSessionOverview = {
    /** 包房数量 */
    roomCount?: number;
    /** 场次数量 */
    sessionCount?: number;
  };
