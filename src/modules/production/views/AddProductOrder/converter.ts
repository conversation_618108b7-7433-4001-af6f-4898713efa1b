import { OpenTableProductConverter } from '../OpenTableProduct/converter';
import type { OrderProductVO, RoomVO } from '@/types/projectobj';
import { omit } from 'lodash';

export class AddProductOrderConverter extends OpenTableProductConverter {
  /**
   * 将购物车商品转换为订单商品
   */
  static cartItemsToOrderProducts(cartItems: any[]): any[] {
    return cartItems.map(item => {
      // 基础字段
      const baseProduct = {
        id: '', // 添加必需的id字段
        venueId: '', // 添加必需的venueId字段
        roomId: '', // 添加必需的roomId字段
        sessionId: '', // 添加必需的sessionId字段
        productName: item.name,
        flavors: item.flavor || '',
        quantity: item.quantity,
        unit: item.unit,
        payPrice: item.currentPrice,
        originalPrice: item.currentPrice,
        payAmount: item.currentPrice * item.quantity!,
        originalAmount: item.currentPrice * item.quantity!,
        payStatus: 'unpaid'
      };

      // 套餐特殊处理
      if (item.isPackage) {
        // 解析套餐明细，提取包含商品信息
        let packageProductInfo: any[] = [];
        const productMap = new Map<string, any>(); // 用于去重
        let packageDetail: any = null; // 在外部声明packageDetail变量

        try {
          // 尝试从packageDetail中解析选择的商品信息
          if (item.packageDetail) {
            // 如果是字符串格式需要解析
            packageDetail = typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : item.packageDetail;

            // 处理默认产品
            if (packageDetail.packageProducts && typeof packageDetail.packageProducts === 'string') {
              const defaultProducts = JSON.parse(packageDetail.packageProducts);
              if (Array.isArray(defaultProducts)) {
                defaultProducts.forEach((product: any) => {
                  const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
                  if (productInfo) {
                    const key = product.id;
                    if (!productMap.has(key)) {
                      productMap.set(key, {
                        id: product.id,
                        count: product.count || 1,
                        price: productInfo.price || productInfo.currentPrice || 0, // 优先使用原价
                        name: productInfo.name || '未命名商品'
                      });
                    } else {
                      // 如果已存在，累加数量
                      const existing = productMap.get(key);
                      existing.count += product.count || 1;
                    }
                  } else {
                    // 如果找不到商品信息，使用默认价格
                    const key = product.id;
                    if (!productMap.has(key)) {
                      productMap.set(key, {
                        id: product.id,
                        count: product.count || 1,
                        price: 0, // 默认价格为0
                        name: `商品(${product.id})`
                      });
                    }
                  }
                });
              }
            }

            // 处理可选商品
            if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts)) {
              packageDetail.selectedProducts.forEach((selectedProduct: any) => {
                const productInfo = packageDetail.productVOList?.find((p: any) => p.id === selectedProduct.id);
                if (productInfo) {
                  const key = selectedProduct.id;
                  if (!productMap.has(key)) {
                    const productData = {
                      id: selectedProduct.id,
                      count: selectedProduct.count || 1,
                      price: productInfo.price || productInfo.currentPrice || 0, // 优先使用原价
                      name: productInfo.name || '未命名商品'
                    };
                    productMap.set(key, productData);
                  } else {
                    // 如果已存在，累加数量
                    const existing = productMap.get(key);
                    existing.count += selectedProduct.count || 1;
                  }
                } else {
                  // 如果找不到商品信息，使用默认价格
                  const key = selectedProduct.id;
                  if (!productMap.has(key)) {
                    const productData = {
                      id: selectedProduct.id,
                      count: selectedProduct.count || 1,
                      price: 0, // 默认价格为0
                      name: `商品(${selectedProduct.id})`
                    };
                    productMap.set(key, productData);
                  }
                }
              });
            }

            // 处理可选组商品（optionalGroups）
            if (packageDetail.optionalGroups && Array.isArray(packageDetail.optionalGroups)) {
              packageDetail.optionalGroups.forEach((group: any) => {
                if (group.products && Array.isArray(group.products)) {
                  group.products.forEach((product: any) => {
                    const selectedCount = product.selected_count || 0;

                    if (selectedCount > 0) {
                      const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
                      if (productInfo) {
                        const key = product.id;

                        // 🔥 修复：根据可选组类型正确计算商品数量
                        let finalCount = selectedCount;
                        if (group.optionType === 'by_plan') {
                          // by_plan类型：显示数量 = count × selected_count
                          finalCount = (product.count || 1) * selectedCount;
                        } else {
                          // by_count类型：显示数量 = selected_count
                          finalCount = selectedCount;
                        }

                        if (!productMap.has(key)) {
                          const productData = {
                            id: product.id,
                            count: finalCount, // 使用修正后的数量
                            price: productInfo.price || productInfo.currentPrice || 0, // 优先使用原价
                            name: productInfo.name || '未命名商品'
                          };
                          productMap.set(key, productData);
                        } else {
                          // 如果已存在，累加数量
                          const existing = productMap.get(key);
                          existing.count += finalCount; // 使用修正后的数量
                        }
                      }
                    }
                  });
                }
              });
            }

            // 将Map转换为数组
            packageProductInfo = Array.from(productMap.values());
          }
        } catch (error) {
          console.error('解析套餐明细失败:', error);
          // 发生错误时使用基本信息
          packageProductInfo = [
            {
              id: item.id,
              count: item.quantity,
              price: item.currentPrice || 0,
              name: item.name || '未命名商品'
            }
          ];
        }

        return {
          ...baseProduct,
          packageId: item.id, // 使用packageId替代productId
          packageProductInfo: JSON.stringify(packageProductInfo), // 序列化时不包含name字段
          inPackageTag: 'yes'
        };
      } else {
        // 普通商品处理
        return {
          ...baseProduct,
          productId: item.id,
          inPackageTag: 'no'
        };
      }
    });
  }

  /**
   * 格式化套餐商品信息
   * 用于在提交订单时格式化套餐内商品信息（不包含name字段）
   */
  static formatPackageProductInfo(item: any): { id: string; count: number; price: number }[] {
    if (!item.isPackage || !item.packageDetail) {
      return [];
    }

    let packageProductInfo: any[] = [];
    const productMap = new Map<string, any>(); // 用于去重

    try {
      // 如果是字符串格式需要解析
      const packageDetail = typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : item.packageDetail;

      // 处理默认产品
      if (packageDetail.packageProducts && typeof packageDetail.packageProducts === 'string') {
        const defaultProducts = JSON.parse(packageDetail.packageProducts);
        if (Array.isArray(defaultProducts)) {
          defaultProducts.forEach((product: any) => {
            const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
            if (productInfo) {
              const key = product.id;
              if (!productMap.has(key)) {
                productMap.set(key, {
                  id: product.id,
                  count: product.count || 1,
                  price: productInfo.price || productInfo.currentPrice || 0 // 优先使用原价
                });
              } else {
                // 如果已存在，累加数量
                const existing = productMap.get(key);
                existing.count += product.count || 1;
              }
            }
          });
        }
      }

      // 处理可选商品
      if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts)) {
        packageDetail.selectedProducts.forEach((selectedProduct: any) => {
          const productInfo = packageDetail.productVOList?.find((p: any) => p.id === selectedProduct.id);
          if (productInfo) {
            const key = selectedProduct.id;
            if (!productMap.has(key)) {
              productMap.set(key, {
                id: selectedProduct.id,
                count: selectedProduct.count || 1,
                price: productInfo.price || productInfo.currentPrice || 0 // 优先使用原价
              });
            } else {
              // 如果已存在，累加数量
              const existing = productMap.get(key);
              existing.count += selectedProduct.count || 1;
            }
          }
        });
      }

      // 将Map转换为数组，移除name字段用于服务端提交
      packageProductInfo = Array.from(productMap.values()).map(product => omit(product, 'name'));
    } catch (error) {
      console.error('格式化套餐商品信息失败:', error);
    }

    return packageProductInfo;
  }

  /**
   * 格式化套餐商品信息用于前端显示（包含name字段）
   * 用于在UI中显示套餐明细
   */
  static formatPackageProductInfoForDisplay(item: any): { id: string; count: number; price: number; name: string }[] {
    if (!item.isPackage || !item.packageDetail) {
      return [];
    }

    let packageProductInfo: any[] = [];
    const productMap = new Map<string, any>(); // 用于去重

    try {
      // 如果是字符串格式需要解析
      const packageDetail = typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : item.packageDetail;

      // 🔥 重要：优先检查selectedProducts，如果存在且有数据，直接使用，避免重复计算
      if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts) && packageDetail.selectedProducts.length > 0) {
        packageDetail.selectedProducts.forEach((selectedProduct: any) => {
          // 🔥 重要：从productVOList中获取正确的价格信息
          const productInfo = packageDetail.productVOList?.find((p: any) => p.id === selectedProduct.id);
          const productPrice = productInfo?.price || productInfo?.currentPrice || selectedProduct.price || 0;

          const productData = {
            id: selectedProduct.id,
            count: selectedProduct.count || 1,
            price: productPrice, // 使用从productVOList获取的价格
            name: selectedProduct.name || productInfo?.name || '未命名商品'
          };

          productMap.set(selectedProduct.id, productData);
        });

        // 直接计算最终数量并返回，不再处理其他数据源
        const packageQuantity = item.quantity || 1;

        packageProductInfo = Array.from(productMap.values()).map(product => {
          return {
            ...product,
            count: product.count * packageQuantity // 商品数量 = 单个套餐中的数量 × 套餐数量
          };
        });

        return packageProductInfo;
      }

      // 处理默认产品
      if (packageDetail.packageProducts && typeof packageDetail.packageProducts === 'string') {
        const defaultProducts = JSON.parse(packageDetail.packageProducts);

        if (Array.isArray(defaultProducts)) {
          defaultProducts.forEach((product: any) => {
            const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
            if (productInfo) {
              const key = product.id;
              if (!productMap.has(key)) {
                const productData = {
                  id: product.id,
                  count: product.count || 1,
                  price: productInfo.price || productInfo.currentPrice || 0, // 优先使用原价
                  name: productInfo.name || '未命名商品'
                };
                productMap.set(key, productData);
              } else {
                // 如果已存在，累加数量
                const existing = productMap.get(key);
                existing.count += product.count || 1;
              }
            }
          });
        }
      }

      // 处理可选组商品（optionalGroups）
      if (packageDetail.optionalGroups && Array.isArray(packageDetail.optionalGroups)) {
        packageDetail.optionalGroups.forEach((group: any) => {
          if (group.products && Array.isArray(group.products)) {
            group.products.forEach((product: any) => {
              const selectedCount = product.selected_count || 0;

              if (selectedCount > 0) {
                const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
                if (productInfo) {
                  const key = product.id;

                  // 🔥 修复：根据可选组类型正确计算商品数量
                  let finalCount = selectedCount;
                  if (group.optionType === 'by_plan') {
                    // by_plan类型：显示数量 = count × selected_count
                    finalCount = (product.count || 1) * selectedCount;
                  } else {
                    // by_count类型：显示数量 = selected_count
                    finalCount = selectedCount;
                  }

                  if (!productMap.has(key)) {
                    const productData = {
                      id: product.id,
                      count: finalCount, // 使用修正后的数量
                      price: productInfo.price || productInfo.currentPrice || 0, // 优先使用原价
                      name: productInfo.name || '未命名商品'
                    };
                    productMap.set(key, productData);
                  } else {
                    // 如果已存在，累加数量
                    const existing = productMap.get(key);
                    existing.count += finalCount; // 使用修正后的数量
                  }
                }
              }
            });
          }
        });
      }

      // 将Map转换为数组，保留name字段用于前端显示
      // 重要：这里需要将每个商品的数量乘以套餐数量
      const packageQuantity = item.quantity || 1;

      packageProductInfo = Array.from(productMap.values()).map(product => {
        return {
          ...product,
          count: product.count * packageQuantity // 商品数量 = 单个套餐中的数量 × 套餐数量
        };
      });
    } catch (error) {
      console.error('格式化套餐商品信息失败:', error);
    }

    return packageProductInfo;
  }

  /**
   * 生成打印数据
   */
  static generatePrintData(params: { orderNo: string; roomVO: RoomVO; employeeName: string; orderProducts: OrderProductVO[]; sessionId: string }) {
    const { orderNo, roomVO, employeeName, orderProducts, sessionId } = params;
    return {
      orderNo,
      roomVO,
      employeeName,
      ctime: Date.now(),
      orderProductVOs: orderProducts,
      sessionId
    };
  }

  /**
   * 计算订单总金额
   */
  static calculateTotalAmounts(cartItems: any[]) {
    // 套餐价格计算逻辑：套餐总价 = 套餐单价 × 数量
    // 套餐内部的商品数量变化不影响套餐总价
    return {
      totalPayAmount: cartItems.reduce((total, item) => total + item.currentPrice * item.quantity!, 0),
      totalOriginalAmount: cartItems.reduce((total, item) => total + item.currentPrice * item.quantity!, 0)
    };
  }
}
