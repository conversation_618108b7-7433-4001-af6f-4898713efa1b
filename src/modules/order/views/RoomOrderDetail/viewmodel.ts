import { ComputedRef } from 'vue';
import type { OrderDetailData, ProductGroup } from '../../types';

// 套餐商品详情接口
export interface PackageProductDetail {
  id: string;
  count: number;
  price: number;
  name: string;
}

// 订单类型
export interface OrderDisplay {
  orderNo: string;
  type: string;
  status: string;
  ctime: number;
  payAmount: number;
  totalAmount: number;
  isPartialRefund: boolean;
  products?: any[];
}

// 选择状态
export interface SelectionState {
  selectAll: boolean;
  orders: string[];
}

// UI状态接口
export interface IRoomOrderDetailState {
  orderData: OrderDetailData | null;
  selectedRoomPlans: SelectionState;
  selectedInPackageOrders: SelectionState;
  selectedOutPackageOrders: SelectionState;
  selectedProducts: string[];
  expandedOrders: string[];
  printDialogVisible: boolean;
  refundMap?: Map<string, Map<string, number>>; // 退款映射: Map<原始订单号, Map<商品ID, 已退数量>>
  // 新增：套餐商品展开状态管理
  expandedPackageProducts: string[]; // 存储已展开的套餐商品ID
  // 配置项
  defaultExpandPackageProducts: boolean; // 是否默认展开套餐商品
}

// UI计算属性接口
export interface IRoomOrderDetailComputed {
  isMinChargeReached: ComputedRef<boolean>;
  groupedProducts: ComputedRef<ProductGroup[]>;
  getRoomPlanPayStatus: ComputedRef<(orderNo: string) => string>;
  // 新增账单分类计算属性
  roomPlanOrders: ComputedRef<OrderDisplay[]>; // 房费订单
  inPackageOrders: ComputedRef<OrderDisplay[]>; // 套餐内订单
  outPackageOrders: ComputedRef<OrderDisplay[]>; // 套餐外订单
  // 新增使用时长计算属性
  usageDuration: ComputedRef<string>; // 使用时长
  getProductRefundInfo: ComputedRef<(product: any) => { refundQuantity: number; refundAmount: number }>;
  // 新增：套餐商品相关计算属性
  getPackageProductDetails: ComputedRef<(packageProductInfo: string) => PackageProductDetail[]>;
  isPackageProductExpanded: ComputedRef<(productId: string) => boolean>;
  // 新增：费用计算属性
  roomFeeAmount: ComputedRef<number>; // 包厢费用
  productFeeAmount: ComputedRef<number>; // 商品费用
  totalConsumptionAmount: ComputedRef<number>; // 消费总计
}

// UI动作接口
export interface IRoomOrderDetailActions {
  handleManageProduct(): void;
  handleSettlementDetail(): void;
  handleCleanComplete(): void;
  handlePrint(): void;
  toggleOrder(orderNo: string): void;
  calculateRoomFeesTotal(): number;
  getStatusText(status: string): string;
  handlePopup(): void;
  handleSettlement(): void;
  // 新增选择功能
  toggleSelectAllRoomPlans(): void;
  toggleSelectAllInPackageOrders(): void;
  toggleSelectAllOutPackageOrders(): void;
  // 结账详情 - 使用BillRestoreDialog
  handleShowPayDetail(): void;
  // 新增：套餐商品展开/收起功能
  togglePackageProduct(productId: string): void;
  // 全部展开/收起套餐商品
  toggleAllPackageProducts(): void;
}

// 总的ViewModel接口
export interface IRoomOrderDetailViewModel {
  state: IRoomOrderDetailState;
  computed: IRoomOrderDetailComputed;
  actions: IRoomOrderDetailActions;
}
