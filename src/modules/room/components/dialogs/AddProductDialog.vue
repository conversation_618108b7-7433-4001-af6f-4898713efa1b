<template>
  <app-dialog v-model="visible">
    <template #title-addon>{{ vm.state.selectedRoom?.name ? ' ' + vm.state.selectedRoom.name : '' }}</template>
    <!-- 商品列表 -->
    <LeftRightLayout :leftClass="'w-[1020px]'" :rightClass="'w-[660px]'" leftTitle="商品列表" rightTitle="商品账单">
      <!-- 左侧商品列表区域 -->
      <template #left>
        <div class="h-full flex flex-col pb-[24px]">
          <!-- 商品内容区域 - 使用flex和子元素定位 -->
          <div class="flex flex-1 relative overflow-hidden">
            <!-- 分类侧边栏 -->
            <div class="w-[160px] h-full overflow-y-auto border-r flex-shrink-0">
              <div class="w-[120px] mx-auto mt-5 flex flex-col gap-4">
                <div
                  v-for="category in categories"
                  :key="category.id || 'all'"
                  class="h-[60px] rounded-lg flex items-center justify-center cursor-pointer transition-colors duration-200"
                  :class="{
                    'bg-red-500 text-white': category.isActive,
                    'text-gray-600 hover:bg-red-50 hover:text-red-600': !category.isActive
                  }"
                  @click="changeCategory(category.id)">
                  <span class="truncate text-base font-medium">{{ category.name }}</span>
                </div>
              </div>
            </div>
            <!-- 商品区域 - 使用flex和el-scrollbar -->
            <div class="flex-1 flex flex-col overflow-hidden">
              <!-- 搜索框 -->
              <div class="p-5 flex-shrink-0">
                <div class="w-full h-[60px] rounded-lg border border-black/10 flex items-center px-6">
                  <input v-model="searchKeyword" placeholder="搜索商品" class="w-full text-lg text-black/40 outline-none font-medium" @input="handleSearch" />
                  <el-button v-if="searchKeyword" link @click="clearSearch" class="ml-2">
                    <el-icon class="text-[24px] text-black/40">
                      <Close />
                    </el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 商品表格 - 使用el-table组件 -->
              <div class="flex-1 px-5 overflow-hidden flex flex-col pb-5">
                <el-table
                  stripe
                  :data="products"
                  class="w-full product-table compact-table flex-1"
                  :row-class-name="tableRowClassName"
                  v-loading="loading"
                  @scroll="handleTableScroll"
                  table-layout="auto"
                  ref="productTableRef">
                  <el-table-column prop="name" label="商品名称" align="left">
                    <template #default="{ row }">
                      <div class="flex flex-col justify-center py-2">
                        <span class="font-medium text-black truncate">{{ row.name }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="80px" align="center">
                    <template #default="{ row }">
                      <el-tag v-if="row.isSoldOut" type="info" size="small" effect="plain" class="text-sm">沽清</el-tag>
                      <el-tag
                        v-else-if="row.hasOptionalGroups"
                        size="small"
                        effect="plain"
                        class="text-sm"
                        style="color: #319cff; border-color: #319cff; background-color: rgba(49, 156, 255, 0.1)"
                        >可选</el-tag
                      >
                      <el-tag v-else-if="row.tag" type="warning" size="small" effect="plain" class="text-sm">{{ row.tag }}</el-tag>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="unit" label="单位" width="80px" align="center">
                    <template #default="{ row }">
                      {{ row.unit || '份' }}
                    </template>
                  </el-table-column>

                  <el-table-column prop="currentPrice" label="单价" width="120px" align="center">
                    <template #default="{ row }">
                      <div class="flex items-center justify-center">
                        <span class="text-[16px] font-medium">{{ formatYuanWithSymbol(row.currentPrice).replace('￥', '') }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="120px" align="center">
                    <template #default="{ row }">
                      <div class="flex justify-center items-center">
                        <erp-input-number :disabled="row.isSoldOut" v-model="row.quantity" simpleMode type="default" @change="handleAddToCart(row)" />
                      </div>
                    </template>
                  </el-table-column>

                  <!-- 添加空数据提示 -->
                  <template #empty>
                    <div class="py-10 text-center">
                      <div class="text-gray-400 mb-2 text-lg">{{ searchKeyword ? '没有找到匹配的商品' : '暂无商品数据' }}</div>
                      <div class="text-gray-400 text-sm">
                        {{ searchKeyword ? `没有找到包含"${searchKeyword}"的商品，请尝试其他关键词` : '请选择其他分类或刷新页面' }}
                      </div>
                    </div>
                  </template>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 右侧订单区域 -->
      <template #right>
        <div class="flex flex-col cart-container h-full">
          <!-- 内容区域（包含商品列表和按钮区） -->
          <div class="flex flex-col h-full">
            <!-- 购物车区域 - 使用el-card和el-table -->
            <el-card class="cart-card !rounded-[12px] mb-4 flex-1 !flex !flex-col !overflow-hidden">
              <template #header>
                <div class="flex justify-between items-center px-2">
                  <div class="text-lg text-black font-semibold">商品账单</div>
                  <div v-if="vm.state.cartItems.length > 0" class="flex items-center text-sm font-semibold cursor-pointer">
                    <el-button link @click="vm.actions.clearCart" text>
                      <span class="text-sm">清空</span>
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 购物车商品表格区域 -->
              <div class="flex-1 overflow-hidden flex flex-col px-[12px]">
                <el-table
                  size="small"
                  ref="cartTable"
                  v-loading="isLoading"
                  :data="processedCartItems"
                  :span-method="objectSpanMethod"
                  :row-class-name="cartItemRowClassName"
                  class="cart-table flex-1">
                  <!-- 商品名称列 -->
                  <el-table-column prop="name" label="商品名称">
                    <template #default="scope">
                      <div v-if="scope.row.isDetail" class="detail-row">
                        <PackageDetailItem
                          :row="scope.row"
                          :isDetail="scope.row.isDetail"
                          :parentId="scope.row.parentId"
                          :parent="scope.row.parentId ? findParentItem(scope.row.parentId) : undefined" />
                      </div>
                      <div v-else class="product-name">{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>

                  <!-- 数量列 -->
                  <el-table-column prop="quantity" label="数量" max-width="120px" align="center">
                    <template #default="scope">
                      <div class="py-1">
                        <ErpInputNumber
                          v-if="!scope.row.isDetail"
                          :key="`quantity-${scope.row.id}`"
                          v-model="scope.row.quantity"
                          type="default"
                          size="small"
                          :min="0"
                          :step="1"
                          @update:modelValue="debouncedQuantityChange($event, scope.row)" />
                      </div>
                    </template>
                  </el-table-column>

                  <!-- 单价列 -->
                  <el-table-column prop="currentPrice" label="单价" max-width="100px" align="right">
                    <template #default="scope">
                      <span v-if="!scope.row.isDetail" class="price">¥ {{ (scope.row.currentPrice / 100).toFixed(2) }}</span>
                    </template>
                  </el-table-column>

                  <!-- 金额列 -->
                  <el-table-column label="金额" max-width="120px" align="right">
                    <template #default="scope">
                      <span v-if="!scope.row.isDetail" class="amount text-red-500">
                        ¥ {{ ((scope.row.currentPrice * scope.row.quantity) / 100).toFixed(2) }}
                      </span>
                    </template>
                  </el-table-column>

                  <!-- 购物车空数据提示 -->
                  <template #empty>
                    <div class="py-10 text-center">
                      <div class="text-gray-400 mb-2 text-lg">购物车还是空的</div>
                      <div class="text-gray-400 text-sm">请从左侧商品列表选择商品添加到购物车</div>
                    </div>
                  </template>
                </el-table>
              </div>

              <!-- 小计 -->
              <div v-if="vm.state.cartItems.length > 0" class="p-3 text-right border-t border-gray-100">
                <span class="text-gray-500"
                  >小计: ¥
                  <span class="text-lg text-black font-bold">{{ formatYuanWithSymbol(vm.computed.totalAmountInFen.value).replace('￥', '') }}</span></span
                >
                <span class="text-sm text-gray-500 ml-2">(共{{ vm.computed.totalItems.value }}件)</span>
              </div>
            </el-card>

            <!-- 功能按钮区 -->
            <!-- <div class="grid grid-cols-5 gap-2 function-buttons">
              <el-button class="!h-10 text-sm">商品打折</el-button>
              <el-button class="!h-10 text-sm">存单</el-button>
              <el-button class="!h-10 text-sm">取单</el-button>
              <el-button class="!h-10 text-sm">核销</el-button>
              <el-button class="!h-10 text-sm">备注</el-button>
            </div> -->
          </div>
        </div>
      </template>

      <!-- 添加底部栏插槽 -->
      <template #right-footer>
        <div class="flex items-center justify-between px-5 border-t h-full border-l border-gray-100">
          <div class="flex flex-col">
            <div class="text-[16px] font-semibold text-[#000]">总金额:</div>
            <div class="flex items-baseline">
              <span class="text-[36px] font-bold leading-none">{{ Math.floor(vm.computed.totalAmountInFen.value / 100) }}</span>
              <span class="text-[16px] text-[rgba(0,0,0,0.40)]">.00</span>
            </div>
          </div>
          <el-button class="btn-black" @click="handleConfirm" :loading="isConfirmProcessing">
            {{ mode === 'gift' ? '赠送' : '确认' }}
          </el-button>
        </div>
      </template>
    </LeftRightLayout>

    <ProductionOrderPrinter
      v-if="vm.state.showPrinter"
      v-model:visible="vm.state.showPrinter"
      :data="vm.state.printData"
      @print-success="vm.actions.handlePrintSuccess"
      @print-failed="vm.actions.handlePrintFailed" />

    <!-- 添加套餐选择弹窗 -->
    <package-dialog
      v-if="vm.state.packageDialogVisible"
      v-model="vm.state.currentPackage"
      v-model:visible="vm.state.packageDialogVisible"
      @confirm="handlePackageConfirm"
      @cancel="vm.actions.closePackageDialog" />
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, onBeforeUnmount, defineComponent, computed } from 'vue';
import { Search, Loading, Minus, Plus, Close, Operation } from '@element-plus/icons-vue';
import ProductSelector from '@/modules/production/components/ProductSelector.vue';
import RoomSelectorDialog from '@/modules/room/components/RoomSelectorDialog.vue';
import ProductionOrderPrinter from '@/modules/production/components/ProductionOrderPrinter.vue';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import { useAddProductOrder } from '@/modules/production/views/AddProductOrder/presenter';
import PackageDialog from '@/modules/production/components/packageDialog/index.vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import LeftRightLayout from '@/components/Dialog/layout';
import IconButton from '@/components/icons/IconButton.vue';
import { IconPlus, IconMinus } from '@/components/icons';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import PackageDetailItem from './components/PackageDetailItem.vue';

import { ICartItem } from '@/modules/production/views/OpenTableProduct/viewmodel';
import { ProductApi } from '@/modules/production/api/product';
// 使用vue-hooks-plus库中的useRequest
import { useRequest } from 'vue-hooks-plus';
import { debounce, uniqBy, groupBy, sumBy, isEmpty, isArray, isString, isNumber } from 'lodash-es';
import type { OrderProductVO } from '@/types/projectobj';
import { ElMessage } from 'element-plus';
import { ExtendedCartItem } from './types';
import { cleanupCartItems, validateCartItem, shouldCreateDetailRow, createDetailRow, parsePackageProducts } from './utils';
import type { SessionOperationVOVoOrderVO } from '@/api/autoGenerated';

// 记录组件实例ID和打开次数
const instanceId = 'add_product_dialog';

// 工具方法：将页面商品转换为购物车项
const convertToCartItem = (product: any): ICartItem => {
  // 🔥 重要修复：标记是否已经在套餐处理阶段处理了可选组
  let packageHasProcessedOptionalGroups = false;

  // 验证基础字段类型
  const id = product.id;
  const name = product.productName || product.name;
  const currentPrice = product.currentPrice || product.price || 0;
  const quantity = product.quantity;
  const isPackage = product.isPackage;

  // 处理套餐明细，格式化为更符合设计图的格式
  let packageDetail = null;
  if (isPackage && product.packageProducts) {
    try {
      let packageProductsData;

      // 明确的类型检查
      if (isString(product.packageProducts)) {
        const parseResult = parsePackageProducts(product.packageProducts, product.productVOList || []);
        if (parseResult.success) {
          packageProductsData = parseResult.data;
        } else {
          packageProductsData = [];
        }
      } else if (isArray(product.packageProducts)) {
        packageProductsData = product.packageProducts;
      } else {
        packageProductsData = [];
      }

      if (isArray(packageProductsData) && !isEmpty(packageProductsData)) {
        // 🔥 重要修复：为套餐商品创建结构化的selectedProducts数据
        const productVOList = product.productVOList || [];
        const selectedProducts = [];

        // 首先添加套餐固定商品
        packageProductsData.forEach((p: any, index: number) => {
          const productName = p.name;
          const productCount = p.quantity || p.count || 1;

          // 构建结构化商品数据（与可选组保持一致）
          const productItem = {
            id: p.id,
            name: productName,
            count: productCount,
            currentPrice: p.currentPrice || 0
          };

          selectedProducts.push(productItem);
        });

        // 🔥 重要修复：如果同时有可选组，需要合并可选组的选中商品
        if (product.optionalGroups) {
          try {
            let optionalGroupsData;
            if (isString(product.optionalGroups)) {
              optionalGroupsData = JSON.parse(product.optionalGroups);
            } else if (isArray(product.optionalGroups)) {
              optionalGroupsData = product.optionalGroups;
            }

            if (isArray(optionalGroupsData)) {
              optionalGroupsData.forEach((group: any) => {
                if (group.products && isArray(group.products)) {
                  group.products.forEach((optProduct: any) => {
                    let isSelected = false;
                    let finalCount = 0;

                    if (group.optionType === 'by_plan') {
                      const selectedCount = optProduct.selected_count || 0;
                      isSelected = selectedCount > 0;
                      finalCount = (optProduct.count || 1) * selectedCount;
                    } else {
                      const selectedCount = optProduct.selected_count || 0;
                      isSelected = selectedCount > 0;
                      finalCount = selectedCount;
                    }

                    if (isSelected && finalCount > 0) {
                      const productInfo = productVOList.find((vo: any) => vo.id === optProduct.id);
                      const productName = productInfo?.name || optProduct.name || `可选商品(${optProduct.id})`;

                      // 🔥 重要修复：检查是否已存在相同商品，避免重复
                      const existingProductIndex = selectedProducts.findIndex(p => p.id === optProduct.id);

                      if (existingProductIndex >= 0) {
                        // 如果商品已存在，累加数量而不是重复添加
                        selectedProducts[existingProductIndex].count += finalCount;
                      } else {
                        // 如果商品不存在，添加新商品
                        const productItem = {
                          id: optProduct.id,
                          name: productName,
                          count: finalCount,
                          currentPrice: optProduct.currentPrice || 0
                        };

                        selectedProducts.push(productItem);
                      }
                    }
                  });
                }
              });
            }
          } catch (error) {
            // 处理错误但不输出日志
          }

          // 🔥 重要修复：标记套餐已经处理了可选组
          packageHasProcessedOptionalGroups = true;
        }

        // 🔥 重要修复：重新生成detailItems，确保数量正确
        const detailItems = selectedProducts.map(product => `${product.name}×${product.count}`);

        // 🔥 重要修复：创建与可选组一致的packageDetail结构
        packageDetail = {
          packageProducts: product.packageProducts, // 保留原始套餐商品数据
          optionalGroups: product.optionalGroups, // 保留可选组数据
          productVOList: productVOList, // 保留商品信息映射
          selectedProducts: selectedProducts, // 新增：结构化的套餐商品列表（包含套餐+可选组，已去重）
          detailString: detailItems.join('，') // 兼容原有的字符串格式，使用去重后的数据
        };
      } else {
        packageDetail = {
          packageProducts: product.packageProducts || '',
          productVOList: product.productVOList || [],
          selectedProducts: [],
          detailString: '套餐暂无商品'
        };
      }
    } catch (error) {
      packageDetail = {
        packageProducts: product.packageProducts || '',
        productVOList: product.productVOList || [],
        selectedProducts: [],
        detailString: '套餐明细解析失败'
      };
    }
  } else if (product.packageDetail) {
    packageDetail = product.packageDetail;
  }

  // 处理可选组商品 - 修复逻辑：根据实际可选组数据判断
  let isOptionalGroup = product.isOptionalGroup;

  // 如果没有isOptionalGroup字段，但有可选组数据，则认为是可选组商品
  if (!isOptionalGroup && product.optionalGroups) {
    // 检查是否有有效的可选组数据
    try {
      let optionalGroupsData;
      if (typeof product.optionalGroups === 'string') {
        optionalGroupsData = JSON.parse(product.optionalGroups);
      } else if (Array.isArray(product.optionalGroups)) {
        optionalGroupsData = product.optionalGroups;
      }

      // 如果解析出的可选组数据是非空数组，则认为是可选组商品
      if (Array.isArray(optionalGroupsData) && optionalGroupsData.length > 0) {
        isOptionalGroup = true;
      }
    } catch (error) {
      // 处理错误但不输出日志
    }
  }

  let optionalProducts = null;
  let optionType = null;
  let optionCount = null;
  let selectedQuantity = null;
  let isFree = product.isFree;
  let details = '';

  if (isOptionalGroup) {
    // 🔥 重要修复：如果商品既是套餐又有可选组，且已经在套餐处理阶段合并了可选组，则跳过单独处理
    if (
      isPackage &&
      packageHasProcessedOptionalGroups &&
      packageDetail &&
      (packageDetail as any).selectedProducts &&
      (packageDetail as any).selectedProducts.length > 0
    ) {
      // 直接使用套餐明细中的数据，不重复处理
      details = packageDetail.detailString || '';
    } else {
      // 只有纯可选组商品才进行单独处理
      // 首先尝试从optionalProducts获取数据
      let sourceData = product.optionalProducts;
      let sourceField = 'optionalProducts';

      // 如果optionalProducts不存在或为空，尝试从optionalGroups获取
      if (!sourceData && product.optionalGroups) {
        sourceData = product.optionalGroups;
        sourceField = 'optionalGroups';
      }

      try {
        // 明确的类型检查
        if (typeof sourceData === 'string') {
          try {
            const parsedData = JSON.parse(sourceData);

            // 如果是来自optionalGroups，需要提取商品列表
            if (sourceField === 'optionalGroups' && Array.isArray(parsedData) && parsedData.length > 0) {
              const firstGroup = parsedData[0];

              // 提取可选组的商品列表
              if (firstGroup.products && Array.isArray(firstGroup.products)) {
                optionalProducts = firstGroup.products;
                optionType = firstGroup.optionType || firstGroup.type || 'ByCount';
                optionCount = firstGroup.optionCount || firstGroup.count || 1;
              } else {
                optionalProducts = [];
              }
            } else {
              // 直接来自optionalProducts
              optionalProducts = parsedData;
            }
          } catch (parseError) {
            optionalProducts = [];
          }
        } else if (Array.isArray(sourceData)) {
          // 如果是来自optionalGroups的数组
          if (sourceField === 'optionalGroups' && sourceData.length > 0) {
            const firstGroup = sourceData[0];

            if (firstGroup.products && Array.isArray(firstGroup.products)) {
              optionalProducts = firstGroup.products;
              optionType = firstGroup.optionType || firstGroup.type || 'ByCount';
              optionCount = firstGroup.optionCount || firstGroup.count || 1;
            } else {
              optionalProducts = [];
            }
          } else {
            // 直接来自optionalProducts的数组
            optionalProducts = sourceData;
          }
        } else {
          optionalProducts = [];
        }

        // 验证和设置其他可选组字段（如果还没有设置）
        if (!optionType) {
          optionType = product.optionType || 'ByCount';
        }
        if (!optionCount) {
          optionCount = product.optionCount || 1;
        }
        selectedQuantity = product.selectedQuantity || 0;

        // 准备商品名称映射数据
        const productVOList = product.productVOList || [];

        // 生成详情文本和结构化明细数据
        if (Array.isArray(optionalProducts) && optionalProducts.length > 0) {
          // 筛选已选择的商品并构建结构化明细数据
          const selectedProducts = [];
          const detailItems = [];

          optionalProducts.forEach((p: any, index: number) => {
            // 对于by_plan类型，应该检查selected_count而不是count字段
            // 对于by_count类型，检查quantity或其他数量字段
            let quantity = 0;
            let isSelected = false;

            if (optionType === 'by_plan') {
              // by_plan类型：检查selected_count字段，selected_count > 0表示该方案被选中
              const selectedCount = p.selected_count || 0;
              isSelected = selectedCount > 0;
              // by_plan类型的最终数量 = count * selected_count (通常selected_count为1)
              quantity = (p.count || 1) * selectedCount;
            } else {
              // by_count类型：只检查selected_count字段，不使用count字段
              // 🔥 重要修复：只有selected_count > 0的商品才算被选中
              const selectedCount = p.selected_count || 0;
              isSelected = selectedCount > 0;
              quantity = selectedCount; // 直接使用selected_count作为最终数量
            }

            if (isSelected && quantity > 0) {
              // 从productVOList中查找商品信息获取名称
              const productInfo = productVOList.find((vo: any) => vo.id === p.id);
              const productName = productInfo?.name || p.name || p.productName || p.title || '未知商品';

              // 构建结构化商品数据
              const productItem = {
                id: p.id,
                name: productName,
                count: quantity, // 使用计算后的最终数量
                currentPrice: p.currentPrice || 0
              };

              selectedProducts.push(productItem);
              detailItems.push(`${productName}×${quantity}`);
            }
          });

          if (selectedProducts.length > 0) {
            details = detailItems.join('，');

            // 🔥 重要修复：如果已经有套餐明细，需要合并而不是覆盖
            if (packageDetail && packageDetail.selectedProducts && packageDetail.selectedProducts.length > 0) {
              // 🔥 使用lodash进行智能合并：相同ID的商品数量累加，不同ID的商品直接添加
              const existingProducts = packageDetail.selectedProducts;
              const newProducts = selectedProducts;

              // 创建合并后的商品列表
              const mergedProducts = [...existingProducts];

              newProducts.forEach(newProduct => {
                const existingIndex = mergedProducts.findIndex(existing => existing.id === newProduct.id);
                if (existingIndex >= 0) {
                  // 相同商品，累加数量
                  mergedProducts[existingIndex].count += newProduct.count;
                } else {
                  // 新商品，直接添加
                  mergedProducts.push(newProduct);
                }
              });

              // 重新生成明细字符串
              const mergedDetailString = mergedProducts.map(product => `${product.name}×${product.count}`).join('，');

              packageDetail = {
                packageProducts: packageDetail.packageProducts, // 保留套餐商品数据
                optionalGroups: sourceData, // 更新可选组数据
                productVOList: productVOList, // 保留商品信息映射
                selectedProducts: mergedProducts, // 合并去重后的商品列表
                detailString: mergedDetailString // 重新生成的明细字符串
              };
            } else {
              // 创建纯可选组的包详细信息
              packageDetail = {
                optionalGroups: sourceData, // 保留原始可选组数据
                productVOList: productVOList, // 保留商品信息映射
                selectedProducts: selectedProducts, // 新增：结构化的已选商品列表
                detailString: details // 兼容原有的字符串格式
              };
            }
          } else {
            details = '未选择任何商品';
            // 如果没有选择任何可选组商品，保持原有的套餐明细不变
            if (!packageDetail) {
              packageDetail = {
                optionalGroups: sourceData,
                productVOList: productVOList,
                selectedProducts: [],
                detailString: details
              };
            }
          }
        } else {
          details = '可选组商品列表为空';
          // 如果可选组商品列表为空，保持原有的套餐明细不变
          if (!packageDetail) {
            packageDetail = {
              optionalGroups: sourceData || '',
              productVOList: productVOList,
              selectedProducts: [],
              detailString: details
            };
          }
        }
      } catch (error) {
        optionalProducts = [];
        details = '可选组明细解析失败';
      }
    }
  }

  // 创建购物车项，确保所有字段类型正确
  const cartItem: ExtendedCartItem = {
    id: isString(id) ? id : '',
    name: isString(name) ? name : '',
    currentPrice: isNumber(currentPrice) ? currentPrice : 0,
    quantity: isNumber(quantity) && quantity > 0 ? quantity : 1,
    flavors: isString(product.flavors) ? product.flavors : '',
    unit: isString(product.unit) ? product.unit : '',
    isPackage: typeof isPackage === 'boolean' ? isPackage : false,
    packageDetail: packageDetail,
    isOptionalGroup: typeof isOptionalGroup === 'boolean' ? isOptionalGroup : false,
    isFree: typeof isFree === 'boolean' ? isFree : false
  };

  // 仅当是可选组时添加这些属性
  if (cartItem.isOptionalGroup) {
    cartItem.optionalProducts = isArray(optionalProducts) ? optionalProducts : [];
    cartItem.optionType = isString(optionType) ? optionType : 'ByCount';
    cartItem.optionCount = isNumber(optionCount) ? optionCount : 1;
    cartItem.selectedQuantity = isNumber(selectedQuantity) ? selectedQuantity : 0;
    cartItem.details = isString(details) ? details : '';
  }

  return cartItem;
};

// 定义商品和分类的类型
interface ProductCategory {
  id: string | null;
  name: string;
  count: string | number;
  isActive: boolean;
  type: string;
  isPackage: boolean;
}

interface Product {
  id: string;
  name: string;
  stock: number;
  tag: string;
  currentPrice: number;
  unit: string;
  isPackage: boolean;
  isSoldOut: boolean;
  packageProducts?: any[];
  optionalGroups?: any[];
  productVOList?: any[];
  hasOptionalGroups?: boolean;
}

interface ProductsResult {
  products: Product[];
  isLoadingMore: boolean;
}

// 商品选择器相关状态
const searchKeyword = ref('');
const categories = ref<ProductCategory[]>([]);
const products = ref<Product[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const selectedCategoryId = ref<string | null>(null);
const currentPage = ref(1);
const pageSize = ref(200);
const productTableRef = ref<HTMLElement | null>(null);
const loadingMore = ref(false);
const hasMore = ref(true);

// 设置表格行的类名
const tableRowClassName = ({ row }: { row: Product }) => {
  if (row.isSoldOut) {
    return 'sold-out-row';
  }
  return 'normal-row';
};

// 加载更多数据
const loadMore = async () => {
  if (loadingMore.value || !hasMore.value) return;
  loadingMore.value = true;
  currentPage.value++;
  await fetchProducts(true);
};

// 类型定义
const CATEGORY_TYPE = {
  ALL: 'all',
  PACKAGE: 'package',
  PRODUCT: 'product'
};

// 使用useRequest获取商品分类
const {
  loading: categoriesLoading,
  error: categoriesError,
  run: fetchCategories,
  cancel: cancelFetchCategories
} = useRequest(
  async () => {
    const response = await ProductApi.listProductTypes({});

    if (response.code === 0) {
      // 商品分类
      const productTypes = response.data.productTypeVOs.map((type: any) => ({
        id: type.id,
        name: type.name,
        count: type.count,
        isActive: false,
        type: CATEGORY_TYPE.PRODUCT,
        isPackage: false
      }));

      // 套餐分类
      const packageTypes = response.data.productPackageTypeVOs.map((type: any) => ({
        id: type.id,
        name: type.name,
        count: type.count,
        isActive: false,
        type: CATEGORY_TYPE.PACKAGE,
        isPackage: true
      }));

      // 所有分类
      const allCategories: ProductCategory[] = [
        {
          id: null,
          name: '全部分类',
          count: '',
          isActive: true,
          type: CATEGORY_TYPE.ALL,
          isPackage: false
        },
        ...packageTypes,
        ...productTypes
      ];

      categories.value = allCategories;
      selectedCategoryId.value = null;
      return allCategories;
    } else {
      throw new Error(response.message || '获取商品分类失败');
    }
  },
  {
    manual: true,
    onBefore: () => {
      // 如果已经有数据，则不需要再次请求
      if (categories.value.length > 0) {
        return false; // 返回false会阻止请求发出
      }
    },
    onError: (err: Error) => {
      error.value = '获取分类失败: ' + err.message;
    },
    onFinally: () => {
      // 请求完成
    }
  }
);

// 使用useRequest获取商品列表
const {
  loading: productsLoading,
  error: productsError,
  run: runFetchProducts,
  refresh: refreshProducts,
  cancel: cancelFetchProducts
} = useRequest<ProductsResult, [boolean, string | null, number, number]>(
  async (isLoadingMore = false, categoryId: string | null = selectedCategoryId.value, page = currentPage.value, size = pageSize.value) => {
    // 找到选中的分类
    const selectedCategory = categories.value.find(c => c.id === categoryId) ?? null;

    // 创建API参数对象
    const apiParams: any = {
      pageNum: page,
      pageSize: size
    };
    if (selectedCategory?.isPackage) {
      apiParams.productPackageTypeId = selectedCategory.id;
    } else {
      apiParams.category = selectedCategory?.id || '';
    }

    // 添加搜索关键字参数
    if (searchKeyword.value.trim()) {
      apiParams.name = searchKeyword.value.trim();
    }

    // 使用实际的 ProductApi
    const response = await ProductApi.queryDetailByType(apiParams);

    if (response.code === 0) {
      let newProducts: Product[] = [];

      // 处理套餐数据
      const packageList = response.data.productPackageVOs || [];
      const packageProducts = packageList.map((item: any) => ({
        id: item.id,
        name: item.name,
        stock: -1,
        tag: item.isPromotion ? '促销' : '',
        currentPrice: item.currentPrice,
        unit: '套餐',
        isPackage: true,
        isSoldOut: item.isSoldOut,
        packageProducts: item.packageProducts,
        optionalGroups: item.optionalGroups,
        productVOList: item.productVOList,
        // optionalGroups 是json数组字符串
        hasOptionalGroups: item.optionalGroups && JSON.parse(item.optionalGroups).length > 0
      }));

      // 处理普通商品数据
      const productList = response.data.productVOs || [];
      const normalProducts = productList.map((item: any) => ({
        id: item.id,
        name: item.name,
        stock: item.calculateInventory ? item.lowStockThreshold : -1,
        tag: item.isPromotion ? '促销' : '',
        currentPrice: item.currentPrice,
        unit: item.unit,
        isPackage: false,
        isSoldOut: item.isSoldOut
      }));

      // 根据选中的分类类型决定显示哪些产品
      if (selectedCategory && selectedCategory.id !== null) {
        newProducts = selectedCategory.isPackage ? packageProducts : normalProducts;
      } else {
        newProducts = [...packageProducts, ...normalProducts];
      }

      hasMore.value = newProducts.length === size;

      // 返回处理结果和加载状态
      return {
        products: newProducts,
        isLoadingMore
      };
    } else {
      throw new Error(response.message || '获取商品列表失败');
    }
  },
  {
    manual: true,
    onBefore: params => {
      const [isLoadingMore] = params;
      // 设置加载状态
      if (!isLoadingMore) {
        loading.value = true;
      }
      loadingMore.value = !!isLoadingMore;
    },
    onSuccess: (result: ProductsResult) => {
      const { products: newProducts, isLoadingMore } = result;

      if (isLoadingMore) {
        products.value = [...products.value, ...newProducts];
      } else {
        products.value = newProducts;
      }
    },
    onError: (err: Error) => {
      error.value = '获取商品列表失败: ' + err.message;
    },
    onFinally: () => {
      loading.value = false;
      loadingMore.value = false;
    }
  }
);

// 立即取消所有未完成的请求
const cancelAllRequests = () => {
  cancelFetchCategories();
  cancelFetchProducts();
};

// 封装fetchProducts函数，保持与原有代码兼容，取消之前的请求
const fetchProducts = (isLoadingMore = false) => {
  // 防止重复调用
  if (loading.value && !isLoadingMore) {
    return Promise.resolve();
  }

  // 不管是否加载更多，先取消之前的请求
  cancelFetchProducts();
  return runFetchProducts(isLoadingMore, selectedCategoryId.value, currentPage.value, pageSize.value);
};

// 切换分类
const changeCategory = (categoryId: string | null) => {
  // 如果选择的是相同分类，不做处理
  if (categoryId === selectedCategoryId.value) {
    return;
  }

  selectedCategoryId.value = categoryId;
  categories.value.forEach(category => {
    category.isActive = category.id === categoryId;
  });
  currentPage.value = 1;
  hasMore.value = true;
  fetchProducts();
};

// 滚动监听处理
const handleTableScroll = (e: any) => {
  if (!productTableRef.value) return;

  // 获取表格元素，使用any类型来访问$el属性
  const table = productTableRef.value as any;
  const scrollElement = table.$el.querySelector('.el-scrollbar__wrap');

  if (scrollElement) {
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    // 判断是否滚动到底部 (距离底部小于50px)
    if (scrollHeight - scrollTop - clientHeight < 50) {
      if (!loadingMore.value && hasMore.value) {
        loadMore();
      }
    }
  }
};

// 处理搜索的实际逻辑
const doSearch = () => {
  // 重置页码并重新加载数据
  currentPage.value = 1;
  hasMore.value = true;

  // 如果是搜索操作，可以重置分类选择
  if (searchKeyword.value.trim()) {
    // 可以选择重置分类为全部，也可以保持当前分类
    // categories.value.forEach(category => {
    //   category.isActive = category.id === null
    // })
    // selectedCategoryId.value = null
  }

  fetchProducts();
};

// 使用lodash的debounce创建防抖函数，延迟300ms
const debouncedSearch = debounce(doSearch, 300);

// 处理搜索 - 使用lodash debounce
const handleSearch = () => {
  debouncedSearch();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  currentPage.value = 1;
  hasMore.value = true;
  fetchProducts();
};

// 处理添加到购物车
const handleAddToCart = (product: any) => {
  console.log('[addCart] handleAddToCart product:', product);
  vm.actions.addToCart(product);

  // 查找购物车中是否有该商品
  const cartItem = vm.state.cartItems.find(item => item.id === product.id);

  // 如果是可选组商品，确保更新其明细
  if (cartItem && (cartItem as ExtendedCartItem).isOptionalGroup) {
    updateOptionalGroupDetails(cartItem as ExtendedCartItem);
  }
};

// 扩展AddProductOrder的state类型
interface ExtendedAddProductOrderState {
  selectedRoom: any;
  selectedArea: string;
  currentSessionId: string;
  cartItems: ICartItem[];
  selectedSeller: string;
  showPrinter: boolean;
  printData: any;
  isChargeToRoom: boolean;
  showRoomSelector: boolean;
  packageDialogVisible: boolean;
  currentPackage: any;
  orderType: string;
}

// 扩展AddProductOrder的actions类型
interface ExtendedAddProductOrderActions {
  addToCart: (product: any) => void;
  increaseQuantity: (item: any) => void;
  decreaseQuantity: (item: any) => void;
  clearCart: () => void;
  handleRoomSelect: (room: any) => void;
  clearRoomSelection: () => void;
  handleConfirm: () => Promise<void>;
  handlePrintSuccess: () => void;
  handlePrintFailed: () => void;
  handlePackageConfirm: (packageData: any) => void;
  closePackageDialog: () => void;
  addNote?: () => void;
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roomId: {
    type: String,
    default: ''
  },
  roomName: {
    type: String,
    default: ''
  },
  areaId: {
    type: String,
    default: ''
  },
  areaName: {
    type: String,
    default: ''
  },
  sessionId: {
    type: String,
    default: ''
  },
  outOrderProducts: {
    type: Array,
    default: () => []
  },
  // 新增赠送模式属性
  mode: {
    type: String,
    default: 'normal', // normal 普通点单模式, gift 赠送商品模式
    validator: (value: string) => ['normal', 'gift'].includes(value)
  },
  operator: {
    type: String,
    default: ''
  },
  venueId: {
    type: String,
    default: ''
  },
  memberId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'gift-success']);

const visible = ref(props.modelValue);

// 数据加载状态标志
const isDataInitialized = ref(false);
const isRoomInitialized = ref(false);
const isConfirmProcessing = ref(false);
const isLoading = ref(false);

// 使用解构方式获取presenter
const addProductOrder = useAddProductOrder();
const vm = {
  state: addProductOrder.state as unknown as ExtendedAddProductOrderState,
  computed: addProductOrder.computed,
  actions: addProductOrder.actions as unknown as ExtendedAddProductOrderActions
};

// 获取当前模式
const mode = computed(() => props.mode);

// 初始化房间和会话信息
const initializeRoomAndSession = () => {
  // 如果已经初始化过，则不再重复初始化
  if (isRoomInitialized.value) {
    return;
  }

  // 设置房间信息
  if (props.roomId && props.roomName) {
    vm.state.selectedRoom = {
      id: props.roomId,
      name: props.roomName,
      areaVO:
        props.areaId && props.areaName
          ? {
              id: props.areaId,
              name: props.areaName
            }
          : undefined
    };

    if (props.areaId) {
      vm.state.selectedArea = props.areaId;
    }
  }

  // 设置会话ID
  if (props.sessionId) {
    vm.state.currentSessionId = props.sessionId;
  }

  // 设置已有商品（数据回写）
  if (props.outOrderProducts && props.outOrderProducts.length > 0) {
    // 清空现有商品，避免重复添加
    vm.state.cartItems = [];
    // 使用转换方法添加传入的商品到购物车
    vm.state.cartItems = props.outOrderProducts.map((product: any) => convertToCartItem(product));
  }

  isRoomInitialized.value = true;
};

// 加载商品数据
const loadProductData = () => {
  if (isDataInitialized.value) {
    return;
  }

  console.log(`[${instanceId}] 开始加载商品数据`);
  // 加载商品分类
  if (categories.value.length === 0) {
    fetchCategories();
  }

  // 加载商品列表
  if (products.value.length === 0) {
    fetchProducts();
  }

  isDataInitialized.value = true;
};

// 使用一个独立的状态来追踪是否已初始化
let hasInitialized = false;

// 监听props变化
watch(
  () => props.modelValue,
  (val, oldVal) => {
    if (val === oldVal) {
      return;
    }

    visible.value = val;

    if (val) {
      // 当弹窗打开时，初始化数据
      if (!hasInitialized) {
        hasInitialized = true;
        nextTick(() => {
          if (!isRoomInitialized.value) {
            initializeRoomAndSession();
          }
          if (!isDataInitialized.value) {
            loadProductData();
          }
        });
      } else {
      }
    } else {
      // 弹窗关闭时，重置处理状态
      isConfirmProcessing.value = false;
      // 取消所有未完成的请求
      cancelAllRequests();
    }
  },
  { immediate: true }
);

// 监听visible变化，实现双向绑定，使用函数形式避免副作用
watch(visible, (val, oldVal) => {
  if (val === oldVal) return;
  emit('update:modelValue', val);
});

// 组件挂载时初始化
onMounted(() => {
  if (props.modelValue && !isRoomInitialized.value && !hasInitialized) {
    hasInitialized = true;
    nextTick(() => {
      initializeRoomAndSession();
      loadProductData();
      initializeOrderType();
    });
  }
});

// 组件卸载前取消所有请求
onBeforeUnmount(() => {
  cancelAllRequests();
});

// 处理数量变更
const handleQuantityChange = (newValue: number, item: ICartItem) => {
  // 防止无效输入
  if (!isNumber(newValue) || isNaN(newValue)) {
    return;
  }

  // 当数量为0时，从购物车中移除该商品
  if (newValue === 0) {
    // 直接从购物车数组中移除该商品
    const index = vm.state.cartItems.findIndex(cartItem => cartItem.id === item.id);
    if (index !== -1) {
      vm.state.cartItems.splice(index, 1);
    }
    return;
  }

  // 当数量小于0时，设置为0并移除
  if (newValue < 0) {
    // 先设置为0，然后移除
    const cartItem = vm.state.cartItems.find(cartItem => cartItem.id === item.id);
    if (cartItem) {
      cartItem.quantity = 0;
      // 延迟移除，让用户看到数量变为0的过程
      setTimeout(() => {
        const index = vm.state.cartItems.findIndex(cartItem => cartItem.id === item.id);
        if (index !== -1) {
          vm.state.cartItems.splice(index, 1);
        }
      }, 100);
    }
    return;
  }

  // 确保newValue是正整数
  const validQuantity = Math.max(0, Math.floor(newValue));

  // 查找购物车中对应的商品并更新数量
  const cartItem = vm.state.cartItems.find(cartItem => cartItem.id === item.id);
  if (cartItem) {
    // 避免重复更新相同的数量
    if (cartItem.quantity === validQuantity) {
      return;
    }

    cartItem.quantity = validQuantity;

    // 如果是可选组商品，更新其details属性以重新生成明细文本
    if ((cartItem as ExtendedCartItem).isOptionalGroup) {
      updateOptionalGroupDetails(cartItem as ExtendedCartItem);
    }
  } else {
    // 商品未找到的情况
  }

  // 清理购物车中可能存在的数量为0的商品
  vm.state.cartItems = cleanupCartItems(vm.state.cartItems as ExtendedCartItem[]);
};

// 添加更新可选组明细的函数
const updateOptionalGroupDetails = (item: ExtendedCartItem) => {
  if (!item.isOptionalGroup || !item.optionalProducts) return;

  // 获取已选择的商品
  const selectedProducts = item.optionalProducts.filter(product => (product.quantity || 0) > 0);

  if (selectedProducts.length === 0) {
    item.details = '未选择任何商品';
    return;
  }

  // 生成明细文本
  const detailItems = selectedProducts.map(product => `${product.name}×${product.quantity}`);
  item.details = detailItems.join('，');

  // 更新已选数量
  item.selectedQuantity = selectedProducts.reduce((sum, product) => sum + (product.quantity || 0), 0);
};

// 购物车行类名
const cartItemRowClassName = ({ row }: { row: ExtendedCartItem }) => {
  if (row.isDetail) {
    return 'is-detail-row';
  }
  if (row.isPackage) {
    return 'is-package';
  }
  return '';
};

// 获取套餐明细显示文本
const getPackageDetailsText = (parentId: string): string => {
  const parent = findParentItem(parentId);
  return parent?.packageDetail?.detailString || '';
};

// 处理套餐确认
const handlePackageConfirm = (packageData: any) => {
  // 转换为购物车项
  const cartItem = convertToCartItem(packageData);

  // 检查是否为编辑模式
  if (vm.state.currentPackage?.isEditing) {
    const originalItem = vm.state.cartItems.find(
      item => item.isPackage && item.id === cartItem.id && vm.state.currentPackage.packageUniqueId === item.packageUniqueId
    );

    if (originalItem) {
      cartItem.quantity = originalItem.quantity;
      // 删除原项
      vm.state.cartItems = vm.state.cartItems.filter(item => item !== originalItem);
    }
  }

  // 添加到购物车
  vm.state.cartItems.push(cartItem);

  // 关闭套餐对话框
  vm.state.packageDialogVisible = false;
  vm.state.currentPackage = null;
};

// 确认按钮处理 - 添加防抖处理
const handleConfirm = async () => {
  // 防止重复点击
  if (isConfirmProcessing.value) {
    return;
  }

  try {
    isConfirmProcessing.value = true;

    // 确保购物车有商品
    if (vm.state.cartItems.length === 0) {
      ElMessage.warning('请先添加商品');
      isConfirmProcessing.value = false;
      return;
    }

    // 确保已选择房间
    if (!vm.state.selectedRoom) {
      ElMessage.warning('请先选择送达包厢');
      isConfirmProcessing.value = false;
      return;
    }

    // 根据当前模式选择不同的处理逻辑
    if (mode.value === 'gift') {
      // 赠送商品模式
      await handleGiftProducts();
    } else {
      // 普通点单模式 - 保持原有逻辑
      emit('confirm', vm.state.cartItems);

      // 关闭弹窗
      visible.value = false;
    }
  } catch (error) {
    ElMessage.error('操作失败，请稍后重试');
  } finally {
    isConfirmProcessing.value = false;
  }
};

// 处理赠送商品逻辑
const handleGiftProducts = async () => {
  try {
    // 验证必要参数
    if (!props.sessionId) {
      throw new Error('缺少场次ID');
    }
    if (!props.roomId) {
      throw new Error('缺少房间ID');
    }

    // 计算总金额
    const totalAmount = vm.computed.totalAmountInFen.value;

    // 构建订单商品列表
    const orderProductVOs = convertCartItemsToOrderProducts();

    // 构建赠送请求
    const giftParams = {
      memberId: props.memberId || '',
      orderProductVOs,
      originalAmount: totalAmount,
      payAmount: 0, // 赠送商品支付总金额为0
      roomId: props.roomId,
      sessionId: props.sessionId,
      operator: props.operator
    };

    // 调用赠送API
    // @ts-ignore
    const response = await ProductApi.giftProduct(giftParams);

    if (response.code === 0) {
      const operationVO = response.data as SessionOperationVOVoOrderVO;
      console.log(`[${instanceId}] 赠送商品成功:`, operationVO.data);
      ElMessage.success('赠送商品成功');

      // 打印出品单和结账单
      

      // 发送赠送成功事件
      emit('gift-success', operationVO.data);

      // 关闭弹窗
      visible.value = false;
    } else {
      throw new Error(response.message || '赠送商品失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '赠送商品失败，请稍后重试');
    throw error;
  }
};

// 取消按钮处理
const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

// 初始化订单类型
const initializeOrderType = () => {
  // 根据当前场景设置订单类型
  if (vm.state.selectedRoom) {
    vm.state.orderType = 'room'; // 默认为包厢点单
  }
};

// 处理对象跨行
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  // 如果是详情行，则合并所有列
  if (row.isDetail) {
    if (columnIndex === 0) {
      return {
        colspan: 4, // 合并所有4列（商品名称、数量、单价、金额）
        rowspan: 1
      };
    } else {
      return {
        colspan: 0,
        rowspan: 0
      };
    }
  }
};

// 处理购物车数据
const processedCartItems = ref<ExtendedCartItem[]>([]);

// 监听购物车变化，处理明细行
watch(
  () => vm.state.cartItems,
  newCartItems => {
    // 清理和验证购物车数据
    const cleanedItems = cleanupCartItems(newCartItems);

    const processedItems: ExtendedCartItem[] = [];

    cleanedItems.forEach((item, index) => {
      // 验证商品数据
      const validationResult = validateCartItem(item);
      if (!validationResult.success) {
        return;
      }

      // 🔥 重要修复：对于套餐和可选组商品，确保packageDetail中的selectedProducts数据是最新的
      if ((item.isPackage || item.isOptionalGroup) && item.packageDetail?.selectedProducts) {
        // 更新packageDetail中的动态明细字符串，以反映当前数量
        const parentQuantity = item.quantity || 1;
        const detailItems = item.packageDetail.selectedProducts.map((product: any) => {
          const productCount = (product.count || 1) * parentQuantity;
          return `${product.name}×${productCount}`;
        });

        // 更新detailString以保持一致性
        item.packageDetail.detailString = detailItems.join('，');
      }

      // 添加主商品
      processedItems.push(item);

      // 检查是否需要创建明细行
      const shouldCreateDetail = shouldCreateDetailRow(item);

      if (shouldCreateDetail) {
        const detailRowResult = createDetailRow(item);
        if (detailRowResult.success && detailRowResult.data) {
          processedItems.push(detailRowResult.data);
        }
      }
    });

    processedCartItems.value = processedItems;
  },
  { deep: true, immediate: true }
);

// 根据父项ID查找对应的购物车项目
const findParentItem = (parentId: string): ICartItem | undefined => {
  return vm.state.cartItems.find(item => item.id === parentId);
};

// 创建防抖的数量变更处理函数
const debouncedQuantityChange = debounce((newValue: number, item: ICartItem) => {
  handleQuantityChange(newValue, item);
}, 100); // 100ms防抖

// 将购物车项转换为订单商品
const convertCartItemsToOrderProducts = (): OrderProductVO[] => {
  return vm.state.cartItems.map(item => ({
    id: '', // 新建时为空
    venueId: props.venueId,
    roomId: props.roomId,
    sessionId: props.sessionId,
    orderNo: '',
    productId: item.id,
    productName: item.name,
    flavors: item.flavors || '',
    unit: item.unit || '份',
    quantity: item.quantity,
    payPrice: 0, // 赠送商品支付价格为0
    originalPrice: item.currentPrice, // 使用当前价格作为原价
    payAmount: 0, // 赠送商品支付总金额为0
    originalAmount: item.currentPrice * item.quantity, // 原始总金额
    discountRate: 0, // 赠送相当于100%折扣
    reduceAmount: 0,
    freeAmount: item.currentPrice * item.quantity, // 全额免单
    mark: '',
    inPackageTag: '',
    src: '',
    ctime: 0,
    utime: 0,
    state: 0,
    version: 0
  }));
};
</script>

<style scoped>
:deep(.el-table .cell) {
  padding: 0 !important;
}

:deep(.cart-table .el-table__header tr th.el-table__cell) {
  background-color: #f3f3f3 !important;
}

/* 购物车卡片样式 */
:deep(.cart-card) {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

:deep(.cart-card .el-card__header) {
  padding: 16px !important;
  border-bottom: 1px solid #ebeef5 !important;
  flex-shrink: 0 !important;
}

:deep(.cart-card .el-card__body) {
  flex: 1 !important;
  padding: 0 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 表格样式 */
:deep(.cart-table) {
  flex: 1 !important;
}

:deep(.cart-table .el-table__inner-wrapper) {
  height: 100% !important;
}

:deep(.cart-table .el-table__body-wrapper) {
  overflow-y: auto !important;
}

:deep(.cart-table .el-table__header-wrapper) {
  flex-shrink: 0 !important;
}

:deep(.cart-table .el-table__empty-block) {
  height: 100% !important;
}

/* 确保小计区域固定在底部 */
:deep(.cart-card) .p-3 {
  flex-shrink: 0 !important;
  background: #fff !important;
  border-top: 1px solid #ebeef5 !important;
}

/* 修复商品表格布局 */
:deep(.product-table) {
  height: 100% !important;
}

:deep(.product-table .el-table__inner-wrapper) {
  height: 100% !important;
}

:deep(.product-table .el-table__body-wrapper) {
  overflow-y: auto !important;
  flex: 1 !important;
}

/* 修复左侧面板内容区域 */
:deep(.left-panel .panel-content) {
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}
</style>
