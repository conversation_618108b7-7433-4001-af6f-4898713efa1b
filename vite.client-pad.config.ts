import { resolve } from 'path';
import { defineConfig, loadEnv, Plugin } from 'vite';
import vue from '@vitejs/plugin-vue';
import { VueMcp } from 'vite-plugin-vue-mcp';
import Inspect from 'vite-plugin-inspect';
import legacy from '@vitejs/plugin-legacy';
import VueDevTools from 'vite-plugin-vue-devtools';
import { existsSync, readFileSync } from 'fs';
import { visualizer } from 'rollup-plugin-visualizer';
import type { Connect } from 'vite';

// 应用类型固定为client-pad
const appType = 'client-pad';
const appVersion = '1.0.0';
// 明确指定入口HTML文件路径
const entryHtmlPath = resolve(__dirname, 'client-pad.html');

// 创建日志插件
function createPadLogPlugin(): Plugin {
  return {
    name: 'vite:pad-log-plugin',
    configureServer(server) {
      // 在服务器启动时打印信息
      setTimeout(() => {
        console.log('');
        console.log('=========================');
        console.log('[Pad] 开发服务器已启动');
        console.log('[Pad] URL: http://localhost:5174/');
        console.log('=========================');
        console.log('');
      }, 1000);
    }
  };
}

// 创建强制客户端使用client-pad.html的插件
function createForceClientPadPlugin(): Plugin {
  let entryHtmlContent: string = '';

  return {
    name: 'vite:force-client-pad',
    configResolved() {
      try {
        // 读取client-pad.html内容
        entryHtmlContent = readFileSync(entryHtmlPath, 'utf-8');
        console.log(`[Pad] 已加载client-pad.html作为入口文件: ${entryHtmlPath.length}字节`);
      } catch (error) {
        console.error(`[Pad] 无法读取client-pad.html: ${error}`);
      }
    },
    configureServer(server) {
      // 在transformIndexHtml后注入中间件，确保能获取到转换后的HTML内容
      return () => {
        // 注册中间件 - 处理所有请求
        server.middlewares.use((req, res, next) => {
          // 确保有URL
          if (!req.url) {
            return next();
          }

          const url = req.url;

          // 更精确地判断静态资源 - 按文件扩展名检查
          const isJsRequest = /\.(js|jsx|ts|tsx|mjs)(\?|$)/.test(url);
          const isCssRequest = /\.(css|less|sass|scss)(\?|$)/.test(url);
          const isImageRequest = /\.(png|jpg|jpeg|gif|svg|webp|avif)(\?|$)/.test(url);
          const isFontRequest = /\.(woff|woff2|eot|ttf|otf)(\?|$)/.test(url);
          const isJsonRequest = /\.(json)(\?|$)/.test(url);
          const isMapRequest = /\.(map)(\?|$)/.test(url);

          // Vite内部请求 - 始终放行
          const isViteInternal =
            url.startsWith('/@') || url.includes('vite-hmr') || url.includes('vite-client') || url.startsWith('/@fs/') || url.startsWith('/node_modules/');

          // 所有静态资源请求
          const isAssetRequest =
            isJsRequest || isCssRequest || isImageRequest || isFontRequest || isJsonRequest || isMapRequest || isViteInternal || url.startsWith('/assets/');

          // 静态资源直接放行
          if (isAssetRequest) {
            return next();
          }

          // HTML请求 - 明确只处理这些URL
          const isExplicitHtmlRequest = url === '/' || url === '/index.html' || url === '/client-pad.html';

          if (isExplicitHtmlRequest) {
            console.log(`[Pad] 直接提供HTML: ${url} -> client-pad.html`);
            res.statusCode = 200;
            res.setHeader('Content-Type', 'text/html');
            res.end(entryHtmlContent);
            return;
          }

          // 对于SPA路由 (不含扩展名的URL) - 使用后备处理，而不是直接返回HTML
          const isSpaRoute = !url.includes('.') && url.length > 1;

          if (isSpaRoute) {
            // 我们只添加额外的重定向逻辑到historyApiFallback机制之后
            // 让Vite的historyApiFallback先尝试处理
            next();
            return;
          }

          // 其他所有请求放行
          next();
        });
      };
    }
  };
}

// 验证入口文件
function verifyEntryFiles() {
  const entryTsPath = resolve(__dirname, 'src/apps/client-pad/main.ts');

  if (!existsSync(entryHtmlPath)) {
    console.error(`[Pad] 错误: 找不到入口HTML文件: ${entryHtmlPath}`);
    process.exit(1);
  }

  if (!existsSync(entryTsPath)) {
    console.error(`[Pad] 错误: 找不到入口TS文件: ${entryTsPath}`);
    process.exit(1);
  }

  console.log(`[Pad] 入口文件验证成功:`);
  console.log(`  - HTML: ${entryHtmlPath}`);
  console.log(`  - TS: ${entryTsPath}`);
}

// 验证环境文件是否存在
function verifyEnvFile(mode: string) {
  const envFilePath = `.env.${appType}.${mode}`;

  if (!existsSync(envFilePath)) {
    console.error(`[Pad] 错误: 找不到环境文件 ${envFilePath}`);
    console.error(`[Pad] 请手动创建环境文件，确保包含必要的配置`);
    process.exit(1); // 终止进程
  } else {
    console.log(`[Pad] 已找到环境文件: ${envFilePath}`);
  }
}

// 在配置开始前验证入口文件
verifyEntryFiles();

export default defineConfig(({ mode }) => {
  // 验证环境文件存在
  verifyEnvFile(mode);

  // 使用Vite内置的环境变量加载机制
  const env = loadEnv(mode, process.cwd(), '');

  // 打印当前环境模式
  console.log(`[Pad] 环境， mode: ${mode}`);

  // 同时支持production和stage环境
  const isBuildMode = mode === 'production' || mode === 'stage';

  // 配置插件
  const plugins = [
    // 强制使用client-pad.html的插件 - 放在最前面
    createForceClientPadPlugin(),
    vue(),
    VueMcp({ host: 'localhost', printUrl: true }),
    Inspect(),
    legacy({
      targets: ['defaults', 'chrome >= 49', 'safari >= 10', 'edge >= 14', 'ios >= 10', 'android >= 6'],
      modernPolyfills: true,
      externalSystemJS: true,
      renderLegacyChunks: true
    }),
    createPadLogPlugin(),
    VueDevTools({
      launchEditor: 'cursor',
      componentInspector: {
        toggleComboKey: 'meta-shift',
        toggleButtonVisibility: 'active',
        appendTo: /.+/,
        disableInspectorOnEditorOpen: false,
        cleanHtml: true
      }
    })
  ];

  // 在生产环境和stage环境都添加分析插件
  if (isBuildMode) {
    plugins.push(
      visualizer({
        filename: `dist-${appType}/stats-${mode}.html`,
        open: false,
        gzipSize: true,
        brotliSize: true
      }) as Plugin
    );
  }

  return {
    plugins,
    // 确保所有资源都基于根目录
    base: '/',

    // 这是单入口应用，指定根目录为项目根目录
    root: __dirname,
    publicDir: resolve(__dirname, 'public'),

    // 使用简单的define配置
    define: {
      __APP_TYPE__: JSON.stringify(appType),
      __APP_VERSION__: JSON.stringify(appVersion),
      __APP_MODE__: JSON.stringify(mode),
      __IS_PROD__: mode === 'production',
      __IS_DEV__: mode === 'development',
      __IS_STAGE__: mode === 'stage'
    },

    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/style/element-plus/index.scss" as *;`
        }
      },
      devSourcemap: false
    },

    build: {
      outDir: `dist-${appType}`,
      sourcemap: false,
      target: 'es2015',
      minify: 'esbuild',
      cssCodeSplit: false,
      assetsInlineLimit: 4096,
      chunkSizeWarningLimit: 5000,
      emptyOutDir: true,
      esbuildOptions: {
        drop: isBuildMode ? ['console', 'debugger'] : [],
        concurrency: 3,
        legalComments: 'none',
        treeShaking: true,
        minifySyntax: true,
        minifyWhitespace: true,
        target: ['es2015']
      },
      // 对于单入口应用，直接指定入口文件路径
      rollupOptions: {
        input: entryHtmlPath,
        output: {
          // 代码分块策略 - 与主配置保持一致的极简化分块策略
          manualChunks: id => {
            // 极简化分块策略 - 只分成两个主要块
            if (id.includes('node_modules')) {
              // 所有第三方依赖打包在一起，避免初始化顺序问题
              return 'vendor';
            }

            // 所有业务代码打包在一起
            return 'app';
          },
          // 优化资源文件命名 - 确保版本更新时强制刷新
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: assetInfo => (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name ?? '') ? 'assets/fonts/[name][extname]' : 'assets/[name]-[hash][extname]')
        }
      }
    },

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },

    server: {
      host: '0.0.0.0',
      port: 5174,
      strictPort: true,
      hmr: { overlay: false },
      fs: {
        allow: ['..']
      },
      middlewareMode: false,
      // 直接打开项目
      open: true,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*'
      },
      // 开启Vite的historyApiFallback作为后备机制
      historyApiFallback: {
        disableDotRule: true,
        index: '/client-pad.html',
        rewrites: [{ from: /^\//, to: '/client-pad.html' }]
      }
    },

    optimizeDeps: {
      // 明确指定单一入口
      entries: [entryHtmlPath],
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'element-plus',
        'axios',
        'dayjs',
        'mitt',
        'pinia-plugin-persistedstate',
        '@element-plus/icons-vue'
      ],
      force: true
    },

    esbuild: {
      tsconfigRaw: JSON.stringify({
        compilerOptions: {
          skipLibCheck: true,
          ignoreDeprecations: '5.0'
        }
      })
    },

    // 针对生产构建配置
    preview: {
      port: 5174,
      // 启用historyApiFallback
      historyApiFallback: {
        index: '/client-pad.html',
        disableDotRule: true
      }
    },

    test: {
      globals: true,
      environment: 'jsdom',
      alias: [{ find: '@', replacement: resolve(__dirname, 'src') }]
    }
  };
});
