import { CartItem } from '@/apps/client-pad/views/ProductOrder/viewmodel';

/**
 * 购物车转换器类
 * 用于处理购物车数据的转换
 */
export class CartDrawerConverter {
  /**
   * 计算购物车总金额
   * @param cartItems 购物车商品列表
   * @returns 总金额（分）
   */
  public static calculateTotalAmount(cartItems: CartItem[]): number {
    return cartItems.reduce((total, item) => {
      return total + item.currentPrice * item.quantity;
    }, 0);
  }

  /**
   * 将分转换为元
   * @param priceInFen 以分为单位的价格
   * @returns 以元为单位的价格
   */
  public static fenToYuan(priceInFen: number): number {
    return priceInFen / 100;
  }

  /**
   * 格式化价格显示（单位：元）
   * @param priceInFen 以分为单位的价格
   * @returns 格式化后的价格字符串（元）
   */
  public static formatPrice(priceInFen: number): string {
    return this.fenToYuan(priceInFen).toFixed(2);
  }
}
