import request from "@/utils/request";
import * as Types from "../shared/types";

/** 创建出品单打印任务 根据订单号创建出品单打印任务，返回PrintTask格式数据 POST /api/print-record/product-out/create */
export async function postApiPrintRecordProductOutCreate (body: Types.CreateProductOutPrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOutPrintTaskVO[]>('/api/print-record/product-out/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据会话ID获取出品单打印记录 根据会话ID获取出品单打印记录 GET /api/print-record/product-out/session */
export async function getApiPrintRecordProductOutSession(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getApiPrintRecordProductOutSessionParams,
  options?: { [key: string]: any }
) {
  return request<Types.ProductOutPrintRecordVO[]>('/api/print-record/product-out/session', {
    method: 'GET',
    params: {
      ...params
    },
    ...(options || {})
  });
}

/** 通过订单号查询订单信息 通过订单号查询订单及其关联的房间计划和产品信息 POST /api/productOut/query-by-nos */
export async function postApiProductOutQueryByNos (body: Types.QueryOrdersByNosReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderDetailsMap>('/api/productOut/query-by-nos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建交班单打印记录 创建交班单打印记录 POST /api/print-record/shift-change/create */
export async function postApiPrintRecordShiftChangeCreate (body: Types.CreateShiftChangePrintRecordReq, options?: { [key: string]: any }) {
  return request<Types.ShiftChangePrintRecordVO>('/api/print-record/shift-change/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建结账单打印记录 创建结账单打印记录 POST /api/print-record/checkout/create */
export async function postApiPrintRecordCheckoutCreate (body: Types.CreateCheckoutPrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.CheckoutPrintRecordVO>('/api/print-record/checkout/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建开台单打印记录 创建开台单打印记录 POST /api/print-record/open-table/create */
export async function postApiPrintRecordOpenTableCreate (body: Types.CreateOpenTablePrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.OpenTablePrintRecordVO>('/api/print-record/open-table/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建续房单打印记录 创建续房单打印记录 POST /api/print-record/room-extension/create */
export async function postApiPrintRecordRoomExtensionCreate (body: Types.CreateRoomExtensionPrintRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExtensionPrintRecordVO>('/api/print-record/room-extension/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
