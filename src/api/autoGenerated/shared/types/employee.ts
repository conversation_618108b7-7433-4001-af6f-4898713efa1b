// employee相关类型定义

export type AddEmployeeGiftRecordReqDto = {
    /** 赠金金额 */
    amount?: number;
    /** 员工ID */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 扩展信息(JSON) */
    info?: string;
    /** 会员ID */
    memberId?: string;
    /** 会员姓名 */
    memberName?: string;
    /** 操作类型 */
    operateType?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 会话ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddEmployeeGroupEmployeeReqDto = {
    /** 员工组ID */
    employeeGroupId?: string;
    /** 员工ID */
    employeeId?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddEmployeeGroupReqDto = {
    /** 直属成员 */
    directMembers?: string;
    /** 员工组名称 */
    name?: string;
    /** 主管 */
    supervisor?: string;
  };

export type AddEmployeeReqDto = {
    /** 是否可以管理组 */
    canManageGroups?: boolean;
    /** 是否可以查看佣金业绩 */
    canViewCommissionPerformance?: boolean;
    /** 员工卡ID */
    employeeCardId?: string;
    /** 员工组 */
    employeeGroup?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 营销角色 */
    marketingRole?: string;
    /** 员工姓名 */
    name?: string;
    /** 密码 */
    password?: string;
    /** 权限角色 */
    permissionRole?: string;
    /** 权限列表 */
    permissions?: string;
    /** 电话号码 */
    phone?: string;
    /** 销售业绩 */
    salesPerformance?: number;
    /** 员工类型 */
    type?: string;
    /** 微信unionid */
    unionid?: string;
    /** 门店ID */
    venueId?: string;
    /** 微信绑定 */
    wechatBinding?: string;
  };

export type AddVenueAndEmployeeReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteEmployeeGiftRecordReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteEmployeeGroupEmployeeReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteEmployeeGroupReqDto = {
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteEmployeeReqDto = {
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteVenueAndEmployeeReqDto = {
    /** ID */
    id?: string;
  };

export type EmployeeGiftRecordVO = {
    /** 赠金金额 */
    amount?: number;
    /** 创建时间 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    id?: string;
    /** 扩展信息(JSON) */
    info?: string;
    /** 会员ID */
    memberId?: string;
    /** 会员姓名 */
    memberName?: string;
    /** 操作类型 */
    operateType?: string;
    /** 额度 */
    quota?: number;
    /** 剩余额度 */
    remainQuota?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 会话ID */
    sessionId?: string;
    /** 状态（1-有效 0-删除） */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type EmployeeGroupEmployeeVO = {
    /** 创建时间 */
    ctime?: number;
    /** 员工组ID */
    employeeGroupId?: string;
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type EmployeeGroupVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 直属成员 */
    directMembers?: string;
    /** ID */
    id?: string;
    /** 员工组名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 主管 */
    supervisor?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type EmployeeInviteVO = {
    /** 邀请二维码 */
    inviteQrCode?: string;
  };

export type EmployeeVO = {
    /** 是否可以管理组 */
    canManageGroups?: boolean;
    /** 是否可以查看佣金业绩 */
    canViewCommissionPerformance?: boolean;
    /** 创建时间戳 */
    ctime?: number;
    /** 员工卡ID */
    employeeCardId?: string;
    /** 员工组 */
    employeeGroup?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** ID */
    id?: string;
    /** 是否是门店老板 */
    isBoss?: boolean;
    /** 营销角色 */
    marketingRole?: string;
    /** 员工姓名 */
    name?: string;
    /** 权限角色 */
    permissionRole?: string;
    /** 权限列表 */
    permissions?: string;
    /** 电话号码 */
    phone?: string;
    /** 审核状态（0-待审核 1-通过 2-拒绝） */
    reviewStatus?: number;
    /** 销售业绩 */
    salesPerformance?: number;
    /** 状态值 */
    state?: number;
    /** 员工类型 */
    type?: string;
    /** 微信unionid */
    unionid?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
    /** 微信绑定 */
    wechatBinding?: string;
  };

export type ERPUserAndEmployeeVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** ERP用户ID */
    erpUserId?: string;
    /** ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type InviteEmployeeReqDto = {
    /** 门店ID */
    venueId?: string;
  };

export type LoginVenueAndEmployeeVO = {
    /** 门店地址 */
    address?: string;
    /** 用于前端的appId，与加密狗绑定 */
    appId?: string;
    /** 用于前端的appKey，与加密狗绑定 */
    appKey?: string;
    /** 审核状态：0-待审核 1-已通过 2-已拒绝 */
    auditStatus?: number;
    /** 市 */
    city?: string;
    /** 联系人 */
    contact?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 门店描述 */
    description?: string;
    /** 区 */
    district?: string;
    /** 营业结束时间 */
    endHours?: string;
    /** ID */
    id?: string;
    /** 是否是雷石VOD点歌系统 */
    isThunderVOD?: number;
    /** 登录员工信息 */
    loginEmployeeVO?: EmployeeVO;
    /** 门店logo URL */
    logo?: string;
    /** 门店名称 */
    name?: string;
    /** 门店照片URL列表 */
    photos?: string;
    /** 省 */
    province?: string;
    /** 营业开始时间 */
    startHours?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店类型 */
    venueType?: number;
    /** 版本号 */
    version?: number;
  };

export type PageVOArrayVoEmployeeGroupEmployeeVO = {
    data?: EmployeeGroupEmployeeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoEmployeeVO = {
    data?: EmployeeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoERPUserAndEmployeeVO = {
    data?: ERPUserAndEmployeeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoVenueAndEmployeeVO = {
    data?: VenueAndEmployeeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type QueryEmployeeGiftRecordReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 会话ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryEmployeeGroupEmployeeReqDto = {
    /** 员工组ID */
    employeeGroupId?: string;
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryEmployeeGroupReqDto = {
    /** 直属成员 */
    directMembers?: string;
    /** ID */
    id?: string;
    /** 员工组名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 主管 */
    supervisor?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryEmployeeReqDto = {
    /** 员工卡ID */
    employeeCardId?: string;
    /** 员工组 */
    employeeGroup?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 是否是老板 */
    isBoss?: boolean;
    /** 营销角色 */
    marketingRole?: string;
    /** 员工姓名 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 权限角色 */
    permissionRole?: string;
    /** 电话号码 */
    phone?: string;
    /** 审核状态 */
    reviewStatus?: number;
    /** 员工类型 */
    type?: string;
    /** 微信unionid */
    unionid?: string;
    /** 门店ID */
    venueId?: string;
    /** 门店ID列表 */
    venueIds?: string[];
    /** 微信绑定 */
    wechatBinding?: string;
  };

export type QueryVenueAndEmployeeReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 员工ID列表 */
    employeeIds?: string[];
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type ResultArrayVoEmployeeGiftRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeGiftRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoEmployeeGroupEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeGroupEmployeeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoEmployeeGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeGroupVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoERPUserAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ERPUserAndEmployeeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoVenueAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAndEmployeeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoEmployeeGiftRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeGiftRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoEmployeeGroupEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeGroupEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoEmployeeGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeGroupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoEmployeeInviteVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeInviteVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoERPUserAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ERPUserAndEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoLoginVenueAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: LoginVenueAndEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoEmployeeGroupEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoEmployeeGroupEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoERPUserAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoERPUserAndEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoVenueAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoVenueAndEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueAndEmployeeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAndEmployeeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ReviewEmployeeReqDto = {
    /** 员工ID */
    id: string;
    /** 审核状态 (1: 通过, 2: 拒绝) */
    reviewStatus: number;
    /** 门店id */
    venueId: string;
  };

export type UpdateEmployeeGiftRecordReqDto = {
    /** 赠金金额 */
    amount?: number;
    /** 员工ID */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** ID */
    id?: string;
    /** 扩展信息(JSON) */
    info?: string;
    /** 会员ID */
    memberId?: string;
    /** 会员姓名 */
    memberName?: string;
    /** 操作类型 */
    operateType?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 会话ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateEmployeeGroupEmployeeReqDto = {
    /** 员工组ID */
    employeeGroupId?: string;
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateEmployeeGroupReqDto = {
    /** 直属成员 */
    directMembers?: string;
    /** ID */
    id?: string;
    /** 员工组名称 */
    name?: string;
    /** 主管 */
    supervisor?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateEmployeeReqDto = {
    /** 是否可以管理组 */
    canManageGroups?: boolean;
    /** 是否可以查看佣金业绩 */
    canViewCommissionPerformance?: boolean;
    /** 员工卡ID */
    employeeCardId?: string;
    /** 员工组 */
    employeeGroup?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** ID */
    id?: string;
    /** 营销角色 */
    marketingRole?: string;
    /** 员工姓名 */
    name?: string;
    /** 密码 */
    password?: string;
    /** 权限角色 */
    permissionRole?: string;
    /** 权限列表 */
    permissions?: string;
    /** 电话号码 */
    phone?: string;
    /** 销售业绩 */
    salesPerformance?: number;
    /** 员工类型 */
    type?: string;
    /** 微信unionid */
    unionid?: string;
    /** 门店ID */
    venueId?: string;
    /** 微信绑定 */
    wechatBinding?: string;
  };

export type UpdateVenueAndEmployeeReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type VenueAndEmployeeVO = {
    /** 创建时间 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };
