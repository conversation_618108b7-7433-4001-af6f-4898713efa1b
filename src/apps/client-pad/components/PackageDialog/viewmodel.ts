/**
 * 套餐选择侧边栏组件的视图模型
 */

// 产品项类型
export interface IProductItem {
  id: string;
  name: string;
  imageUrl: string;
  price: number;
  quantity: number;
  unit: string;
}

// 可选组类型
export interface IOptionalGroup {
  name: string;
  optionType: 'by_count' | 'by_plan'; // by_count: 按数量选择, by_plan: 按计划选择
  optionCount: number; // 需要选择的数量
  selectedCount: number; // 已选择的数量
  products: IProductItem[];
  billType: string; // 付费类型，如：'paid'
}

// 套餐选择侧边栏视图模型接口
export interface IPackageDialogViewModel {
  // 显示状态
  visible: boolean;

  // 套餐信息
  packageId: string;
  packageName: string;
  packageImageUrl: string;
  packageQuantity: number;

  // 商品列表
  fixedProducts: IProductItem[];
  optionalGroups: IOptionalGroup[];

  // 已选商品文本
  selectedItemsText: string;

  // 总价
  totalPrice: number;

  // 是否可以确认
  canConfirm: boolean;
}

// 套餐选择侧边栏操作接口
export interface IPackageDialogActions {
  // 打开套餐选择对话框
  openDialog: (product: any) => void;

  // 关闭套餐选择对话框
  closeDialog: () => void;

  // 增加可选商品数量
  increaseProduct: (groupIndex: number, productId: string) => void;

  // 减少可选商品数量
  decreaseProduct: (groupIndex: number, productId: string) => void;

  // 增加套餐数量
  increasePackageQuantity: () => void;

  // 减少套餐数量
  decreasePackageQuantity: () => void;

  // 确认选择
  confirmSelection: () => void;
}
