import {
  Receipt,
  ReceiptElement,
  ReceiptElementType,
  ReceiptText,
  ReceiptTable,
  ReceiptLine,
  ReceiptEmptyLine,
  TextAlignment,
  FontSize,
} from '@/domains/prints/shared/models/receipt-model';
import { PrintCommand } from './printerTypes';
import { PrintCommands } from './printCommands';
import { thermoPrinterSimulator } from './thermoPrinterSimulator';


/**
 * EscPosCommandGenerator 基础设施层服务
 * 负责将领域层的 Receipt 对象转换为打印机可以理解的 ESC/POS 命令
 */
export class EscPosCommandGenerator {
  /**
   * 将 Receipt 对象转换为打印命令序列
   * @param receipt 领域层的票据对象
   * @param simulatePrint 是否开启模拟打印（在控制台显示）
   * @returns 打印命令数组
   */
  generate(receipt: Receipt, simulatePrint: boolean = false): PrintCommand[] {
    const commands: PrintCommand[] = [];

    // 初始化打印机
    commands.push(PrintCommands.command(PrintCommands.INIT));
    commands.push(PrintCommands.command(PrintCommands.CHINESE_MODE));

    // 处理每个票据元素
    for (const element of receipt.elements) {
      const elementCommands = this.convertElement(element);
      commands.push(...elementCommands);
    }

    // 添加结尾命令（切纸）
    commands.push(PrintCommands.command(PrintCommands.LINE_FEED));
    commands.push(PrintCommands.command(PrintCommands.LINE_FEED));
    commands.push(PrintCommands.command(PrintCommands.CUT_PAPER));

    // 如果开启模拟打印，则在控制台输出模拟效果
    if (simulatePrint) {
      thermoPrinterSimulator.simulatePrint(receipt);
    }

    return commands;
  }

  /**
   * 将单个票据元素转换为打印命令
   */
  private convertElement(element: ReceiptElement): PrintCommand[] {
    switch (element.type) {
      case ReceiptElementType.TEXT:
        return this.convertTextElement(element as ReceiptText);
      case ReceiptElementType.TABLE:
        return this.convertTableElement(element as ReceiptTable);
      case ReceiptElementType.LINE:
        return this.convertLineElement(element as ReceiptLine);
      case ReceiptElementType.EMPTY_LINE:
        return this.convertEmptyLineElement(element as ReceiptEmptyLine);
      default:
        console.warn('未知的票据元素类型:', element.type);
        return [];
    }
  }

  /**
   * 转换文本元素
   */
  private convertTextElement(element: ReceiptText): PrintCommand[] {
    const commands: PrintCommand[] = [];

    // 设置对齐方式
    if (element.align) {
      switch (element.align) {
        case TextAlignment.CENTER:
          commands.push(PrintCommands.command(PrintCommands.ALIGN_CENTER));
          break;
        case TextAlignment.RIGHT:
          commands.push(PrintCommands.command(PrintCommands.ALIGN_RIGHT));
          break;
        default:
          commands.push(PrintCommands.command(PrintCommands.ALIGN_LEFT));
          break;
      }
    } else {
      commands.push(PrintCommands.command(PrintCommands.ALIGN_LEFT));
    }

    // 设置字体样式
    if (element.bold) {
      commands.push(PrintCommands.command(PrintCommands.BOLD_ON));
    }

    // 设置字体大小
    if (element.fontSize) {
      switch (element.fontSize) {
        case FontSize.SMALL:
          commands.push(PrintCommands.command(PrintCommands.FONT_SMALL));
          break;
        case FontSize.DOUBLE:
          commands.push(PrintCommands.command(PrintCommands.FONT_SIZE_DOUBLE));
          break;
        case FontSize.DOUBLE_HEIGHT:
          commands.push(PrintCommands.command(PrintCommands.FONT_SIZE_DOUBLE_HEIGHT));
          break;
        case FontSize.DOUBLE_WIDTH:
          commands.push(PrintCommands.command(PrintCommands.FONT_SIZE_DOUBLE_WIDTH));
          break;
        default:
          commands.push(PrintCommands.command(PrintCommands.FONT_SIZE_NORMAL));
          break;
      }
    }

    // 添加文本内容
    let content = element.content;
    if (!content.endsWith('\n')) {
      content += '\n';
    }
    commands.push(PrintCommands.text(content));

    // 重置样式（如果需要）
    if (element.bold) {
      commands.push(PrintCommands.command(PrintCommands.BOLD_OFF));
    }
    if (element.fontSize && element.fontSize !== FontSize.NORMAL) {
      commands.push(PrintCommands.command(PrintCommands.FONT_SIZE_NORMAL));
    }

    return commands;
  }

  /**
   * 转换表格元素
   */
  private convertTableElement(element: ReceiptTable): PrintCommand[] {
    const commands: PrintCommand[] = [];

    // 创建 PrintCommands 可以理解的表格数据格式
    for (const row of element.columns) {
      const tableRow = row.map((column) => ({
        text: column.text,
        width: column.width,
        align: column.align === TextAlignment.RIGHT ? 'right' : 'left',
      }));

      commands.push(PrintCommands.table(tableRow as { text: string; width: number; align?: 'right' | 'left' }[]));
    }

    return commands;
  }

  /**
   * 转换线条元素
   */
  private convertLineElement(element: ReceiptLine): PrintCommand[] {
    return [PrintCommands.line(element.char, element.length)];
  }

  /**
   * 转换空行元素
   */
  private convertEmptyLineElement(element: ReceiptEmptyLine): PrintCommand[] {
    return [PrintCommands.emptyLine(element.lines)];
  }
}

// 导出单例
export const escPosCommandGenerator = new EscPosCommandGenerator(); 