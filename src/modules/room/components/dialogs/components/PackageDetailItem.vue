<template>
  <div v-if="isDetail" class="package-detail-expanded-item">
    <div class="w-full px-4 py-2">
      <!-- 套餐明细逻辑 - 以tag形式显示 -->
      <template v-if="isPackageDetail && packageDetailItems.length > 0">
        <div class="flex flex-wrap -mx-1">
          <div v-for="(item, index) in packageDetailItems" :key="index" class="px-1 py-1">
            <span class="inline-block rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600"> {{ item.name }} × {{ item.count }} </span>
          </div>
        </div>
      </template>

      <!-- 可选组明细逻辑 - 以tag形式显示 -->
      <template v-else-if="isOptionalGroupDetail && optionalGroupItems.length > 0">
        <div class="flex flex-wrap -mx-1">
          <div v-for="(item, index) in optionalGroupItems" :key="index" class="px-1 py-1">
            <span class="inline-block rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600"> {{ item.name }} × {{ item.count }} </span>
          </div>
        </div>
      </template>

      <!-- 如果没有解析出具体商品项，显示原始文本 -->
      <template v-else-if="packageDetailText || optionalGroupDetailText">
        <div class="text-gray-600 text-sm">
          {{ packageDetailText || optionalGroupDetailText }}
        </div>
      </template>

      <!-- 无明细数据时的提示 -->
      <template v-else>
        <div class="text-xs text-gray-400">暂无明细信息</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { PackageDetailItemProps, ExtendedCartItem } from '../types';
import { generateOptionalGroupDetailString } from '../utils';

// 定义商品项接口
interface DetailItem {
  name: string;
  count: number;
  id?: string;
}

// 定义Props
const props = defineProps<PackageDetailItemProps>();

// 为了兼容旧写法，创建rowRef
const rowData = computed<ExtendedCartItem>(() => props.row as ExtendedCartItem);

// 解析套餐明细项为tag数组
const packageDetailItems = computed<DetailItem[]>(() => {
  const item = rowData.value;
  if (!item || !isPackageDetail.value) return [];

  // 🔥 重要：首先尝试从packageDetail中读取selectedProducts
  if (item.packageDetail) {
    try {
      let packageDetailObj = item.packageDetail;
      if (typeof packageDetailObj === 'string') {
        packageDetailObj = JSON.parse(packageDetailObj);
      }

      // 优先检查是否有selectedProducts字段
      if (packageDetailObj && 'selectedProducts' in packageDetailObj && Array.isArray(packageDetailObj.selectedProducts)) {
        const selectedProducts = packageDetailObj.selectedProducts;
        if (selectedProducts.length > 0) {
          console.log(`🔍 PackageDetailItem: 使用selectedProducts数据`, {
            count: selectedProducts.length,
            products: selectedProducts
          });

          // 🔥 重要修复：根据父商品数量计算明细商品数量
          const parentItem = props.parent;
          const parentQuantity = parentItem?.quantity || 1;

          return selectedProducts.map((product: any) => ({
            id: product.id,
            name: product.name,
            count: (product.count || 1) * parentQuantity // 套餐数量 × 商品基础数量
          }));
        }
      }

      // 兜底：尝试从packageProducts解析
      if (packageDetailObj?.packageProducts) {
        const products = typeof packageDetailObj.packageProducts === 'string' ? JSON.parse(packageDetailObj.packageProducts) : packageDetailObj.packageProducts;
        const productVOList = packageDetailObj.productVOList || [];

        if (Array.isArray(products)) {
          // 🔥 重要修复：根据父商品数量计算明细商品数量
          const parentItem = props.parent;
          const parentQuantity = parentItem?.quantity || 1;

          return products.map((product: any) => {
            const productInfo = productVOList.find((p: any) => p.id === product.id);
            const baseCount = product.count || product.quantity || 1;
            return {
              id: product.id,
              name: productInfo?.name || product.name || `商品(${product.id})`,
              count: baseCount * parentQuantity // 套餐数量 × 商品基础数量
            };
          });
        }
      }
    } catch (e) {
      console.error('🔍 解析套餐明细失败:', e);
    }
  }

  return [];
});

// 解析可选组明细项为tag数组
const optionalGroupItems = computed<DetailItem[]>(() => {
  const item = rowData.value;
  if (!item || !isOptionalGroupDetail.value) return [];

  // 🔥 重要：首先尝试从packageDetail中读取selectedProducts（可选组也可能有这个字段）
  if (item.packageDetail) {
    try {
      let packageDetailObj = item.packageDetail;
      if (typeof packageDetailObj === 'string') {
        packageDetailObj = JSON.parse(packageDetailObj);
      }

      // 优先检查是否有selectedProducts字段
      if (packageDetailObj && 'selectedProducts' in packageDetailObj && Array.isArray(packageDetailObj.selectedProducts)) {
        const selectedProducts = packageDetailObj.selectedProducts;
        if (selectedProducts.length > 0) {
          console.log(`🔍 PackageDetailItem: 可选组使用selectedProducts数据`, {
            count: selectedProducts.length,
            products: selectedProducts
          });

          // 🔥 重要修复：根据父商品数量计算明细商品数量
          const parentItem = props.parent;
          const parentQuantity = parentItem?.quantity || 1;

          return selectedProducts.map((product: any) => ({
            id: product.id,
            name: product.name,
            count: (product.count || 1) * parentQuantity // 可选组数量 × 商品基础数量
          }));
        }
      }

      // 兜底：从optionalGroups解析
      if (packageDetailObj?.optionalGroups) {
        const result: DetailItem[] = [];
        const optionalGroups =
          typeof packageDetailObj.optionalGroups === 'string' ? JSON.parse(packageDetailObj.optionalGroups) : packageDetailObj.optionalGroups;
        const productVOList = packageDetailObj.productVOList || [];

        if (Array.isArray(optionalGroups)) {
          // 🔥 重要修复：根据父商品数量计算明细商品数量
          const parentItem = props.parent;
          const parentQuantity = parentItem?.quantity || 1;

          optionalGroups.forEach((group: any) => {
            const groupOptionType = group.optionType || group.type || 'by_count';

            if (Array.isArray(group.products)) {
              group.products.forEach((product: any) => {
                let isSelected = false;
                let baseFinalCount = 0;

                if (groupOptionType === 'by_plan') {
                  const selectedCount = product.selected_count || 0;
                  isSelected = selectedCount > 0;
                  baseFinalCount = (product.count || 1) * selectedCount;
                } else {
                  const selectedCount = product.selected_count || 0;
                  isSelected = selectedCount > 0;
                  baseFinalCount = selectedCount;
                }

                if (isSelected && baseFinalCount > 0) {
                  const productInfo = productVOList.find((p: any) => p.id === product.id);
                  const productName = productInfo?.name || product.name || `可选商品(${product.id})`;

                  result.push({
                    id: product.id,
                    name: productName,
                    count: baseFinalCount * parentQuantity // 基础数量 × 父商品数量
                  });
                }
              });
            }
          });
        }

        return result;
      }
    } catch (e) {
      console.error('🔍 解析可选组明细失败:', e);
    }
  }

  // 最后兜底：从details字段解析文本
  if (item.details && typeof item.details === 'string' && item.details !== '未选择任何商品') {
    try {
      // 🔥 重要修复：根据父商品数量计算明细商品数量
      const parentItem = props.parent;
      const parentQuantity = parentItem?.quantity || 1;

      const items = item.details
        .split('，')
        .map((detailItem: string, index: number) => {
          const match = detailItem.match(/^(.+?)×(\d+)$/);
          if (match) {
            const [, name, count] = match;
            const baseCount = parseInt(count, 10);
            return {
              id: `parsed-${index}`,
              name: name.trim(),
              count: baseCount * parentQuantity // 基础数量 × 父商品数量
            };
          }
          return null;
        })
        .filter(item => item !== null);

      return items;
    } catch (e) {
      console.error('🔍 从details解析可选组明细失败:', e);
    }
  }

  return [];
});

// 判定可选组明细
const isOptionalGroupDetail = computed(() => {
  const result = !!rowData.value?.isOptionalGroup;
  console.log(`🔍 PackageDetailItem isOptionalGroupDetail:`, {
    itemId: rowData.value?.id,
    itemName: rowData.value?.name,
    isOptionalGroup: rowData.value?.isOptionalGroup,
    result
  });
  return result;
});

// 判定套餐明细
const isPackageDetail = computed(() => {
  const result = !!rowData.value?.isPackage;
  console.log(`🔍 PackageDetailItem isPackageDetail:`, {
    itemId: rowData.value?.id,
    itemName: rowData.value?.name,
    isPackage: rowData.value?.isPackage,
    hasPackageDetail: !!rowData.value?.packageDetail,
    result
  });
  return result;
});

// 套餐明细文本
const packageDetailText = computed(() => {
  const item = rowData.value;
  if (!item || !isPackageDetail.value) {
    console.log(`🔍 PackageDetailItem packageDetailText: 不是套餐明细`, {
      isPackageDetail: isPackageDetail.value,
      itemExists: !!item
    });
    return '';
  }

  // 对于明细行，直接使用name字段中的明细文本
  if (props.isDetail && item.name) {
    console.log(`🔍 PackageDetailItem packageDetailText: 使用明细行name字段`, {
      name: item.name,
      isDetail: props.isDetail
    });
    return item.name;
  }

  // 对于主商品，查找packageDetail
  if (item.packageDetail?.detailString) {
    console.log(`🔍 PackageDetailItem packageDetailText: 使用packageDetail.detailString`, {
      detailString: item.packageDetail.detailString
    });
    return item.packageDetail.detailString;
  }

  console.log(`🔍 PackageDetailItem packageDetailText: 无有效明细数据`, {
    hasPackageDetail: !!item.packageDetail,
    name: item.name,
    isDetail: props.isDetail
  });
  return '';
});

// 可选组明细文本
const optionalGroupDetailText = computed(() => {
  const item = rowData.value;
  if (!item || !isOptionalGroupDetail.value) {
    return '';
  }

  // 对于明细行，直接使用name字段中的明细文本
  if (props.isDetail && item.name) {
    console.log(`🔍 PackageDetailItem optionalGroupDetailText: 使用明细行name字段`, {
      name: item.name,
      isDetail: props.isDetail
    });
    return item.name;
  }

  // 对于主商品，查找details字段
  if (item.details) {
    return item.details;
  }

  if (item.optionalProducts && Array.isArray(item.optionalProducts)) {
    try {
      const result = generateOptionalGroupDetailString(item.optionalProducts);
      return result;
    } catch (error) {
      console.error(`🔍 PackageDetailItem optionalGroupDetailText 生成失败`, error);
      return '';
    }
  }
  return '';
});
</script>

<style lang="scss" scoped>
.package-detail-expanded-item {
  background-color: #f9f9f9;
  min-height: 32px;
  width: 100%;
  padding: 4px 0;
}

/* 确保内容不被截断 */
.break-words {
  white-space: normal;
  word-break: break-word;
  line-height: 1.4;
  min-height: 20px;
  width: 100%;
  display: block;
}
</style>
