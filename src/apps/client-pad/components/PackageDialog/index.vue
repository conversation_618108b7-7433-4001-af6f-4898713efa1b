<!-- 
  PackageDialog/index.vue
  套餐选择侧边栏组件
-->
<template>
  <el-drawer v-model="state.visible" direction="rtl" size="61%" :with-header="false" :destroy-on-close="true" @close="actions.closeDialog">
    <div class="package-drawer">
      <div class="drawer-close">
        <el-icon class="close-icon" @click="actions.closeDialog"><Close /></el-icon>
      </div>

      <!-- 套餐商品信息 -->
      <div class="header-info">
        <div class="product-image">
          <img :src="state.packageImageUrl" :alt="state.packageName" @error="handleImageError" />
        </div>

        <div class="package-info">
          <h2 class="package-name">{{ state.packageName }}</h2>
          <div class="selected-items">已选: {{ state.selectedItemsText }}</div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto">
        <!-- 固定商品 -->
        <h3 class="section-title">固定商品</h3>
        <div class="products-grid fixed-products">
          <div v-for="product in state.fixedProducts" :key="product.id" class="product-card">
            <div class="product-image">
              <img :src="product.imageUrl" :alt="product.name" @error="handleImageError" />
            </div>
            <div class="product-detail">
              <h4 class="product-name">{{ product.name }}</h4>
              <div class="quantity-control">
                <div class="quantity-display">
                  <span class="number">{{ product.quantity }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 可选商品组 -->
        <div v-for="(group, groupIndex) in state.optionalGroups" :key="groupIndex">
          <h3 class="section-title">{{ group.name }} (已选: {{ group.selectedCount }}/{{ group.optionCount }})</h3>
          <div class="products-grid optional-products">
            <div v-for="product in group.products" :key="product.id" class="product-card">
              <div class="product-image">
                <img :src="product.imageUrl" :alt="product.name" @error="handleImageError" />
              </div>
              <div class="product-detail">
                <h4 class="product-name">{{ product.name }}</h4>
                <div class="quantity-control">
                  <template v-if="product.quantity > 0">
                    <div class="with-quantity">
                      <div class="minus-btn" @click="actions.decreaseProduct(groupIndex, product.id)"></div>
                      <div class="quantity-display">
                        <span class="number">{{ product.quantity }}</span>
                      </div>
                      <div class="plus-btn" @click="actions.increaseProduct(groupIndex, product.id)"></div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="add-btn" @click="actions.increaseProduct(groupIndex, product.id)"></div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部操作栏 -->
      <div class="footer-actions">
        <div class="quantity-control-container">
          <div class="add-quantity">
            <div class="minus-btn" @click="actions.decreasePackageQuantity()"></div>
            <div class="quantity-display">
              <span class="number">{{ state.packageQuantity }}</span>
            </div>
            <div class="plus-btn" @click="actions.increasePackageQuantity()"></div>
          </div>
        </div>

        <div class="price-container">
          <span class="price-symbol">¥</span>
          <span class="price-value">{{ (state.totalPrice / 100).toFixed(2) }}</span>
        </div>

        <el-button type="primary" class="add-to-cart-btn" :disabled="!state.canConfirm" @click="actions.confirmSelection"> 加入购物车 </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { ref } from 'vue';
import { Close } from '@element-plus/icons-vue';
import { handleImageError } from '@/utils/imageUtils';
import { usePackageDialog } from './presenter';

// 辅助函数：将分转为元并格式化
const fenToYuanFormatted = (priceInFen: number) => {
  return (priceInFen / 100).toFixed(2);
};

export default {
  name: 'PackageDialog',
  components: { Close },
  emits: ['confirm'],
  setup(props: any, { emit, expose }: { emit: any; expose: any }) {
    // 使用ViewModel
    const { state, actions } = usePackageDialog(emit);

    // 对外暴露方法
    expose({
      open: actions.openDialog
    });

    return {
      state,
      actions,
      handleImageError
    };
  }
};
</script>

<style scoped>
.drawer-close {
  position: absolute;
  right: 24px;
  top: 24px;
}

.close-icon {
  font-size: 32px;
  cursor: pointer;
  color: #666;
}

.close-icon:hover {
  color: #333;
}

.package-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header-info {
  display: flex;
  margin-bottom: 30px;
}

.product-image {
  width: 130px;
  height: 130px;
  border-radius: 12px;
  background-color: #f3f3f3;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.package-info {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.package-name {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 10px;
  color: rgba(0, 0, 0, 0.8);
}

.selected-items {
  font-size: 24px;
  color: #666;
  max-width: 832px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  margin: 20px 0;
  color: #000;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(244px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.product-card {
  width: 100%;
  height: 418px;
  background-color: #f3f3f3;
  border-radius: 12px;
  border: 1px dashed rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.product-card .product-image {
  width: 204px;
  height: 204px;
  margin: 20px auto;
  border-radius: 12px;
  overflow: hidden;
}

.product-detail {
  padding: 0 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 24px;
  font-weight: 600;
  color: #666;
  margin-bottom: 10px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.quantity-control {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.with-quantity {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 80px;
  width: 184px;
  height: 72px;
  position: relative;
}

.minus-btn,
.plus-btn {
  width: 72px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.minus-btn::before,
.plus-btn::before,
.plus-btn::after {
  content: '';
  position: absolute;
  background-color: #666;
  border-radius: 1px;
}

/* 横线 */
.minus-btn::before,
.plus-btn::before {
  width: 24px;
  height: 4px;
}

/* 加号的竖线 */
.plus-btn::after {
  width: 4px;
  height: 24px;
}

.quantity-display {
  flex: 1;
  font-size: 32px;
  text-align: center;
}

.number {
  font-family: 'MiSans-Demibold', sans-serif;
  font-weight: bold;
  color: #000;
}

.add-btn {
  width: 72px;
  height: 72px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.add-btn::before,
.add-btn::after {
  content: '';
  position: absolute;
  background-color: #666;
  border-radius: 1px;
}

/* 横线 */
.add-btn::before {
  width: 24px;
  height: 4px;
}

/* 竖线 */
.add-btn::after {
  width: 4px;
  height: 24px;
}

.footer-actions {
  height: 156px;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 50px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.quantity-control-container {
  display: flex;
  align-items: center;
}

.add-quantity {
  display: flex;
  align-items: center;
  background-color: #f3f3f3;
  border-radius: 80px;
  width: 228px;
  height: 94px;
}

.price-container {
  margin-left: 50px;
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 28px;
  color: #ff3333;
}

.price-value {
  font-size: 50px;
  font-weight: bold;
  color: #ff3333;
}

.price-unit {
  font-size: 28px;
  color: #ff3333;
}

.add-to-cart-btn {
  margin-left: auto;
  width: 278px;
  height: 100px;
  font-size: 32px;
  background-color: #e23939;
  border-color: #e23939;
  border-radius: 6px;
}

.add-to-cart-btn:hover {
  background-color: #c62929;
  border-color: #c62929;
}
</style>
