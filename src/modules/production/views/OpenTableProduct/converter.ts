import type { OrderProductVO } from '@/types/projectobj';
import type { ICartItem } from './viewmodel';

export class OpenTableProductConverter {
  /**
   * 将购物车商品转换为订单商品VO
   */
  static convertCartItemsToOrderProducts(cartItems: ICartItem[]): Partial<OrderProductVO>[] {
    return cartItems.map(item => ({
      id: item.id,
      productName: item.name,
      flavors: item.flavors || '',
      quantity: item.quantity,
      unit: item.unit,
      currentPrice: item.currentPrice,
      totalAmount: item.currentPrice * item.quantity,
      unitPrice: item.currentPrice,
      totalPrice: item.currentPrice * item.quantity,
      isPackage: item.isPackage,
      // 添加套餐明细字段
      packageDetail: item.packageDetail?.detailString || '',
      packageProducts: item.packageDetail?.packageProducts,
      optionalGroups: item.packageDetail?.optionalGroups,
      productVOList: item.packageDetail?.productVOList
    }));
  }

  /**
   * 从路由参数解析已有商品数据
   */
  static parseExistingProducts(outOrderProductsStr: string): ICartItem[] {
    try {
      const existingProducts = JSON.parse(outOrderProductsStr);
      return existingProducts.map((product: any) => ({
        id: product.id,
        name: product.productName,
        currentPrice: product.currentPrice,
        quantity: product.quantity,
        flavors: product.flavors,
        unit: product.unit,
        isPackage: Boolean(product.isPackage),
        packageDetail: product.packageDetail,
        packageProducts: product.packageProducts,
        optionalGroups: product.optionalGroups,
        productVOList: product.productVOList
      }));
    } catch (error) {
      console.error('解析已有商品数据失败:', error);
      return [];
    }
  }

  /**
   * 将商品数据转换为购物车项
   */
  static convertProductToCartItem(product: any): ICartItem {
    if (product.isPackage) {
      return this.convertPackageToCartItem(product);
    }

    return {
      id: product.id,
      name: product.name,
      currentPrice: product.currentPrice,
      quantity: 1,
      flavors: product.flavors,
      unit: product.unit,
      isPackage: false
    };
  }

  /**
   * 判断是否是可选套餐
   */
  static hasOptionalGroups(packageData: any): boolean {
    try {
      return packageData.optionalGroups && JSON.parse(packageData.optionalGroups).length > 0;
    } catch {
      return false;
    }
  }

  /**
   * 生成套餐唯一标识
   */
  static generatePackageUniqueId(packageData: any): string {
    if (!this.hasOptionalGroups(packageData)) return packageData.id;

    try {
      const optionalGroups = JSON.parse(packageData.optionalGroups);
      // 生成选择方案的字符串
      const selections = optionalGroups
        .map((group: any) => {
          return group.products
            .filter((p: any) => p.selected_count > 0)
            .map((p: any) => `${p.id}_${p.selected_count}`)
            .join(',');
        })
        .join('|');

      // 生成唯一标识
      return `${packageData.id}_${selections}`;
    } catch {
      return packageData.id;
    }
  }

  /**
   * 将套餐数据转换为购物车项
   */
  static convertPackageToCartItem(packageData: any): ICartItem {
    console.log('[addCart] convertPackageToCartItem packageData:', packageData);
    const hasOptionalGroups = this.hasOptionalGroups(packageData);

    // 生成套餐明细字符串
    const detailString = this.generatePackageDetailString(packageData);

    // 处理套餐商品数据，补充商品名称
    let enrichedPackageProducts = packageData.packageProducts;
    if (packageData.packageProducts && packageData.productVOList) {
      try {
        const packageProductsArray = JSON.parse(packageData.packageProducts);
        const productVOList = packageData.productVOList;

        // 为每个套餐商品补充名称信息
        const enrichedArray = packageProductsArray.map((packageProduct: any) => {
          const productInfo = productVOList.find((vo: any) => vo.id === packageProduct.id);
          return {
            ...packageProduct,
            name: productInfo?.name || packageProduct.name || `未知商品(${packageProduct.id})`
          };
        });

        enrichedPackageProducts = JSON.stringify(enrichedArray);
        console.log('[addCart] 补充商品名称后的packageProducts:', enrichedArray);
      } catch (error) {
        console.warn('[addCart] 解析packageProducts失败，使用原始数据:', error);
        enrichedPackageProducts = packageData.packageProducts;
      }
    }

    // 保存套餐中所选商品的状态
    let packageDetail: any = {
      packageProducts: enrichedPackageProducts, // 使用补充了名称的数据
      optionalGroups: packageData.optionalGroups,
      productVOList: packageData.productVOList,
      detailString // 添加生成的明细字符串
    };

    // 如果存在packageDetail，并且包含selectedProducts，保留该信息
    if (packageData.packageDetail) {
      try {
        // 处理packageDetail字符串或对象
        if (typeof packageData.packageDetail === 'string') {
          const parsed = JSON.parse(packageData.packageDetail);
          if (parsed.selectedProducts) {
            packageDetail.selectedProducts = parsed.selectedProducts;
          }
        } else if (packageData.packageDetail.selectedProducts) {
          // 如果已经是对象且包含selectedProducts
          packageDetail.selectedProducts = packageData.packageDetail.selectedProducts;
        }
      } catch (e) {
        console.error('解析套餐详情失败:', e);
      }
    }

    return {
      id: packageData.id,
      name: packageData.name,
      currentPrice: packageData.currentPrice,
      quantity: packageData.quantity || 1,
      unit: packageData.unit || '份',
      isPackage: true,
      hasOptionalGroups,
      packageUniqueId: hasOptionalGroups
        ? // 如果有packageConfigId（由PackageDialogPresenter生成），则优先使用它
          packageData.packageConfigId || this.generatePackageUniqueId(packageData)
        : undefined,
      packageDetail: packageDetail
    };
  }

  /**
   * 生成套餐明细字符串
   */
  private static generatePackageDetailString(packageData: any): string {
    try {
      const details: string[] = [];
      console.log('generatePackageDetailString packageData:', packageData);
      // 处理默认商品
      if (packageData.packageProducts) {
        const defaultProducts = JSON.parse(packageData.packageProducts);
        defaultProducts.forEach((product: any) => {
          const productInfo = packageData.productVOList?.find((p: any) => p.id === product.id);
          if (productInfo) {
            details.push(`${productInfo.name}x${product.count}`);
          }
        });
      }

      // 处理可选商品组
      if (packageData.optionalGroups) {
        try {
          const optionalGroupsData = JSON.parse(packageData.optionalGroups);
          // 确保 optionalGroupsData 是数组
          if (Array.isArray(optionalGroupsData)) {
            optionalGroupsData.forEach((group: any) => {
              if (Array.isArray(group.products)) {
                group.products.forEach((product: any) => {
                  if (product.selected_count > 0) {
                    const productInfo = packageData.productVOList?.find((p: any) => p.id === product.id);
                    if (productInfo) {
                      details.push(`${productInfo.name}x${product.selected_count}`);
                    }
                  }
                });
              }
            });
          } else {
            console.warn('optionalGroups 不是数组格式:', optionalGroupsData);
          }
        } catch (error) {
          console.warn('解析 optionalGroups 失败:', error);
        }
      }

      // 如果没有任何明细，返回空字符串
      if (details.length === 0) {
        return '';
      }
      console.log('generatePackageDetailString details:', details);
      return details.join('，');
    } catch (error) {
      console.error('生成套餐明细字符串失败:', error, packageData);
      return '';
    }
  }
}
