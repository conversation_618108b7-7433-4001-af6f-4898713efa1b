import { ProductOrPackageRVO, ProductTypeVO, ProductPackageTypeVO, ProductVO, ProductPackageVO } from '@/api/autoGenerated/shared/types';
import { ProductCategory, ProductItem, CartItem } from './viewmodel';
import { getProductImageUrl } from '@/utils/imageUtils';

/**
 * ProductOrder转换器
 * 负责各种数据格式之间的转换
 */
export class ProductOrderConverter {
  /**
   * 将分转换为元
   * @param priceInFen 以分为单位的价格
   * @returns 以元为单位的价格
   */
  static fenToYuan(priceInFen: number): number {
    return priceInFen / 100;
  }

  /**
   * 将API返回的商品类型转换为前端分类列表
   * @param data API返回的商品类型数据
   * @param selectedCategoryId 当前选中的分类ID
   * @returns 前端分类列表
   */
  static apiCategoriesToViewCategories(
    productTypes: ProductTypeVO[] = [],
    packageTypes: ProductPackageTypeVO[] = [],
    selectedCategoryId: string = ''
  ): ProductCategory[] {
    const categories: ProductCategory[] = [
      // 默认添加一个"全部"分类
      {
        id: 'all',
        name: '全部分类',
        isPackage: false,
        isSelected: selectedCategoryId === 'all' || !selectedCategoryId
      }
    ];

    // 先添加套餐分类
    packageTypes.forEach(type => {
      categories.push({
        id: type.id || '',
        name: type.name || '',
        isPackage: true,
        isSelected: selectedCategoryId === type.id
      });
    });

    // 再添加普通商品分类
    productTypes.forEach(type => {
      categories.push({
        id: type.id || '',
        name: type.name || '',
        isPackage: false,
        isSelected: selectedCategoryId === type.id
      });
    });

    return categories;
  }

  /**
   * 将API返回的商品数据转换为前端商品列表
   * @param data API返回的商品数据
   * @returns 前端商品列表
   */
  static apiProductsToViewProducts(data: ProductOrPackageRVO): ProductItem[] {
    const products: ProductItem[] = [];

    // 处理普通商品
    if (data.productVOs && Array.isArray(data.productVOs)) {
      data.productVOs.forEach(product => {
        // 使用类型断言获取可能存在的促销标签
        const promotionTag = (product as any).promotionTag || '';
        // 获取是否为推广商品
        const isPromotion = Boolean((product as any).isPromotion);

        // 从API返回的数据中获取分类ID
        // 根据用户提供的信息，使用category字段作为分类ID
        const categoryId = (product as any).category || '';

        // 保持价格以分为单位
        const priceInFen = product.currentPrice || 0;

        products.push({
          id: product.id || '',
          name: product.name || '',
          price: priceInFen, // 保持价格以分为单位
          unit: product.unit || '份',
          imageUrl: product.image || '', // 保持原始URL，在UI层处理默认图片
          status: product.isSoldOut ? 'sold_out' : 'normal',
          isPackage: false,
          promotionTag, // 使用后端提供的促销标签
          isPromotion, // 添加是否为推广商品标记
          categoryId, // 保存分类ID，用于本地过滤
          packageDetail: {
            categoryId, // 同样保存在packageDetail中，保持结构一致
            typeId: categoryId
          }
        });
      });
    }

    // 处理套餐商品
    if (data.productPackageVOs && Array.isArray(data.productPackageVOs)) {
      data.productPackageVOs.forEach(pkg => {
        // 使用类型断言获取可能存在的促销标签
        const promotionTag = (pkg as any).promotionTag || '';
        // 获取是否为推广商品
        const isPromotion = Boolean((pkg as any).isPromotion);

        // 从API返回的数据中获取分类ID
        // 根据用户提供的信息，使用category字段作为分类ID
        const categoryId = (pkg as any).category || '';

        // 保持价格以分为单位
        const priceInFen = pkg.currentPrice || 0;

        // 确保套餐的packageDetail包含分类ID信息
        const packageDetail = {
          ...pkg,
          typeId: categoryId, // 使用category作为typeId
          categoryId: categoryId // 保存分类ID
        };

        products.push({
          id: pkg.id || '',
          name: pkg.name || '',
          price: priceInFen, // 保持价格以分为单位
          unit: '份', // 套餐默认使用份作为单位
          imageUrl: pkg.image || '', // 保持原始URL，在UI层处理默认图片
          status: pkg.isOnShelf === false ? 'sold_out' : 'normal', // 使用isOnShelf属性判断是否可售
          isPackage: true,
          packageDetail: packageDetail,
          promotionTag, // 使用后端提供的促销标签
          isPromotion, // 添加是否为推广商品标记
          categoryId // 保存分类ID，用于本地过滤
        });
      });
    }

    return products;
  }

  /**
   * 将产品转换为购物车项
   * @param product 商品
   * @returns 购物车项
   */
  static productToCartItem(product: ProductItem): CartItem {
    return {
      id: product.id,
      name: product.name,
      currentPrice: product.price, // 保持价格以分为单位
      quantity: 1,
      unit: product.unit,
      imageUrl: product.imageUrl || '', // 添加商品图片URL
      isPackage: product.isPackage,
      packageDetail: product.isPackage ? product.packageDetail : undefined,
      packageSelectedItems: '' // 普通商品没有已选项
    };
  }

  /**
   * 将套餐数据转换为购物车项
   * @param packageData 套餐数据
   * @returns 购物车项
   */
  static packageToCartItem(packageData: any): CartItem {
    // 为每个套餐项生成一个唯一ID，用于识别不同的套餐选择组合
    const packageUniqueId = `pkg_${packageData.id}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // 确保有有效的价格
    const price = packageData.price || packageData.currentPrice || 0;

    // 确保有有效的名称
    const name = packageData.name || '未命名套餐';

    // 确保有有效的图片URL
    const imageUrl = packageData.image || packageData.imageUrl || '';

    // 生成套餐已选商品描述
    let packageSelectedItems = '';
    if (packageData.selectedItems && Array.isArray(packageData.selectedItems) && packageData.selectedItems.length > 0) {
      packageSelectedItems = packageData.selectedItems.map((item: any) => `${item.name} x${item.quantity}`).join(' / ');
    } else if (packageData.optionalGroups && Array.isArray(packageData.optionalGroups)) {
      // 尝试从可选组中提取已选商品
      const selectedProducts: string[] = [];
      packageData.optionalGroups.forEach((group: any) => {
        if (group.products && Array.isArray(group.products)) {
          group.products.forEach((product: any) => {
            if (product.quantity > 0) {
              selectedProducts.push(`${product.name} x${product.quantity}`);
            }
          });
        }
      });
      packageSelectedItems = selectedProducts.join(' / ');
    }

    console.log('Package data in converter:', {
      id: packageData.id,
      name: name,
      price: price,
      selectedItems: packageSelectedItems,
      imageUrl: imageUrl
    });

    return {
      id: packageData.id || '',
      name: name,
      currentPrice: price, // 保持价格以分为单位
      quantity: packageData.quantity || 1,
      unit: packageData.unit || '套',
      imageUrl: imageUrl,
      isPackage: true,
      packageUniqueId,
      packageDetail: packageData,
      packageSelectedItems
    };
  }

  /**
   * 计算购物车总金额
   * @param cartItems 购物车项目列表
   * @returns 总金额（分）
   */
  static calculateTotalAmount(cartItems: CartItem[]): number {
    return cartItems.reduce((total, item) => {
      return total + item.currentPrice * item.quantity;
    }, 0);
  }

  /**
   * 检查商品是否有可选组
   * @param product 商品数据
   * @returns 是否有可选组
   */
  static hasOptionalGroups(product: any): boolean {
    // 添加调试日志
    console.log('Checking hasOptionalGroups for product:', product.id, product.name);
    console.log('Package detail:', product.packageDetail);

    if (!product || !product.packageDetail) {
      console.log('No package detail found');
      return false;
    }

    // 检查optionalGroups属性
    if (product.packageDetail.optionalGroups) {
      console.log('Found optionalGroups:', product.packageDetail.optionalGroups);
      return true;
    }

    // 检查optionalGroupsPricePlanUnionVOs属性
    if (product.packageDetail.optionalGroupsPricePlanUnionVOs && product.packageDetail.optionalGroupsPricePlanUnionVOs.length > 0) {
      console.log('Found optionalGroupsPricePlanUnionVOs:', product.packageDetail.optionalGroupsPricePlanUnionVOs.length);
      return true;
    }

    // 检查productVOList属性
    if (product.packageDetail.productVOList && product.packageDetail.productVOList.length > 0) {
      console.log('Found productVOList:', product.packageDetail.productVOList.length);
      return true;
    }

    console.log('No optional groups found');
    return false;
  }
}
