<template>
  <div class="p-[24px]">
    <!-- 顶部导航和操作区 -->
    <div class="flex items-center mb-[24px]">
      <!-- 左侧标签页 -->
      <div class="flex rounded-[10px] h-[68px] w-[224px] px-[8px] items-center bg-[#F3F3F3] p-[8px]">
        <router-link to="/member/index" class="flex-1 h-[52px] w-[100px] font-medium rounded-[6px] text-gray-500 flex items-center justify-center">
          会员管理
        </router-link>
        <router-link
          to="/member/operation-list"
          class="bg-btn-focus flex-1 h-[52px] w-[100px] font-medium text-white rounded-[6px] flex items-center justify-center">
          办理记录
        </router-link>
      </div>

      <!-- 右侧搜索区域 -->
      <div class="flex ml-auto gap-[24px]">
        <el-input
          v-model="viewModel.queryParams.keyword"
          placeholder="搜索卡号/姓名/手机号"
          clearable
          class="option-input"
          @keyup.enter="viewModel.handleSearch">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="viewModel.queryParams.optionType" placeholder="操作类型" clearable class="option-select">
          <el-option v-for="item in viewModel.operationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>

    <!-- 表格区域 -->
    <div v-loading="viewModel.loading.value">
      <el-table :data="viewModel.tableData.value" stripe class="w-full" empty-text="暂无数据">
        <el-table-column prop="cardNumber" label="卡号" min-width="120" align="center" />
        <el-table-column prop="cardLevelName" label="会员等级" min-width="100" align="center" />
        <el-table-column prop="cardName" label="会员姓名" min-width="100" align="center" />
        <el-table-column prop="formatePhone" label="手机号" min-width="120" align="center" />
        <el-table-column prop="optionType" label="操作类型" min-width="100" align="center" />
        <el-table-column prop="sellerName" label="销售员" min-width="100" align="center" />
        <el-table-column prop="amount" label="金额" min-width="100" align="center">
          <template #default="scope">
            <div class="flex items-center justify-center">
              <PriceDisplay v-if="scope.row.amount > 0" :amount-in-fen="scope.row.amount" />
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="receivedAmount" label="收款金额" min-width="100" align="center">
          <template #default="scope">
            <div class="flex items-center justify-center">
              <PriceDisplay v-if="scope.row.receivedAmount > 0" :amount-in-fen="scope.row.receivedAmount" />
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="operationTime" label="办理时间" min-width="160" align="center">
          <template #default="scope">
            <span>{{ formatUnixTimestampToDateSimple(scope.row.operationTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMemberOperationListPresenter } from './presenter';
import { Search } from '@element-plus/icons-vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { formatUnixTimestampToDateSimple } from '@/utils/dateUtils';
// 使用Presenter获取视图模型
const viewModel = useMemberOperationListPresenter();
</script>

<style scoped>
:deep(.option-input) {
  .el-input__wrapper {
    border-radius: 10px;
    height: 60px;
    padding: 0 20px;
    width: 332px;
  }
}

:deep(.option-select) {
  .el-select__wrapper {
    width: 180px;
    border-radius: 10px;
    height: 60px;
    padding: 0 20px;
  }
}
</style>
