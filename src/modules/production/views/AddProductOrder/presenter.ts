import { computed, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useTimeStore } from '@/stores/timeStore';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { OrderApi } from '@/modules/order/api/order';
import type { IAddProductOrderViewModel, ShoppingCartItem, RoomOption } from './viewmodel';
import { AddProductOrderConverter } from './converter';
import { AddProductOrderInteractor } from './interactor';
import { useOpenTableProduct } from '../OpenTableProduct/presenter';
import { OpenTableProductConverter } from '../OpenTableProduct/converter';

import { postApiRoomList } from '@/api/autoGenerated/defaultVersion/room';
import { postApiEmployeeQuery } from '@/api/autoGenerated/defaultVersion/employee';
// 导入v3版本的点单接口

import DialogManager from '@/utils/dialog';
import { OrderData, ExtendedOrderData } from '@/modules/order/components/dialogs/OrderPayDialog/orderPayEntity';
import { V3AddOrderAdditionalReqDto } from '@/api/autoGenerated/shared/types/order';
import { useVenueStore } from '@/stores/venueStore';
/**
 * 商品点单
 */
export class AddProductOrderPresenter implements IAddProductOrderViewModel {
  private _openTablePresenter;
  private _router = useRouter();
  private _timeStore = useTimeStore();
  private _userStore = useUserStore();
  private _deviceStore = useDeviceStore();
  private _venueStore = useVenueStore();
  private _interactor: AddProductOrderInteractor;
  private _state;
  public computed;
  public actions;

  // 添加购物车统一处理方法
  private mergeCartItems(newCartItems: ShoppingCartItem[] = []) {
    console.log('🛒 mergeCartItems 被调用:', {
      newCartItems: newCartItems.map(item => ({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
        cartItemId: item.cartItemId,
        isPackage: item.isPackage,
        packageUniqueId: item.packageUniqueId
      })),
      currentCartItems: this._state.cartItems.map(item => ({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
        cartItemId: item.cartItemId,
        isPackage: item.isPackage,
        packageUniqueId: item.packageUniqueId
      }))
    });

    // 如果没有待合并的新商品，直接使用原购物车数据
    if (newCartItems.length === 0) {
      console.log('⚠️ 没有新商品需要合并');
      return;
    }

    // 克隆当前购物车
    const originalCart = [...this._state.cartItems];
    const mergedItems: ShoppingCartItem[] = [...originalCart];

    // 处理新商品
    newCartItems.forEach(newItem => {
      console.log('🔍 处理新商品:', {
        id: newItem.id,
        name: newItem.name,
        quantity: newItem.quantity,
        cartItemId: newItem.cartItemId,
        isPackage: newItem.isPackage,
        packageUniqueId: newItem.packageUniqueId
      });

      // 检查是否可以合并
      const existingItemIndex = mergedItems.findIndex(item => {
        let canMerge = false;

        // 普通商品：匹配ID
        if (!newItem.isPackage) {
          canMerge = item.id === newItem.id && !item.isPackage;
        }
        // 套餐商品：需要更严格的匹配规则
        else if (newItem.isPackage && item.isPackage) {
          // 基本条件：商品ID必须相同
          if (item.id !== newItem.id) {
            canMerge = false;
          }
          // 如果两个套餐都有packageUniqueId，必须完全相同
          else if (newItem.packageUniqueId && item.packageUniqueId) {
            canMerge = item.packageUniqueId === newItem.packageUniqueId;
          }
          // 如果有一个有packageUniqueId而另一个没有，不能合并
          else if (newItem.packageUniqueId || item.packageUniqueId) {
            canMerge = false;
          }
          // 如果都没有packageUniqueId，比较套餐详情
          else {
            canMerge = this.comparePackageDetails(item, newItem);
          }
        }

        console.log('🔍 检查是否可合并:', {
          existingItem: {
            id: item.id,
            name: item.name,
            cartItemId: item.cartItemId,
            isPackage: item.isPackage,
            packageUniqueId: item.packageUniqueId
          },
          newItem: {
            id: newItem.id,
            name: newItem.name,
            cartItemId: newItem.cartItemId,
            isPackage: newItem.isPackage,
            packageUniqueId: newItem.packageUniqueId
          },
          canMerge
        });

        return canMerge;
      });

      // 找到可合并项
      if (existingItemIndex >= 0) {
        console.log('✅ 找到可合并项目，更新数量:', {
          existingItem: mergedItems[existingItemIndex].name,
          originalQuantity: mergedItems[existingItemIndex].quantity,
          addQuantity: newItem.quantity,
          newQuantity: mergedItems[existingItemIndex].quantity + newItem.quantity
        });

        // 更新数量
        mergedItems[existingItemIndex].quantity += newItem.quantity;
      } else {
        console.log('🆕 添加新项目到购物车');

        // 添加新项，并生成唯一的cartItemId
        if (!newItem.cartItemId) {
          newItem.cartItemId = `${newItem.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          console.log('🔑 为新项目生成cartItemId:', newItem.cartItemId);
        }

        // 如果是套餐且没有packageUniqueId，生成一个
        if (newItem.isPackage && !newItem.packageUniqueId) {
          newItem.packageUniqueId = this.generatePackageUniqueId(newItem);
          console.log('🎁 为套餐生成packageUniqueId:', newItem.packageUniqueId);
        }

        mergedItems.push(newItem);
      }
    });

    // 确保所有现有项目都有cartItemId和packageUniqueId（如果是套餐）
    mergedItems.forEach(item => {
      if (!item.cartItemId) {
        item.cartItemId = `${item.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        console.log('🔑 为现有项目生成cartItemId:', {
          item: item.name,
          cartItemId: item.cartItemId
        });
      }

      // 为套餐生成packageUniqueId
      if (item.isPackage && !item.packageUniqueId) {
        item.packageUniqueId = this.generatePackageUniqueId(item);
        console.log('🎁 为现有套餐生成packageUniqueId:', {
          item: item.name,
          packageUniqueId: item.packageUniqueId
        });
      }
    });

    console.log('✅ 购物车合并完成:', {
      originalCount: originalCart.length,
      newCount: mergedItems.length,
      finalCartItems: mergedItems.map(item => ({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
        cartItemId: item.cartItemId,
        isPackage: item.isPackage,
        packageUniqueId: item.packageUniqueId
      }))
    });

    // 更新购物车
    this._state.cartItems = mergedItems;
  }

  constructor() {
    const self = this;
    this._openTablePresenter = useOpenTableProduct();
    this._interactor = new AddProductOrderInteractor();

    this._state = reactive({
      selectedRoom: null,
      selectedArea: '',
      currentSessionId: '',
      cartItems: [] as ShoppingCartItem[],
      selectedSeller: this._userStore.userInfo?.employee?.id || '',
      showPrinter: false,
      printData: {},
      isChargeToRoom: true,
      showRoomSelector: false,
      packageDialogVisible: false,
      currentPackage: null as any,
      roomOptions: [] as RoomOption[],
      loadingRooms: false,
      employeeOptions: [] as { value: string; label: string }[],
      loadingEmployees: false,
      totalAmount: 0,
      loading: false
    });

    this.initializeFromRoute();
    this.loadRooms();
    this.loadEmployees();

    this.computed = {
      isAdditionalOrder: computed(() => {
        return !!this._state.currentSessionId;
      }),

      totalAmountInFen: computed(() => {
        const total = this._state.cartItems.reduce((total, item) => total + item.currentPrice * item.quantity, 0);
        return total;
      }),

      totalItems: computed(() => {
        return this._state.cartItems.reduce((total, item) => total + item.quantity, 0);
      })
    };

    this.actions = {
      loadRooms: this.loadRooms.bind(this),
      loadEmployees: this.loadEmployees.bind(this),
      addToCart: (product: any) => {
        if (product.isPackage) {
          if (OpenTableProductConverter.hasOptionalGroups(product)) {
            this._state.currentPackage = product;
            this._state.packageDialogVisible = true;
            return;
          }

          const cartItem = OpenTableProductConverter.convertPackageToCartItem(product);
          this.mergeCartItems([cartItem]);
        } else {
          const cartItem = OpenTableProductConverter.convertProductToCartItem(product);
          this.mergeCartItems([cartItem]);
        }
      },
      handlePackageConfirm: (packageData: any) => {
        const cartItem = OpenTableProductConverter.convertPackageToCartItem(packageData);

        if (this._state.currentPackage?.isEditing) {
          const originalItem = this._state.cartItems.find(
            item => item.isPackage && item.id === cartItem.id && this._state.currentPackage.packageUniqueId === item.packageUniqueId
          );

          if (originalItem) {
            cartItem.quantity = originalItem.quantity;
            this._state.cartItems = this._state.cartItems.filter(item => item !== originalItem);
          }
        }

        this.mergeCartItems([cartItem]);

        this._state.packageDialogVisible = false;
        this._state.currentPackage = null;
      },
      closePackageDialog: () => {
        this._state.packageDialogVisible = false;
        this._state.currentPackage = null;
      },
      editPackage: (item: ShoppingCartItem) => {
        if (!item.isPackage) return;

        const packageToEdit = JSON.parse(JSON.stringify(item)) as ShoppingCartItem;

        packageToEdit.isEditing = true;

        if (packageToEdit.packageDetail) {
          try {
            const packageDetail = JSON.parse(packageToEdit.packageDetail);
            console.log('Parsed package detail:', packageDetail);

            if (packageDetail && packageDetail.selectedProducts) {
              console.log('Found selectedProducts in package detail');
            }
          } catch (e) {
            console.error('Error parsing package detail:', e);
          }
        }

        this._state.currentPackage = packageToEdit;
        this._state.packageDialogVisible = true;
      },
      increaseQuantity: (item: ShoppingCartItem) => {
        console.log('⬆️ increaseQuantity 被调用:', {
          item: { id: item.id, name: item.name, cartItemId: item.cartItemId }
        });

        // 使用cartItemId进行精确匹配
        const itemKey = this.generateCartItemKey(item);
        const cartItem = this._state.cartItems.find(cartItem => {
          const cartItemKey = this.generateCartItemKey(cartItem);
          return itemKey === cartItemKey;
        });

        if (cartItem) {
          console.log('✅ increaseQuantity 找到匹配项目，数量从', cartItem.quantity, '增加到', cartItem.quantity + 1);
          cartItem.quantity++;
        } else {
          console.log('❌ increaseQuantity 未找到匹配项目');
        }
      },
      decreaseQuantity: (item: ShoppingCartItem) => {
        console.log('⬇️ decreaseQuantity 被调用:', {
          item: { id: item.id, name: item.name, cartItemId: item.cartItemId }
        });

        // 使用cartItemId进行精确匹配
        const itemKey = this.generateCartItemKey(item);
        const cartItem = this._state.cartItems.find(cartItem => {
          const cartItemKey = this.generateCartItemKey(cartItem);
          return itemKey === cartItemKey;
        });

        if (cartItem) {
          if (cartItem.quantity > 1) {
            console.log('✅ decreaseQuantity 找到匹配项目，数量从', cartItem.quantity, '减少到', cartItem.quantity - 1);
            cartItem.quantity--;
          } else {
            console.log('🗑️ decreaseQuantity 找到匹配项目，数量为1，删除商品');
            this._state.cartItems = this._state.cartItems.filter(cartItem => {
              const cartItemKey = this.generateCartItemKey(cartItem);
              return itemKey !== cartItemKey;
            });
          }
        } else {
          console.log('❌ decreaseQuantity 未找到匹配项目');
        }
      },
      updateQuantity: (item: ShoppingCartItem, value: number | undefined) => {
        console.log('🛒 updateQuantity 被调用:', {
          item: {
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            cartItemId: item.cartItemId
          },
          value,
          currentCartItems: this._state.cartItems.map(i => ({
            id: i.id,
            name: i.name,
            quantity: i.quantity,
            cartItemId: i.cartItemId
          }))
        });

        if (!value || value < 1) {
          console.log('🗑️ 准备删除商品');

          // 生成要删除项目的唯一标识符
          const itemKey = this.generateCartItemKey(item);
          console.log('🔑 要删除的商品标识符:', itemKey);

          // 使用更精确的匹配条件来删除商品，避免误删
          const originalLength = this._state.cartItems.length;
          this._state.cartItems = this._state.cartItems.filter(cartItem => {
            const cartItemKey = this.generateCartItemKey(cartItem);
            const shouldKeep = itemKey !== cartItemKey;

            console.log('🔍 检查购物车项目:', {
              cartItem: {
                id: cartItem.id,
                name: cartItem.name,
                quantity: cartItem.quantity,
                cartItemId: cartItem.cartItemId
              },
              cartItemKey,
              shouldKeep,
              match: itemKey === cartItemKey
            });

            return shouldKeep;
          });

          console.log('✅ 删除完成:', {
            原始数量: originalLength,
            删除后数量: this._state.cartItems.length,
            剩余商品: this._state.cartItems.map(i => ({
              id: i.id,
              name: i.name,
              quantity: i.quantity,
              cartItemId: i.cartItemId
            }))
          });
        } else {
          console.log('📝 准备更新商品数量');

          // 使用更精确的匹配条件来查找商品
          const itemKey = this.generateCartItemKey(item);
          console.log('🔑 要更新的商品标识符:', itemKey);

          const cartItem = this._state.cartItems.find(cartItem => {
            const cartItemKey = this.generateCartItemKey(cartItem);
            const isMatch = itemKey === cartItemKey;

            console.log('🔍 检查匹配:', {
              cartItem: {
                id: cartItem.id,
                name: cartItem.name,
                quantity: cartItem.quantity,
                cartItemId: cartItem.cartItemId
              },
              cartItemKey,
              isMatch
            });

            return isMatch;
          });

          if (cartItem) {
            console.log('✅ 找到匹配项目，更新数量:', {
              before: cartItem.quantity,
              after: value
            });
            cartItem.quantity = value;
          } else {
            console.log('❌ 未找到匹配项目');
          }
        }
      },
      handleQuantityChange: (item: ShoppingCartItem, currentValue: number | undefined, oldValue: number | undefined) => {
        console.log('🔄 handleQuantityChange 被调用:', {
          item: {
            id: item.id,
            name: item.name,
            cartItemId: item.cartItemId,
            currentQuantity: item.quantity
          },
          currentValue,
          oldValue
        });

        // 当数量变为0或undefined时，直接删除商品
        if (!currentValue || currentValue < 1) {
          console.log('🗑️ 数量为0，直接删除商品');

          // 生成要删除项目的唯一标识符
          const itemKey = this.generateCartItemKey(item);
          console.log('🔑 要删除的商品标识符:', itemKey);

          // 直接从购物车中删除商品，避免数量为0的状态
          const originalLength = this._state.cartItems.length;
          this._state.cartItems = this._state.cartItems.filter(cartItem => {
            const cartItemKey = this.generateCartItemKey(cartItem);
            const shouldKeep = itemKey !== cartItemKey;

            console.log('🔍 检查购物车项目 (直接删除):', {
              cartItem: {
                id: cartItem.id,
                name: cartItem.name,
                quantity: cartItem.quantity,
                cartItemId: cartItem.cartItemId
              },
              cartItemKey,
              shouldKeep,
              match: itemKey === cartItemKey
            });

            return shouldKeep;
          });

          console.log('✅ 商品直接删除完成:', {
            原始数量: originalLength,
            删除后数量: this._state.cartItems.length,
            剩余商品: this._state.cartItems.map(i => ({
              id: i.id,
              name: i.name,
              quantity: i.quantity,
              cartItemId: i.cartItemId
            }))
          });
        } else {
          console.log('📝 更新商品数量到:', currentValue);
          this.actions.updateQuantity(item, currentValue);
        }
      },
      clearCart: () => {
        this._state.cartItems = [];
      },
      handleRoomSelect: (room: any) => {
        if (room) {
          this._state.selectedRoom = room;
          this._state.currentSessionId = room.sessionId;
        }
      },
      clearRoomSelection: () => {
        this._state.selectedRoom = null;
        this._state.currentSessionId = '';
      },
      handleConfirm: async () => {
        if (!this._state.currentSessionId) {
          ElMessage.warning('请先选择送达包厢');
          return;
        }

        if (this._state.cartItems.length === 0) {
          ElMessage.warning('请先选择商品');
          return;
        }

        const selectedRoomOption = this._state.roomOptions.find(room => room.value === this._state.currentSessionId);
        if (!selectedRoomOption) {
          ElMessage.warning('未找到房间信息，请重新选择包厢');
          return;
        }

        const selectedRoom = selectedRoomOption.roomVO;

        try {
          this._state.loading = true;

          const orderProducts = AddProductOrderConverter.cartItemsToOrderProducts(this._state.cartItems);
          const { totalPayAmount, totalOriginalAmount } = AddProductOrderConverter.calculateTotalAmounts(this._state.cartItems);

          const now = Date.now();

          const orderData: ExtendedOrderData = {
            roomName: selectedRoom.name || '',
            roomAmount: 0,
            productAmount: totalPayAmount,
            startTime: now,
            endTime: now,
            duration: 0,
            roomFee: 0,
            supermarketFee: totalPayAmount,
            totalAmount: totalPayAmount,
            unpaidAmount: totalPayAmount,
            actualAmount: totalPayAmount,
            changeAmount: 0,
            lowConsumptionDiffAmount: 0,
            minConsumptionAmount: 0,
            inOrderProductInfos: [],
            outOrderProductInfos: [],
            productInfos: this._state.cartItems.map(item => {
              const productInfo = {
                productName: item.name,
                quantity: item.quantity,
                price: item.currentPrice,
                payPrice: item.currentPrice,
                orderNo: '',
                originalPrice: item.currentPrice,
                originalAmount: item.currentPrice * item.quantity,
                payAmount: item.currentPrice * item.quantity,
                id: item.id,
                productId: item.isPackage ? '' : item.id,
                packageId: item.isPackage ? item.id : '',
                unit: item.unit || '份',
                status: 'unpaid',
                isPackage: item.isPackage,
                packageProductInfo: item.isPackage ? AddProductOrderConverter.formatPackageProductInfoForDisplay(item) : null
              };

              return productInfo;
            }) as any,
            roomId: selectedRoom.id || '',
            orderNos: [],
            unpaidOrderNos: [],
            sessionVO: {
              id: this._state.currentSessionId,
              status: 'in_use'
            },
            venueId: selectedRoom.venueId || '',
            employeeId: this._state.selectedSeller,
            orderProductVOs: orderProducts,
            originalOrderAmount: totalOriginalAmount,
            orderRoomPlanVOS: [],
            bookingId: ''
          };

          // 打开支付弹窗
          const result = await DialogManager.open(
            'OrderPayDialog',
            {
              orderData: orderData,
              payType: 'productOrderInstant',
              sessionId: this._state.currentSessionId
            },
            {
              paySuccess: async (payResult: any) => {
                this._state.cartItems = [];
                ElMessage.success('支付成功');
              },
              payCancel: () => {
                ElMessage.info('已取消支付');
                this._state.loading = false;
              }
            }
          );
        } catch (error) {
          console.error('确认下单失败:', error);
          ElMessage.error('操作失败，请稍后重试');
        } finally {
          this._state.loading = false;
        }
      },
      handlePrintSuccess: () => {
        ElMessage.success('打印成功');
        this._router.replace('/room/table');
      },
      handlePrintFailed: () => {
        ElMessage.warning('打印失败，请重新打印');
      },
      addNote: () => {
        ElMessage.info('备注功能暂未实现');
        // TODO: 实现备注功能
      },
      // 新增挂单功能
      handlePending: async () => {
        // 验证必填项
        if (!this._state.currentSessionId) {
          ElMessage.warning('请先选择送达包厢');
          return;
        }

        if (this._state.cartItems.length === 0) {
          ElMessage.warning('请先选择商品');
          return;
        }

        // 获取当前选中的房间信息
        const selectedRoomOption = this._state.roomOptions.find(room => room.value === this._state.currentSessionId);
        if (!selectedRoomOption) {
          ElMessage.warning('未找到房间信息，请重新选择包厢');
          return;
        }

        const selectedRoom = selectedRoomOption.roomVO;

        try {
          // 设置loading状态
          this._state.loading = true;

          // 转换订单商品
          const orderProducts = AddProductOrderConverter.cartItemsToOrderProducts(this._state.cartItems);
          const { totalPayAmount, totalOriginalAmount } = AddProductOrderConverter.calculateTotalAmounts(this._state.cartItems);

          // 构建订单参数
          const orderParams: V3AddOrderAdditionalReqDto = {
            sessionId: this._state.currentSessionId,
            venueId: selectedRoom.venueId || '',
            roomId: selectedRoom.id || '',
            employeeId: this._state.selectedSeller, // 使用选中的员工ID
            payAmount: totalPayAmount,
            originalAmount: totalOriginalAmount,
            orderProductVOs: orderProducts.map(product => {
              if (product.packageProductInfo && typeof product.packageProductInfo !== 'string') {
                return {
                  ...product,
                  packageProductInfo: JSON.stringify(product.packageProductInfo)
                };
              }
              return product;
            })
          };

          console.log('后付订单参数:', orderParams);

          // 点单后付 - 使用v3接口
          const response = await OrderApi.additionalOrderV3(orderParams);

          if (response && response.code === 0) {
            ElMessage.success('挂单成功');

            // 挂单成功后打印出品单
            try {
              console.log('挂单成功，准备打印出品单: ', response.data);
              // 从响应中获取订单号，构建数组
              const orderNos = response.data.orderNos;
              if (orderNos && orderNos.length > 0) {
                console.log('准备打印出品单，订单号:', orderNos, '会话ID:', this._state.currentSessionId);
                // 调用 interactor 的打印方法
                this._interactor.printProductionOrder(orderNos, this._state.currentSessionId);
              } else {
                console.log('没有订单号，跳过出品单打印');
              }
            } catch (printError) {
              console.error('打印出品单失败:', printError);
              // 打印失败不影响挂单流程
            }

            // 清空购物车
            this._state.cartItems = [];
            // 跳转到包厢首页
            this._router.replace('/room');
          } else {
            const errorMsg = typeof response === 'object' && 'message' in response ? String(response.message) : '挂单失败';
            ElMessage.error(errorMsg);
          }
        } catch (error) {
          console.error('挂单失败:', error);
          ElMessage.error('操作失败，请稍后重试');
        } finally {
          this._state.loading = false;
        }
      }
    };
  }

  public get state() {
    return this._state;
  }

  // 添加加载房间列表的方法
  private async loadRooms() {
    try {
      this._state.loadingRooms = true;
      // 调用API获取正在使用的房间列表
      const response = await postApiRoomList({
        status: 'in_use', // 状态为in_use表示正在使用的房间
        pageNum: 1,
        pageSize: 100
      });

      if (response.code === 0 && response.data?.data) {
        const inUseRooms = response.data.data;

        // 过滤掉locked状态的房间，只保留in_use且非locked的有效房间
        const validRooms = inUseRooms.filter(room => {
          // 解析tags并检查是否包含locked标签
          const tags = this.parseRoomTags(room.tag);
          return !tags.includes('locked');
        });

        // 将房间数据转换为选项格式
        this._state.roomOptions = validRooms.map(room => {
          return {
            value: room.sessionId || '',
            label: `${room.name || ''}`,
            roomVO: room
          };
        });

        // 如果当前已选择了包厢，但不在列表中，清空选择
        if (this._state.currentSessionId) {
          const exists = this._state.roomOptions.some(option => option.value === this._state.currentSessionId);
          if (!exists) {
            this._state.currentSessionId = '';
            this._state.selectedRoom = null; // 使用null而不是undefined
          }
        }
      } else {
        console.error('加载房间列表失败', '未能获取房间数据');
      }
    } catch (error) {
      console.error('加载房间列表出错', error);
    } finally {
      this._state.loadingRooms = false;
    }
  }

  /**
   * 解析房间标签字符串为数组
   * @param tag 房间标签字符串
   * @returns 标签数组
   */
  private parseRoomTags(tag: any): string[] {
    if (!tag) return [];

    try {
      if (typeof tag === 'string') {
        // 标准格式是JSON数组字符串，例如: ["tag1","locked"]
        if (tag.startsWith('[') && tag.endsWith(']')) {
          return JSON.parse(tag);
        }
        // 其他可能的格式会作为单个标签处理
        return [tag];
      } else if (Array.isArray(tag)) {
        return tag;
      }
    } catch (e) {
      console.warn('解析房间标签失败:', e);
    }

    return [];
  }

  private initializeFromRoute() {
    const route = useRoute();
    const { roomId, roomName, areaId, areaName, sessionId } = route.query;

    if (sessionId) {
      this._state.currentSessionId = sessionId as string;
    }

    if (roomId && roomName) {
      // 创建一个符合RoomVO类型的对象
      const roomData: any = {
        id: roomId as string,
        name: roomName as string
      };

      // 如果有区域信息，添加到对象中
      if (areaId && areaName) {
        roomData.areaVO = {
          id: areaId as string,
          name: areaName as string
        };
      }

      this._state.selectedRoom = roomData;

      if (areaId) {
        this._state.selectedArea = areaId as string;
      }
    }
  }

  /**
   * 加载员工列表
   */
  private async loadEmployees() {
    this._state.loadingEmployees = true;
    try {
      // 确保venues是对象，并且有id值
      const venueId = this._venueStore.venueId || '';

      if (!venueId) {
        console.error('未找到门店ID，无法加载员工列表');
        this._state.loadingEmployees = false;
        return;
      }

      const response = await postApiEmployeeQuery({
        venueId: venueId,
        // 确保员工处于正常状态
        reviewStatus: 1
      });

      // 处理不同结构的API响应
      let employeeList: any[] = [];

      // 根据控制台截图，response是一个对象，包含data数组
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          // response本身是数组
          employeeList = response;
        } else {
          // response是对象，尝试读取data属性
          const respObj = response as any;
          if (respObj.data && Array.isArray(respObj.data)) {
            // response.data是数组
            employeeList = respObj.data;
          } else if (respObj.data && respObj.data.data && Array.isArray(respObj.data.data)) {
            // response.data.data是数组
            employeeList = respObj.data.data;
          } else if (respObj.code === 0 && Array.isArray(respObj.data)) {
            // response是标准格式{code:0, data:Array}
            employeeList = respObj.data;
          }
        }
      }

      // 转换员工数据为选择器选项格式
      if (employeeList && employeeList.length > 0) {
        // 重要：对于select组件，label是显示的文本，value是实际值
        this._state.employeeOptions = employeeList.map((employee: any) => ({
          value: employee.id || '',
          label: employee.name || '未命名员工'
        }));

        // 如果当前没有选中员工，或选中的员工不在列表中，设置第一个员工为默认选择
        const currentSellerExists = this._state.selectedSeller && this._state.employeeOptions.some(e => e.value === this._state.selectedSeller);

        if (!currentSellerExists && this._state.employeeOptions.length > 0) {
          this._state.selectedSeller = this._state.employeeOptions[0].value;
        }
      } else {
        this._state.employeeOptions = [];
      }
    } catch (error) {
      console.error('加载员工列表失败:', error);
      ElMessage.error('加载员工列表失败');
    } finally {
      this._state.loadingEmployees = false;
    }
  }

  private getEmployeeNameById(id: string): string {
    const employee = this._state.employeeOptions.find(e => e.value === id);
    return employee ? employee.label : '未知员工';
  }

  private formatPackageProductInfo(item: ShoppingCartItem): any[] {
    // 使用converter中的方法确保数据完整性和一致性
    return AddProductOrderConverter.formatPackageProductInfoForDisplay(item);
  }

  // 比较两个套餐的详情是否相同
  private comparePackageDetails(item1: ShoppingCartItem, item2: ShoppingCartItem): boolean {
    // 如果都不是套餐，直接返回false
    if (!item1.isPackage || !item2.isPackage) {
      return false;
    }

    // 获取套餐详情
    const getPackageDetailString = (item: ShoppingCartItem): string => {
      if (!item.packageDetail) return '';

      try {
        if (typeof item.packageDetail === 'string') {
          return item.packageDetail;
        } else if (typeof item.packageDetail === 'object') {
          // 标准化对象格式，只比较关键字段
          const detail = item.packageDetail;
          const key = JSON.stringify({
            packageProducts: detail.packageProducts,
            optionalGroups: detail.optionalGroups,
            selectedProducts: detail.selectedProducts,
            configFingerprint: detail.configFingerprint
          });
          return key;
        }
      } catch (e) {
        console.warn('解析套餐详情失败:', e);
      }

      return '';
    };

    const detail1 = getPackageDetailString(item1);
    const detail2 = getPackageDetailString(item2);

    console.log('🔍 比较套餐详情:', {
      item1: { id: item1.id, name: item1.name, detail: detail1 },
      item2: { id: item2.id, name: item2.name, detail: detail2 },
      areEqual: detail1 === detail2
    });

    // 比较详情字符串
    return detail1 === detail2 && detail1 !== '';
  }

  // 为套餐生成唯一标识符
  private generatePackageUniqueId(item: ShoppingCartItem): string {
    if (!item.isPackage) {
      return item.id;
    }

    // 基于套餐详情生成唯一标识符
    const baseId = item.id;
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);

    // 如果有套餐详情，基于详情内容生成更具体的标识符
    if (item.packageDetail) {
      try {
        let detailKey = '';

        if (typeof item.packageDetail === 'string') {
          detailKey = item.packageDetail;
        } else if (typeof item.packageDetail === 'object') {
          // 提取关键配置信息
          const detail = item.packageDetail;
          const configData = {
            packageProducts: detail.packageProducts,
            optionalGroups: detail.optionalGroups,
            selectedProducts: detail.selectedProducts,
            configFingerprint: detail.configFingerprint
          };
          detailKey = JSON.stringify(configData);
        }

        // 生成基于内容的哈希（简化版）
        let hash = 0;
        for (let i = 0; i < detailKey.length; i++) {
          const char = detailKey.charCodeAt(i);
          hash = (hash << 5) - hash + char;
          hash = hash & hash; // 转换为32位整数
        }

        return `${baseId}_cfg${Math.abs(hash)}_${timestamp}_${random}`;
      } catch (e) {
        console.warn('生成套餐配置标识符失败:', e);
      }
    }

    // 默认生成基于时间戳和随机数的唯一标识符
    return `${baseId}_${timestamp}_${random}`;
  }

  // 生成购物车项目的唯一标识符
  private generateCartItemKey(item: ShoppingCartItem): string {
    console.log('🔑 generateCartItemKey 被调用:', {
      item: {
        id: item.id,
        name: item.name,
        cartItemId: item.cartItemId,
        isPackage: item.isPackage,
        packageUniqueId: item.packageUniqueId
      }
    });

    // 如果已经有cartItemId，直接返回
    if (item.cartItemId) {
      console.log('✅ 使用已存在的cartItemId:', item.cartItemId);
      return item.cartItemId;
    }

    // 如果没有cartItemId，说明这是一个老的购物车项目，需要生成一个
    // 这种情况通常发生在页面刷新或者从其他地方加载的数据
    const uniqueId = `${item.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    console.log('🆕 生成新的cartItemId:', uniqueId);

    // 将生成的ID存储到item中，确保后续调用返回相同的ID
    item.cartItemId = uniqueId;

    return uniqueId;
  }
}

export function useAddProductOrder(): IAddProductOrderViewModel {
  return new AddProductOrderPresenter();
}
