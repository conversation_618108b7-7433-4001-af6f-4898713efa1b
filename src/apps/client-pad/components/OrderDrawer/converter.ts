import { OrderItem, OrderProductItem } from './viewmodel';
import { formatUnixTimestampToSimple } from '@/utils/dateUtils';

// 产品信息接口
interface ProductVO {
  id: string;
  name: string;
  image: string;
  // 其他产品字段...
}

/**
 * 订单转换器类
 * 用于处理订单数据的转换
 */
export class OrderDrawerConverter {
  /**
   * 将API返回的订单数据转换为UI显示模型
   * @param apiOrders API返回的订单数据
   * @param inOrderProducts 套餐内商品数据
   * @param outOrderProducts 套餐外商品数据
   * @returns UI显示模型订单列表
   */
  public static toOrderItems(apiOrders: any[], inOrderProducts: any[], outOrderProducts: any[]): OrderItem[] {
    if (!apiOrders || apiOrders.length === 0) {
      return [];
    }

    // 创建退款映射
    const refundOrdersMap = this.createRefundMap(apiOrders);
    // 创建商品退款映射
    const productRefundMap = this.createProductRefundMap(apiOrders, inOrderProducts, outOrderProducts);

    return apiOrders
      .filter(order => order.direction === 'normal') // 只处理普通订单，不处理退款订单
      .map((order: any) => {
        // 获取该订单关联的商品
        const orderProducts = this.getOrderProducts(order.orderNo, inOrderProducts, outOrderProducts, productRefundMap);

        // 检查订单是否有部分退款
        const isPartialRefund = this.checkPartialRefund(order.orderNo, refundOrdersMap);

        // 计算订单总金额
        const totalAmount = orderProducts.reduce((sum, product) => sum + product.price * product.quantity, 0);

        // 转换为OrderItem
        return {
          id: order.id || order.orderNo,
          orderNo: order.orderNo,
          status: order.status || 'unpaid',
          statusText: order.status === 'paid' ? '已结' : '未结',
          time: formatUnixTimestampToSimple(order.ctime || 0),
          timestamp: order.ctime || 0,
          products: orderProducts,
          totalAmount,
          isPartialRefund
        };
      });
  }

  /**
   * 使用产品信息更新订单商品的图片URL
   * @param orderItems 订单项
   * @param productInfos 产品信息
   * @returns 更新后的订单项
   */
  public static updateProductImages(orderItems: OrderItem[], productInfos: ProductVO[]): OrderItem[] {
    if (!orderItems || orderItems.length === 0 || !productInfos || productInfos.length === 0) {
      return orderItems;
    }

    // 创建产品ID到产品信息的映射
    const productMap = new Map<string, ProductVO>();
    for (const product of productInfos) {
      if (product.id) {
        productMap.set(product.id, product);
      }
    }

    // 更新每个订单的产品图片
    return orderItems.map(order => {
      // 深拷贝订单对象
      const updatedOrder = { ...order };

      // 更新产品图片
      updatedOrder.products = order.products.map(product => {
        const productInfo = productMap.get(product.id);

        if (productInfo && productInfo.image) {
          // 返回带有更新图片的商品
          return {
            ...product,
            imageUrl: productInfo.image
          };
        }

        // 如果没有找到对应的产品信息，返回原商品
        return product;
      });

      return updatedOrder;
    });
  }

  /**
   * 从订单项中提取所有产品ID
   * @param orderItems 订单项
   * @returns 产品ID数组
   */
  public static extractProductIds(orderItems: OrderItem[]): string[] {
    if (!orderItems || orderItems.length === 0) {
      return [];
    }

    const productIds = new Set<string>();

    // 从所有订单中收集产品ID
    for (const order of orderItems) {
      for (const product of order.products) {
        if (product.id) {
          productIds.add(product.id);
        }
      }
    }

    return Array.from(productIds);
  }

  /**
   * 创建商品退款映射
   * @param orders 订单列表
   * @param inOrderProducts 套餐内商品列表
   * @param outOrderProducts 套餐外商品列表
   * @returns 商品退款映射 Map<原始订单号, Map<商品ID, {退款数量, 退款金额}>>
   */
  private static createProductRefundMap(
    orders: any[],
    inOrderProducts: any[],
    outOrderProducts: any[]
  ): Map<string, Map<string, { refundQuantity: number; refundAmount: number }>> {
    const productRefundMap = new Map<string, Map<string, { refundQuantity: number; refundAmount: number }>>();

    // 找出所有退款订单
    const refundOrders = orders.filter(order => order.direction === 'refund' && order.pOrderNo);

    // 处理每个退款订单
    for (const refundOrder of refundOrders) {
      const originalOrderNo = refundOrder.pOrderNo;
      if (!originalOrderNo) continue;

      // 获取退款订单关联的商品
      const inRefundProducts = inOrderProducts.filter(product => product.orderNo === refundOrder.orderNo);
      const outRefundProducts = outOrderProducts.filter(product => product.orderNo === refundOrder.orderNo);
      const refundProducts = [...inRefundProducts, ...outRefundProducts];

      // 为每个原始订单创建或获取商品退款映射
      if (!productRefundMap.has(originalOrderNo)) {
        productRefundMap.set(originalOrderNo, new Map());
      }

      // 更新各商品的退款信息
      const orderProductRefundMap = productRefundMap.get(originalOrderNo)!;

      // 处理每个退款商品
      for (const product of refundProducts) {
        const productId = product.productId || '';

        if (!productId) continue;

        // 获取或初始化该商品的退款信息
        const currentRefund = orderProductRefundMap.get(productId) || { refundQuantity: 0, refundAmount: 0 };

        // 累加退款数量和金额
        currentRefund.refundQuantity += product.quantity || 0;
        currentRefund.refundAmount += product.payAmount || 0;

        // 更新退款映射
        orderProductRefundMap.set(productId, currentRefund);
      }
    }

    return productRefundMap;
  }

  /**
   * 获取订单关联的所有商品（套餐内+套餐外）
   * @param orderNo 订单号
   * @param inOrderProducts 套餐内商品列表
   * @param outOrderProducts 套餐外商品列表
   * @param productRefundMap 商品退款映射
   * @returns 订单商品列表
   */
  private static getOrderProducts(
    orderNo: string,
    inOrderProducts: any[],
    outOrderProducts: any[],
    productRefundMap?: Map<string, Map<string, { refundQuantity: number; refundAmount: number }>>
  ): OrderProductItem[] {
    // 根据订单号获取套餐内商品和套餐外商品
    const inProducts = inOrderProducts.filter(product => product.orderNo === orderNo);
    const outProducts = outOrderProducts.filter(product => product.orderNo === orderNo);

    // 获取该订单的商品退款映射
    const orderProductRefundMap = productRefundMap?.get(orderNo) || new Map();

    // 合并并转换商品
    return [...this.convertProducts(inProducts, true, orderProductRefundMap), ...this.convertProducts(outProducts, false, orderProductRefundMap)];
  }

  /**
   * 转换商品数据为UI显示模型
   * @param products 商品数据
   * @param isInPackage 是否为套餐内商品
   * @param productRefundMap 商品退款映射
   * @returns UI显示模型商品列表
   */
  private static convertProducts(
    products: any[],
    isInPackage: boolean,
    productRefundMap: Map<string, { refundQuantity: number; refundAmount: number }>
  ): OrderProductItem[] {
    if (!products || products.length === 0) return [];

    return products.map(product => {
      const productId = product.productId || '';
      const originalQuantity = product.quantity || 0;

      // 获取该商品的退款信息
      const refundInfo = productRefundMap.get(productId);
      const refundQuantity = refundInfo?.refundQuantity || 0;
      const refundAmount = refundInfo?.refundAmount || 0;

      // 计算剩余数量
      const remainingQuantity = Math.max(0, originalQuantity - refundQuantity);

      // 判断是否有退款，是否全退
      const isRefunded = refundQuantity > 0;
      const isFullyRefunded = isRefunded && remainingQuantity === 0;

      // 从API响应中获取退款状态
      const apiRefundStatus = product.statusInOrder === 'partial_refunded' || product.statusInOrder === 'refunded' || product.RefundCount > 0;

      // 综合判断退款状态
      const hasRefund = isRefunded || apiRefundStatus;

      // 判断是否是套餐
      const isPackage = !!product.packageId || !!product.packageProductInfo;

      // 提取套餐选项信息
      let packageSelectedItems = '';
      if (isPackage && product.packageProductInfo) {
        try {
          // 尝试解析packageProductInfo
          const packageInfo = JSON.parse(product.packageProductInfo);
          if (Array.isArray(packageInfo)) {
            packageSelectedItems = packageInfo.map((item: any) => `${item.name}${item.quantity > 1 ? ` x${item.quantity}` : ''}`).join(' / ');
          }
        } catch (e) {
          // 如果解析失败，直接使用原始字符串
          packageSelectedItems = product.packageProductInfo;
        }
      }

      return {
        id: productId,
        name: product.productName || product.name,
        imageUrl: product.imageUrl || '',
        price: product.originalPrice || 0,
        // 显示剩余数量，如果全退则为0
        quantity: remainingQuantity,
        originalQuantity: originalQuantity,
        unit: product.unit || '件',
        isPackage,
        isInPackage,
        packageSelectedItems,
        isRefunded: hasRefund,
        isFullyRefunded,
        refundQuantity,
        refundAmount,
        // 保留原始数据
        originalData: product
      };
    });
  }

  /**
   * 创建退款映射
   * @param orders 订单列表
   * @returns 退款映射 Map<原始订单号, 退款订单>
   */
  private static createRefundMap(orders: any[]): Map<string, any[]> {
    const refundMap = new Map<string, any[]>();

    // 找出所有退款订单
    const refundOrders = orders.filter(order => order.direction === 'refund' && order.pOrderNo);

    // 为每个原始订单建立映射
    for (const refundOrder of refundOrders) {
      const originalOrderNo = refundOrder.pOrderNo;

      if (!originalOrderNo) continue;

      // 获取或创建该原始订单的退款订单列表
      const orderRefunds = refundMap.get(originalOrderNo) || [];
      orderRefunds.push(refundOrder);
      refundMap.set(originalOrderNo, orderRefunds);
    }

    return refundMap;
  }

  /**
   * 检查订单是否有部分退款
   * @param orderNo 订单号
   * @param refundMap 退款映射
   * @returns 是否有部分退款
   */
  private static checkPartialRefund(orderNo: string, refundMap: Map<string, any[]>): boolean {
    return refundMap.has(orderNo) && refundMap.get(orderNo)!.length > 0;
  }

  /**
   * 格式化价格显示
   * @param price 价格（单位：分）
   * @returns 格式化后的价格字符串（单位：元）
   */
  public static formatPrice(price: number): string {
    return (price / 100).toFixed(2);
  }
}
