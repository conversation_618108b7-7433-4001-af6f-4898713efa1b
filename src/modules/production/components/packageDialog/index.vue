<template>
  <app-dialog
    uiType="card"
    v-model="visible"
    :title="vm.state.packageData?.name || '套餐编辑'"
    @close="handleClose"
    :showHeader="true"
    class="!min-h-[70vh] !w-[80vw] max-w-[1410px] !pb-0 !bg-[#F3F3F3]">
    <!-- 使用左右布局 -->
    <div class="flex flex-row h-full justify-center pb-[80px]">
      <!-- 左侧：已选商品区域 -->
      <div class="flex flex-col w-[660px] flex-shrink-0 bg-white rounded-[18px] shadow-sm">
        <!-- 可滚动的已选商品区域 -->
        <div class="flex-grow overflow-auto px-[48px] py-[24px]">
          <!-- 固定商品列表 -->
          <div class="mb-[40px]">
            <div class="text-[18px] font-bold text-[#000] pb-[19px] mb-[0px]">固定商品列表</div>
            <div class="w-full h-[1px] bg-[rgba(0,0,0,0.08)] mb-[25px]"></div>
            <div v-if="vm.computed.defaultProducts.value.length > 0">
              <div v-for="product in vm.computed.defaultProducts.value" :key="product.id" class="flex items-center justify-between py-[6px] mb-[6px]">
                <div class="text-[16px] font-medium text-[#4C4C4C] flex-grow">
                  {{ product.name }}
                  <!-- 对于by_plan类型，显示基数信息 -->
                  <span v-if="product.isPlanProduct && product.originalCount && product.originalCount > 1" class="text-[14px] text-[#999] ml-1">
                    {{ product.originalCount }}份
                  </span>
                </div>
                <div class="flex items-center">
                  <div class="w-[40px] text-center text-[16px] font-bold text-[#000]">{{ product.count }}</div>
                  <div class="w-[100px] flex justify-end">
                    <price-display :amountInFen="product.currentPrice" class="price-display-small" />
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-gray-500 text-[14px]">无默认商品</div>
          </div>

          <!-- 可选商品 -->
          <div>
            <div class="text-[18px] font-bold text-[#000] pb-[19px] mb-[0px]">可选商品列表</div>
            <div class="w-full h-[1px] bg-[rgba(0,0,0,0.08)] mb-[25px]"></div>
            <div v-if="vm.computed.selectedOptionalItems.value.length > 0">
              <div v-for="(group, groupIndex) in vm.computed.selectedOptionalItems.value" :key="`selected-${groupIndex}`">
                <div v-for="product in group.products" :key="product.id" class="flex items-center justify-between py-[6px] mb-[6px]">
                  <div class="text-[16px] font-medium text-[#4C4C4C] flex-grow">
                    {{ product.name }}
                    <!-- 对于by_plan类型，显示基数信息 -->
                    <span v-if="product.isPlanProduct && product.originalCount && product.originalCount > 1" class="text-[14px] text-[#999] ml-1">
                      {{ product.originalCount }}份
                    </span>
                  </div>
                  <div class="flex items-center">
                    <div class="w-[40px] text-center text-[16px] font-bold text-[#000]">{{ product.selected_count }}</div>
                    <div class="w-[100px] flex justify-end">
                      <price-display :amountInFen="product.currentPrice" class="price-display-default" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-gray-500 text-[14px]">暂未选择可选商品</div>
          </div>
        </div>
      </div>

      <!-- 右侧：可选商品区域 -->
      <div class="flex flex-col w-[660px] flex-shrink-0 ml-[24px] bg-white rounded-[18px] shadow-sm pb-[24px]">
        <div class="px-[24px] pt-[24px] pb-[12px] flex-shrink-0">
          <div class="font-bold text-[18px] flex justify-between items-center">
            <span class="text-[#000]">可选商品列表</span>
          </div>
        </div>

        <!-- 可滚动的可选商品区域 -->
        <div class="flex-grow overflow-auto right-scroll-container px-[24px] pb-[24px]" ref="scrollContainerRef">
          <!-- 可选组列表 -->
          <div
            v-for="(group, groupIndex) in vm.computed.optionalGroups.value"
            :key="groupIndex"
            class="mb-6"
            :ref="
              el => {
                if (el) groupRefs[`group-${groupIndex}`] = el;
              }
            "
            :class="{
              'bg-amber-50 rounded-md transition-colors duration-300 outline outline-1 outline-amber-300': highlightedGroup === `group-${groupIndex}`
            }">
            <!-- 可选组名称和选择要求 -->
            <div class="sticky top-0 z-10 bg-white py-2 w-full pr-2 border-b border-gray-100">
              <div class="flex justify-between items-center mb-2">
                <div class="text-[#666] text-[16px] font-500 leading-normal pl-[18px]">{{ group.name }}</div>
                <div class="text-[#666] text-[14px] font-450 leading-normal">
                  {{ getGroupRequirementText(group) }}
                </div>
              </div>
            </div>

            <!-- 使用el-table替换原来的布局 -->
            <el-table :data="group.products" stripe :show-header="true">
              <el-table-column prop="name" label="商品名称">
                <template #default="scope">
                  <span>
                    {{ scope.row.name }}
                    <!-- 对于by_plan类型，显示基数信息 -->
                    <span v-if="isOptionalByPlan(group) && scope.row.originalCount && scope.row.originalCount > 1"> {{ scope.row.originalCount }}份 </span>
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="单价" align="center">
                <template #default="scope">
                  <div class="flex justify-center items-center">
                    <price-display :amountInFen="scope.row.currentPrice" class="price-display-default" />
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="选择" align="right">
                <template #default="scope">
                  <!-- by_plan类型使用勾选框 -->
                  <el-checkbox
                    v-if="isOptionalByPlan(group)"
                    :model-value="scope.row.selected_count > 0"
                    @update:model-value="handlePlanSelection(groupIndex, scope.$index, $event)"
                    :disabled="!scope.row.selected_count && getMaxAllowedCount(group, groupIndex, scope.$index) === 0"
                    size="large">
                  </el-checkbox>
                  <!-- by_count类型使用数量输入框 -->
                  <erp-input-number
                    v-else
                    v-model="scope.row.selected_count"
                    :min="0"
                    :max="getMaxAllowedCount(group, groupIndex, scope.$index)"
                    @update:model-value="handleProductCountChange(groupIndex, scope.$index, $event)"
                    size="small"
                    :disabled="scope.row.selected_count === 0 && getMaxAllowedCount(group, groupIndex, scope.$index) === 0" />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域：添加价格汇总 -->
    <template #footer>
      <div class="dialog-footer absolute bottom-0 left-0 right-0 h-[100px] border-t border-gray-200 flex justify-end items-center px-[24px] bg-white">
        <!-- 价格汇总 -->
        <div class="flex items-end mr-[16px]">
          <div class="text-[16px] text-[#999] font-medium">待结总计:</div>
          <div class="flex items-end ml-[4px]">
            <price-display :amountInFen="vm.computed.totalPrice.value" class="price-display-large" />
          </div>
        </div>

        <!-- 确认按钮 -->
        <el-button
          type="danger"
          class="!bg-[#E23939] !text-white !text-[20px] !h-[64px] !w-[160px] !rounded-[6px]"
          @click="handleConfirm"
          :disabled="!isPackageValid.valid"
          :class="{ 'opacity-50': !isPackageValid.valid }">
          {{ getConfirmButtonText() }}
        </el-button>
      </div>
    </template>
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';
import { usePackageDialog, type OptionalGroupViewModel } from './presenter';
import type { ProductPackageVO } from '@/types/projectobj';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import { isOptionalByCount, isOptionalByPlan } from '@/utils/productPackageUtils';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { ElMessage, ElButton, ElTable, ElTableColumn, ElAffix, ElScrollbar, ElCheckbox } from 'element-plus';

// 定义套餐商品类型
type PackageProductItem = {
  id: string;
  name: string;
  currentPrice: number;
  count: number;
  selected_count: number;
  originalCount?: number;
  isPlanProduct?: boolean;
};

// 组引用对象，用于滚动定位
const groupRefs: Record<string, any> = {};
const highlightedGroup = ref<string | null>(null);
// 滚动容器引用
const scrollContainerRef = ref<HTMLElement | null>(null);

// 扩展ProductPackageVO接口
interface ExtendedProductPackageVO extends ProductPackageVO {
  packageDetail?: string | any;
}

const props = defineProps<{
  modelValue: ExtendedProductPackageVO;
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: ExtendedProductPackageVO];
  'update:visible': [value: boolean];
  confirm: [value: ExtendedProductPackageVO];
  cancel: [];
}>();

const visible = ref(props.visible);
const vm = usePackageDialog(props.modelValue);

// 计算商品最大可选数量
const getMaxAllowedCount = (group: OptionalGroupViewModel, groupIndex: number, productIndex: number) => {
  const currentProduct = group.products[productIndex];
  if (!currentProduct) return 0;

  if (isOptionalByCount(group)) {
    // 计算除当前商品外已选的总数
    const currentTotalWithoutSelf = group.products.reduce((sum: number, p: PackageProductItem, idx: number) => {
      if (idx === productIndex) return sum;
      return sum + p.count * (p.selected_count || 0);
    }, 0);

    // 对于第一个商品，提供更宽松的限制，允许选择更多
    if (productIndex === 0) {
      // 计算当前是否已经满足最低要求
      const currentTotalWithSelf = currentTotalWithoutSelf + currentProduct.count * (currentProduct.selected_count || 0);
      // 如果当前总数已达到要求，允许继续增加（返回一个较大的数值）
      if (currentTotalWithSelf >= group.optionCount) {
        return 99; // 允许用户选择更多
      }
    }

    // 计算剩余可选数量并转换为当前商品数量单位
    const remainingCount = group.optionCount - currentTotalWithoutSelf;
    return Math.max(0, Math.ceil(remainingCount / currentProduct.count));
  } else if (isOptionalByPlan(group)) {
    // by_plan类型：勾选框模式，返回1表示可选，0表示不可选
    // 如果已选则可以取消，未选则检查组内已选数量是否已满
    if (currentProduct.selected_count > 0) {
      return 1; // 已选可以保持或取消
    }
    // 计算组内其它已选商品数量
    const selectedDistinctCount = group.products.filter((p, idx) => idx !== productIndex && p.selected_count > 0).length;
    // 如果未达到要求数量则可选，否则禁止选择
    return selectedDistinctCount < group.optionCount ? 1 : 0;
  }

  return 999; // 默认上限
};

// 获取分组的选择要求文本
const getGroupRequirementText = (group: OptionalGroupViewModel) => {
  if (isOptionalByCount(group)) {
    // 计算当前已选总数
    const selectedTotal = group.products.reduce((sum, product) => sum + product.count * (product.selected_count || 0), 0);
    return `数量 ${selectedTotal}/${group.optionCount}`;
  } else if (isOptionalByPlan(group)) {
    // 计算已选不同方案数
    const selectedPlans = group.products.filter(p => p.selected_count > 0).length;
    const totalPlans = group.products.length;
    return `方案 ${totalPlans}选${group.optionCount} (已选${selectedPlans})`;
  }
  return '';
};

// 获取确认按钮文本
const getConfirmButtonText = () => {
  if (isPackageValid.value.valid) {
    return '确认';
  }

  // 检查是否有简单的缺少提醒，只显示第一个不满足的组的提醒
  for (const group of vm.computed.optionalGroups.value) {
    if (isOptionalByCount(group)) {
      const selectedTotal = group.products.reduce((sum, product) => {
        const productTotal = product.count * (product.selected_count || 0);
        console.log(`商品 ${product.name}: count=${product.count}, selected=${product.selected_count}, total=${productTotal}`);
        return sum + productTotal;
      }, 0);
      const missing = group.optionCount - selectedTotal;
      console.log(`组 ${group.name}: 需要=${group.optionCount}, 已选=${selectedTotal}, 缺少=${missing}`);

      if (missing > 0) {
        // 只在缺少时显示提醒，限制显示范围避免过长
        if (missing <= 5) {
          return `还差${missing}份`;
        } else {
          return `还需选择${missing}份`;
        }
      }
    } else if (isOptionalByPlan(group)) {
      const selectedPlans = group.products.filter(p => p.selected_count > 0).length;
      const missing = group.optionCount - selectedPlans;
      if (missing > 0) {
        // 只在缺少时显示提醒
        if (missing <= 3) {
          return `还差${missing}个方案`;
        } else {
          return `还需选择${missing}个方案`;
        }
      }
    }
  }

  return '确认';
};

// 检查套餐是否有效
const isPackageValid = computed(() => {
  // 检查基本数据有效性
  if (!vm.state.packageData) {
    return { valid: false, message: '套餐数据无效', targetId: '' };
  }

  // 检查套餐数量
  if (!vm.state.packageQuantity || vm.state.packageQuantity < 1) {
    return { valid: false, message: '请设置<span class="text-[#E53935] font-medium px-[2px]">【套餐数量】</span>', targetId: '' };
  }

  // 检查各个可选组是否满足要求
  for (const [index, group] of vm.computed.optionalGroups.value.entries()) {
    const groupTargetId = `group-${index}`;

    // by_count类型：检查是否达到或超过要求数量
    if (isOptionalByCount(group)) {
      const selectedTotal = group.products.reduce((sum, product) => sum + product.count * (product.selected_count || 0), 0);

      if (selectedTotal < group.optionCount) {
        return {
          valid: false,
          message: `<span class="text-[#E53935] font-medium px-[2px]">【${group.name}】</span>需选 <span class="text-[#E53935] font-medium px-[2px]">${group.optionCount}</span> 件，已选 <span class="text-[#E53935] font-medium px-[2px]">${selectedTotal}</span> 件`,
          targetId: groupTargetId
        };
      }

      // 移除对by_count超选的检查，允许超量选择
    }
    // by_plan类型：检查是否达到或超过要求数量的不同方案
    else if (isOptionalByPlan(group)) {
      const selectedPlansCount = group.products.filter(p => p.selected_count > 0).length;
      const totalPlans = group.products.length;

      if (selectedPlansCount < group.optionCount) {
        return {
          valid: false,
          message: `<span class="text-[#E53935] font-medium px-[2px]">【${group.name}】</span>需要从 <span class="text-[#E53935] font-medium px-[2px]">${totalPlans}</span> 个方案中选择 <span class="text-[#E53935] font-medium px-[2px]">${group.optionCount}</span> 个，已选 <span class="text-[#E53935] font-medium px-[2px]">${selectedPlansCount}</span> 个`,
          targetId: groupTargetId
        };
      }

      if (selectedPlansCount > group.optionCount) {
        return {
          valid: false,
          message: `<span class="text-[#E53935] font-medium px-[2px]">【${group.name}】</span>最多选 <span class="text-[#E53935] font-medium px-[2px]">${group.optionCount}</span> 个方案，已超选`,
          targetId: groupTargetId
        };
      }
    }
  }

  return { valid: true, message: '', targetId: '' };
});

// 判断一个可选组是否已选择足够的商品/方案
const isGroupComplete = (group: OptionalGroupViewModel) => {
  if (isOptionalByCount(group)) {
    // 计算当前已选总数
    const selectedTotal = group.products.reduce((sum, product) => sum + product.count * (product.selected_count || 0), 0);
    return selectedTotal >= group.optionCount;
  } else if (isOptionalByPlan(group)) {
    // 计算已选不同方案数
    const selectedPlans = group.products.filter(p => p.selected_count > 0).length;
    return selectedPlans >= group.optionCount;
  }
  return false;
};

// 监听props变化
watch(
  () => props.visible,
  newValue => {
    visible.value = newValue;
  }
);

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 处理确认
const handleConfirm = () => {
  console.log('=== PackageDialog handleConfirm ===');

  if (!isPackageValid.value.valid) {
    ElMessage.warning('请检查套餐选择是否满足要求');
    if (isPackageValid.value.targetId) {
      scrollToTarget(isPackageValid.value.targetId);
    }
    return;
  }

  if (vm.state.packageData) {
    // 调用修改后的convertToConfirmData方法，不再需要传参
    const confirmData = vm.actions.convertToConfirmData();
    console.log('confirmData prepared:', confirmData);

    emit('confirm', confirmData);
    emit('update:visible', false);
  }
};

// 处理方案选择（by_plan类型）
const handlePlanSelection = (groupIndex: number, productIndex: number, checked: string | number | boolean) => {
  const selectedCount = checked ? 1 : 0;
  vm.actions.updateProductCount(groupIndex, productIndex, selectedCount);
  // 确保总价更新
  nextTick(() => {
    vm.computed.totalPrice.value;
  });
};

// 处理商品数量变化（by_count类型）
const handleProductCountChange = (groupIndex: number, productIndex: number, val: number | undefined) => {
  if (val !== undefined) {
    vm.actions.updateProductCount(groupIndex, productIndex, val);
    // 确保总价更新
    nextTick(() => {
      vm.computed.totalPrice.value;
    });
  }
};

// 处理套餐数量变化
const handleQuantityChange = (currentValue: number | undefined, oldValue: number | undefined) => {
  console.log(`套餐数量变化: ${oldValue} → ${currentValue}`);
};

// 滚动到目标元素
const scrollToTarget = (targetId: string) => {
  if (!targetId) return;

  nextTick(() => {
    const targetElement = groupRefs[targetId];
    if (targetElement) {
      // 高亮目标组
      highlightedGroup.value = targetId;

      // 获取右侧滚动容器
      const scrollContainer = document.querySelector('.right-scroll-container');
      if (scrollContainer) {
        const containerRect = scrollContainer.getBoundingClientRect();
        const targetRect = targetElement.getBoundingClientRect();
        const offset = 20; // 向上偏移量
        const scrollPosition = scrollContainer.scrollTop + targetRect.top - containerRect.top - offset;

        // 滚动并确保位置有效
        scrollContainer.scrollTo({
          top: scrollPosition > 0 ? scrollPosition : 0,
          behavior: 'smooth'
        });

        // 3秒后清除高亮
        setTimeout(() => {
          highlightedGroup.value = null;
        }, 3000);
      }
    }
  });
};
</script>

<style scoped>
/* 仅保留必要样式 */
</style>
