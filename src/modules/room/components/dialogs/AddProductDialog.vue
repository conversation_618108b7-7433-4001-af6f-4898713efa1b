<template>
  <app-dialog v-model="visible">
    <template #title-addon>{{ vm.state.selectedRoom?.name ? ' ' + vm.state.selectedRoom.name : '' }}</template>
    <!-- 商品列表 -->
    <LeftRightLayout :leftClass="'w-[1020px]'" :rightClass="'w-[660px]'" leftTitle="商品列表" rightTitle="商品账单">
      <!-- 左侧商品列表区域 -->
      <template #left>
        <div class="h-full flex flex-col pb-[24px]">
          <!-- 商品内容区域 - 使用flex和子元素定位 -->
          <div class="flex flex-1 relative overflow-hidden">
            <!-- 分类侧边栏 -->
            <div class="w-[160px] h-full overflow-y-auto border-r flex-shrink-0">
              <div class="w-[120px] mx-auto mt-5 flex flex-col gap-4">
                <div
                  v-for="category in categories"
                  :key="category.id || 'all'"
                  class="h-[60px] rounded-lg flex items-center justify-center cursor-pointer transition-colors duration-200"
                  :class="{
                    'bg-red-500 text-white': category.isActive,
                    'text-gray-600 hover:bg-red-50 hover:text-red-600': !category.isActive
                  }"
                  @click="changeCategory(category.id)">
                  <span class="truncate text-base font-medium">{{ category.name }}</span>
                </div>
              </div>
            </div>
            <!-- 商品区域 - 使用flex和el-scrollbar -->
            <div class="flex-1 flex flex-col overflow-hidden">
              <!-- 搜索框 -->
              <div class="p-5 flex-shrink-0">
                <div class="w-full h-[60px] rounded-lg border border-black/10 flex items-center px-6">
                  <input v-model="searchKeyword" placeholder="搜索商品" class="w-full text-lg text-black/40 outline-none font-medium" @input="handleSearch" />
                  <el-button v-if="searchKeyword" link @click="clearSearch" class="ml-2">
                    <el-icon class="text-[24px] text-black/40">
                      <Close />
                    </el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 商品表格 - 使用el-table组件 -->
              <div class="flex-1 px-5 overflow-hidden flex flex-col">
                <el-table
                  stripe
                  :data="products"
                  class="w-full product-table compact-table flex-1"
                  :row-class-name="tableRowClassName"
                  v-loading="loading"
                  @scroll="handleTableScroll"
                  table-layout="auto"
                  ref="productTableRef">
                  <el-table-column prop="name" label="商品名称" align="left">
                    <template #default="{ row }">
                      <div class="flex flex-col justify-center py-2">
                        <span class="font-medium text-black truncate">{{ row.name }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="80px" align="center">
                    <template #default="{ row }">
                      <el-tag v-if="row.isSoldOut" type="info" size="small" effect="plain" class="text-sm">沽清</el-tag>
                      <el-tag
                        v-else-if="row.hasOptionalGroups"
                        size="small"
                        effect="plain"
                        class="text-sm"
                        style="color: #319cff; border-color: #319cff; background-color: rgba(49, 156, 255, 0.1)"
                        >可选</el-tag
                      >
                      <el-tag v-else-if="row.tag" type="warning" size="small" effect="plain" class="text-sm">{{ row.tag }}</el-tag>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="unit" label="单位" width="80px" align="center">
                    <template #default="{ row }">
                      {{ row.unit || '份' }}
                    </template>
                  </el-table-column>

                  <!-- <el-table-column 
                    prop="stock" 
                    label="库存" 
                    align="center" /> -->
                  <el-table-column prop="currentPrice" label="单价" width="120px" align="center">
                    <template #default="{ row }">
                      <div class="flex items-center justify-center">
                        <span class="text-[16px] font-medium">{{ formatYuanWithSymbol(row.currentPrice).replace('￥', '') }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="120px" align="center">
                    <template #default="{ row }">
                      <div class="flex justify-center items-center">
                        <erp-input-number :disabled="row.isSoldOut" v-model="row.quantity" simpleMode type="default" @change="handleAddToCart(row)" />
                      </div>
                    </template>
                  </el-table-column>

                  <!-- 添加空数据提示 -->
                  <template #empty>
                    <div class="py-10 text-center">
                      <div class="text-gray-400 mb-2 text-lg">{{ searchKeyword ? '没有找到匹配的商品' : '暂无商品数据' }}</div>
                      <div class="text-gray-400 text-sm">
                        {{ searchKeyword ? `没有找到包含"${searchKeyword}"的商品，请尝试其他关键词` : '请选择其他分类或刷新页面' }}
                      </div>
                    </div>
                  </template>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 右侧订单区域 -->
      <template #right>
        <div class="flex flex-col cart-container h-full">
          <!-- 内容区域（包含商品列表和按钮区） -->
          <div class="flex flex-col h-full">
            <!-- 购物车区域 - 使用el-card和el-table -->
            <el-card class="cart-card !rounded-[12px] mb-4 flex-1 !flex !flex-col !overflow-hidden">
              <template #header>
                <div class="flex justify-between items-center px-2">
                  <div class="text-lg text-black font-semibold">商品账单</div>
                  <div v-if="vm.state.cartItems.length > 0" class="flex items-center text-sm font-semibold cursor-pointer">
                    <el-button link @click="vm.actions.clearCart" text>
                      <span class="text-sm">清空</span>
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 购物车商品表格区域 -->
              <div class="flex-1 overflow-hidden flex flex-col px-[12px]">
                <el-table
                  size="small"
                  ref="cartTable"
                  v-loading="isLoading"
                  :data="processedCartItems"
                  :span-method="objectSpanMethod"
                  :row-class-name="cartItemRowClassName"
                  class="cart-table flex-1">
                  <!-- 商品名称列 -->
                  <el-table-column prop="name" label="商品名称">
                    <template #default="scope">
                      <div v-if="scope.row.isDetail" class="detail-row">
                        <PackageDetailItem
                          :row="scope.row"
                          :isDetail="scope.row.isDetail"
                          :parentId="scope.row.parentId"
                          :parent="findParentItem(scope.row.parentId || '')" />
                      </div>
                      <div v-else class="product-name">{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>

                  <!-- 数量列 -->
                  <el-table-column prop="quantity" label="数量" max-width="120px" align="center">
                    <template #default="scope">
                      <div class="py-1">
                        <ErpInputNumber
                          v-if="!scope.row.isDetail"
                          :key="`quantity-${scope.row.id}`"
                          v-model="scope.row.quantity"
                          type="default"
                          size="small"
                          :min="0"
                          :step="1"
                          @update:modelValue="debouncedQuantityChange($event, scope.row)" />
                      </div>
                    </template>
                  </el-table-column>

                  <!-- 单价列 -->
                  <el-table-column prop="currentPrice" label="单价" max-width="100px" align="right">
                    <template #default="scope">
                      <span v-if="!scope.row.isDetail" class="price">¥ {{ (scope.row.currentPrice / 100).toFixed(2) }}</span>
                    </template>
                  </el-table-column>

                  <!-- 金额列 -->
                  <el-table-column label="金额" max-width="120px" align="right">
                    <template #default="scope">
                      <span v-if="!scope.row.isDetail" class="amount text-red-500">
                        ¥ {{ ((scope.row.currentPrice * scope.row.quantity) / 100).toFixed(2) }}
                      </span>
                    </template>
                  </el-table-column>

                  <!-- 购物车空数据提示 -->
                  <template #empty>
                    <div class="py-10 text-center">
                      <div class="text-gray-400 mb-2 text-lg">购物车还是空的</div>
                      <div class="text-gray-400 text-sm">请从左侧商品列表选择商品添加到购物车</div>
                    </div>
                  </template>
                </el-table>
              </div>

              <!-- 小计 -->
              <div v-if="vm.state.cartItems.length > 0" class="p-3 text-right border-t border-gray-100">
                <span class="text-gray-500"
                  >小计: ¥
                  <span class="text-lg text-black font-bold">{{ formatYuanWithSymbol(vm.computed.totalAmountInFen.value).replace('￥', '') }}</span></span
                >
                <span class="text-sm text-gray-500 ml-2">(共{{ vm.computed.totalItems.value }}件)</span>
              </div>
            </el-card>

            <!-- 功能按钮区 -->
            <!-- <div class="grid grid-cols-5 gap-2 function-buttons">
              <el-button class="!h-10 text-sm">商品打折</el-button>
              <el-button class="!h-10 text-sm">存单</el-button>
              <el-button class="!h-10 text-sm">取单</el-button>
              <el-button class="!h-10 text-sm">核销</el-button>
              <el-button class="!h-10 text-sm">备注</el-button>
            </div> -->
          </div>
        </div>
      </template>

      <!-- 添加底部栏插槽 -->
      <template #right-footer>
        <div class="flex items-center justify-between px-5 border-t h-full border-l border-gray-100">
          <div class="flex flex-col">
            <div class="text-[16px] font-semibold text-[#000]">总金额:</div>
            <div class="flex items-baseline">
              <span class="text-[36px] font-bold leading-none">{{ Math.floor(vm.computed.totalAmountInFen.value / 100) }}</span>
              <span class="text-[16px] text-[rgba(0,0,0,0.40)]">.00</span>
            </div>
          </div>
          <el-button class="btn-black" @click="handleConfirm" :loading="isConfirmProcessing">
            {{ mode === 'gift' ? '赠送' : '确认' }}
          </el-button>
        </div>
      </template>
    </LeftRightLayout>

    <ProductionOrderPrinter
      v-if="vm.state.showPrinter"
      v-model:visible="vm.state.showPrinter"
      :data="vm.state.printData"
      @print-success="vm.actions.handlePrintSuccess"
      @print-failed="vm.actions.handlePrintFailed" />

    <!-- 添加套餐选择弹窗 -->
    <package-dialog
      v-if="vm.state.packageDialogVisible"
      v-model="vm.state.currentPackage"
      v-model:visible="vm.state.packageDialogVisible"
      @confirm="vm.actions.handlePackageConfirm"
      @cancel="vm.actions.closePackageDialog" />
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, onBeforeUnmount, defineComponent, computed } from 'vue';
import { Search, Loading, Minus, Plus, Close, Operation } from '@element-plus/icons-vue';
import ProductSelector from '@/modules/production/components/ProductSelector.vue';
import RoomSelectorDialog from '@/modules/room/components/RoomSelectorDialog.vue';
import ProductionOrderPrinter from '@/modules/production/components/ProductionOrderPrinter.vue';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import { useAddProductOrder } from '@/modules/production/views/AddProductOrder/presenter';
import PackageDialog from '@/modules/production/components/packageDialog/index.vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import LeftRightLayout from '@/components/Dialog/layout';
import IconButton from '@/components/icons/IconButton.vue';
import { IconPlus, IconMinus } from '@/components/icons';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import PackageDetailItem from './components/PackageDetailItem.vue';

import { ICartItem } from '@/modules/production/views/OpenTableProduct/viewmodel';
import { ProductApi } from '@/modules/production/api/product';
// 使用vue-hooks-plus库中的useRequest
import { useRequest } from 'vue-hooks-plus';
import { debounce } from 'lodash-es';
import type { OrderProductVO } from '@/types/projectobj';
import { ElMessage } from 'element-plus';
import type { SessionOperationVOVoOrderVO } from '@/api/autoGenerated';

// 记录组件实例ID和打开次数
const instanceId = 'add_product_dialog';

// 工具方法：将页面商品转换为购物车项
const convertToCartItem = (product: any): ICartItem => {
  // 打印传入的商品结构，帮助调试
  console.log(`[${instanceId}] 转换商品数据, 原始数据:`, product);

  // 处理套餐明细，格式化为更符合设计图的格式
  let packageDetail = null;
  if (product.isPackage && product.packageProducts && product.packageProducts.length > 0) {
    const detailItems = product.packageProducts.map((p: any) => `${p.name}×${p.quantity}`);
    packageDetail = {
      detailString: detailItems.join('，')
    };
  } else if (product.packageDetail) {
    packageDetail = product.packageDetail;
  }

  // 处理可选组商品
  let isOptionalGroup = !!product.isOptionalGroup;
  let optionalProducts = null;
  let optionType = null;
  let optionCount = null;
  let selectedQuantity = null;
  let isFree = !!product.isFree;
  let details = '';

  if (isOptionalGroup && product.optionalProducts) {
    optionalProducts = product.optionalProducts;
    optionType = product.optionType || 'ByCount';
    optionCount = product.optionCount || 1;
    selectedQuantity = product.selectedQuantity || 0;

    // 生成详情文本
    if (optionalProducts.length > 0) {
      const selectedProducts = optionalProducts.filter((p: any) => (p.quantity || 0) > 0);
      if (selectedProducts.length > 0) {
        const items = selectedProducts.map((p: any) => `${p.name}×${p.quantity}`);
        details = items.join('，');
      } else {
        details = '未选择任何商品';
      }
    }
  }

  const cartItem: ExtendedCartItem = {
    id: product.id || '',
    name: product.productName || product.name || '',
    currentPrice: product.currentPrice || product.price || 0,
    // 确保数量至少为1
    quantity: product.quantity > 0 ? product.quantity : 1,
    flavors: product.flavors || '',
    unit: product.unit || '',
    isPackage: !!product.isPackage,
    packageDetail: packageDetail,
    // 可选组属性
    isOptionalGroup: isOptionalGroup,
    isFree: isFree
  };

  // 仅当是可选组时添加这些属性
  if (isOptionalGroup) {
    cartItem.optionalProducts = optionalProducts;
    cartItem.optionType = optionType;
    cartItem.optionCount = optionCount;
    cartItem.selectedQuantity = selectedQuantity;
    cartItem.details = details;
  }

  console.log(`[${instanceId}] 转换后的购物车项:`, cartItem);
  return cartItem;
};

// 定义商品和分类的类型
interface ProductCategory {
  id: string | null;
  name: string;
  count: string | number;
  isActive: boolean;
  type: string;
  isPackage: boolean;
}

interface Product {
  id: string;
  name: string;
  stock: number;
  tag: string;
  currentPrice: number;
  unit: string;
  isPackage: boolean;
  isSoldOut: boolean;
  packageProducts?: any[];
  optionalGroups?: any[];
  productVOList?: any[];
  hasOptionalGroups?: boolean;
}

interface ProductsResult {
  products: Product[];
  isLoadingMore: boolean;
}

// 商品选择器相关状态
const searchKeyword = ref('');
const categories = ref<ProductCategory[]>([]);
const products = ref<Product[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const selectedCategoryId = ref<string | null>(null);
const currentPage = ref(1);
const pageSize = ref(200);
const productTableRef = ref<HTMLElement | null>(null);
const loadingMore = ref(false);
const hasMore = ref(true);

// 设置表格行的类名
const tableRowClassName = ({ row }: { row: Product }) => {
  if (row.isSoldOut) {
    return 'sold-out-row';
  }
  return 'normal-row';
};

// 加载更多数据
const loadMore = async () => {
  if (loadingMore.value || !hasMore.value) return;
  loadingMore.value = true;
  currentPage.value++;
  await fetchProducts(true);
};

// 类型定义
const CATEGORY_TYPE = {
  ALL: 'all',
  PACKAGE: 'package',
  PRODUCT: 'product'
};

// 使用useRequest获取商品分类
const {
  loading: categoriesLoading,
  error: categoriesError,
  run: fetchCategories,
  cancel: cancelFetchCategories
} = useRequest(
  async () => {
    const response = await ProductApi.listProductTypes({});

    if (response.code === 0) {
      // 商品分类
      const productTypes = response.data.productTypeVOs.map((type: any) => ({
        id: type.id,
        name: type.name,
        count: type.count,
        isActive: false,
        type: CATEGORY_TYPE.PRODUCT,
        isPackage: false
      }));

      // 套餐分类
      const packageTypes = response.data.productPackageTypeVOs.map((type: any) => ({
        id: type.id,
        name: type.name,
        count: type.count,
        isActive: false,
        type: CATEGORY_TYPE.PACKAGE,
        isPackage: true
      }));

      // 所有分类
      const allCategories: ProductCategory[] = [
        {
          id: null,
          name: '全部分类',
          count: '',
          isActive: true,
          type: CATEGORY_TYPE.ALL,
          isPackage: false
        },
        ...packageTypes,
        ...productTypes
      ];

      categories.value = allCategories;
      selectedCategoryId.value = null;
      return allCategories;
    } else {
      throw new Error(response.message || '获取商品分类失败');
    }
  },
  {
    manual: true,
    onBefore: () => {
      // 如果已经有数据，则不需要再次请求
      if (categories.value.length > 0) {
        console.log(`[${instanceId}] 已有分类数据，跳过请求`);
        return false; // 返回false会阻止请求发出
      }
    },
    onError: (err: Error) => {
      console.error(`[${instanceId}] 获取商品分类失败:`, err);
      error.value = '获取分类失败: ' + err.message;
    },
    onFinally: () => {
      console.log(`[${instanceId}] 分类请求完成`);
    }
  }
);

// 使用useRequest获取商品列表
const {
  loading: productsLoading,
  error: productsError,
  run: runFetchProducts,
  refresh: refreshProducts,
  cancel: cancelFetchProducts
} = useRequest<ProductsResult, [boolean, string | null, number, number]>(
  async (isLoadingMore = false, categoryId: string | null = selectedCategoryId.value, page = currentPage.value, size = pageSize.value) => {
    console.log(`[${instanceId}] 获取商品列表:`, { isLoadingMore, categoryId, page });

    // 找到选中的分类
    const selectedCategory = categories.value.find(c => c.id === categoryId) ?? null;

    // 创建API参数对象
    const apiParams: any = {
      pageNum: page,
      pageSize: size
    };
    if (selectedCategory?.isPackage) {
      apiParams.productPackageTypeId = selectedCategory.id;
    } else {
      apiParams.category = selectedCategory?.id || '';
    }

    // 添加搜索关键字参数
    if (searchKeyword.value.trim()) {
      apiParams.name = searchKeyword.value.trim();
    }

    console.log(`[${instanceId}] API参数:`, apiParams);

    // 使用实际的 ProductApi
    const response = await ProductApi.queryDetailByType(apiParams);

    if (response.code === 0) {
      let newProducts: Product[] = [];

      // 处理套餐数据
      const packageList = response.data.productPackageVOs || [];
      const packageProducts = packageList.map((item: any) => ({
        id: item.id,
        name: item.name,
        stock: -1,
        tag: item.isPromotion ? '促销' : '',
        currentPrice: item.currentPrice,
        unit: '套餐',
        isPackage: true,
        isSoldOut: item.isSoldOut,
        packageProducts: item.packageProducts,
        optionalGroups: item.optionalGroups,
        productVOList: item.productVOList,
        // optionalGroups 是json数组字符串
        hasOptionalGroups: item.optionalGroups && JSON.parse(item.optionalGroups).length > 0
      }));

      // 处理普通商品数据
      const productList = response.data.productVOs || [];
      const normalProducts = productList.map((item: any) => ({
        id: item.id,
        name: item.name,
        stock: item.calculateInventory ? item.lowStockThreshold : -1,
        tag: item.isPromotion ? '促销' : '',
        currentPrice: item.currentPrice,
        unit: item.unit,
        isPackage: false,
        isSoldOut: item.isSoldOut
      }));

      // 根据选中的分类类型决定显示哪些产品
      if (selectedCategory && selectedCategory.id !== null) {
        newProducts = selectedCategory.isPackage ? packageProducts : normalProducts;
      } else {
        newProducts = [...packageProducts, ...normalProducts];
      }

      console.log(`[${instanceId}] 商品列表处理完成: ${newProducts.length}个商品`);
      hasMore.value = newProducts.length === size;

      // 返回处理结果和加载状态
      return {
        products: newProducts,
        isLoadingMore
      };
    } else {
      throw new Error(response.message || '获取商品列表失败');
    }
  },
  {
    manual: true,
    onBefore: params => {
      const [isLoadingMore] = params;
      // 设置加载状态
      if (!isLoadingMore) {
        loading.value = true;
      }
      loadingMore.value = !!isLoadingMore;
    },
    onSuccess: (result: ProductsResult) => {
      const { products: newProducts, isLoadingMore } = result;

      if (isLoadingMore) {
        products.value = [...products.value, ...newProducts];
      } else {
        products.value = newProducts;
      }

      console.log(`[${instanceId}] 商品列表更新完成，当前有${products.value.length}个商品`);
    },
    onError: (err: Error) => {
      console.error(`[${instanceId}] 获取商品列表失败:`, err);
      error.value = '获取商品列表失败: ' + err.message;
    },
    onFinally: () => {
      loading.value = false;
      loadingMore.value = false;
      console.log(`[${instanceId}] 商品列表请求完成`);
    }
  }
);

// 立即取消所有未完成的请求
const cancelAllRequests = () => {
  console.log(`[${instanceId}] 取消所有未完成的请求`);
  cancelFetchCategories();
  cancelFetchProducts();
};

// 封装fetchProducts函数，保持与原有代码兼容，取消之前的请求
const fetchProducts = (isLoadingMore = false) => {
  // 防止重复调用
  if (loading.value && !isLoadingMore) {
    console.log(`[${instanceId}] 已有商品列表请求正在进行，跳过`);
    return Promise.resolve();
  }

  // 不管是否加载更多，先取消之前的请求
  cancelFetchProducts();
  return runFetchProducts(isLoadingMore, selectedCategoryId.value, currentPage.value, pageSize.value);
};

// 切换分类
const changeCategory = (categoryId: string | null) => {
  // 如果选择的是相同分类，不做处理
  if (categoryId === selectedCategoryId.value) {
    console.log(`[${instanceId}] 选择相同分类，跳过`);
    return;
  }

  selectedCategoryId.value = categoryId;
  categories.value.forEach(category => {
    category.isActive = category.id === categoryId;
  });
  currentPage.value = 1;
  hasMore.value = true;
  fetchProducts();
};

// 滚动监听处理
const handleTableScroll = (e: any) => {
  if (!productTableRef.value) return;

  // 获取表格元素，使用any类型来访问$el属性
  const table = productTableRef.value as any;
  const scrollElement = table.$el.querySelector('.el-scrollbar__wrap');

  if (scrollElement) {
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    // 判断是否滚动到底部 (距离底部小于50px)
    if (scrollHeight - scrollTop - clientHeight < 50) {
      if (!loadingMore.value && hasMore.value) {
        console.log(`[${instanceId}] 滚动到底部，加载更多数据`);
        loadMore();
      }
    }
  }
};

// 处理搜索的实际逻辑
const doSearch = () => {
  // 重置页码并重新加载数据
  currentPage.value = 1;
  hasMore.value = true;

  // 如果是搜索操作，可以重置分类选择
  if (searchKeyword.value.trim()) {
    // 可以选择重置分类为全部，也可以保持当前分类
    // categories.value.forEach(category => {
    //   category.isActive = category.id === null
    // })
    // selectedCategoryId.value = null
  }

  fetchProducts();
};

// 使用lodash的debounce创建防抖函数，延迟300ms
const debouncedSearch = debounce(doSearch, 300);

// 处理搜索 - 使用lodash debounce
const handleSearch = () => {
  debouncedSearch();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  currentPage.value = 1;
  hasMore.value = true;
  fetchProducts();
};

// 处理添加到购物车
const handleAddToCart = (product: any) => {
  console.log(`[${instanceId}] 添加商品到购物车:`, product.name);
  vm.actions.addToCart(product);

  // 查找购物车中是否有该商品
  const cartItem = vm.state.cartItems.find(item => item.id === product.id);

  // 如果是可选组商品，确保更新其明细
  if (cartItem && (cartItem as ExtendedCartItem).isOptionalGroup) {
    updateOptionalGroupDetails(cartItem as ExtendedCartItem);
  }
};

// 扩展AddProductOrder的state类型
interface ExtendedAddProductOrderState {
  selectedRoom: any;
  selectedArea: string;
  currentSessionId: string;
  cartItems: ICartItem[];
  selectedSeller: string;
  showPrinter: boolean;
  printData: any;
  isChargeToRoom: boolean;
  showRoomSelector: boolean;
  packageDialogVisible: boolean;
  currentPackage: any;
  orderType: string;
}

// 扩展AddProductOrder的actions类型
interface ExtendedAddProductOrderActions {
  addToCart: (product: any) => void;
  increaseQuantity: (item: any) => void;
  decreaseQuantity: (item: any) => void;
  clearCart: () => void;
  handleRoomSelect: (room: any) => void;
  clearRoomSelection: () => void;
  handleConfirm: () => Promise<void>;
  handlePrintSuccess: () => void;
  handlePrintFailed: () => void;
  handlePackageConfirm: (packageData: any) => void;
  closePackageDialog: () => void;
  addNote?: () => void;
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roomId: {
    type: String,
    default: ''
  },
  roomName: {
    type: String,
    default: ''
  },
  areaId: {
    type: String,
    default: ''
  },
  areaName: {
    type: String,
    default: ''
  },
  sessionId: {
    type: String,
    default: ''
  },
  outOrderProducts: {
    type: Array,
    default: () => []
  },
  // 新增赠送模式属性
  mode: {
    type: String,
    default: 'normal', // normal 普通点单模式, gift 赠送商品模式
    validator: (value: string) => ['normal', 'gift'].includes(value)
  },
  operator: {
    type: String,
    default: ''
  },
  venueId: {
    type: String,
    default: ''
  },
  memberId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'gift-success']);

const visible = ref(props.modelValue);

// 数据加载状态标志
const isDataInitialized = ref(false);
const isRoomInitialized = ref(false);
const isConfirmProcessing = ref(false);

// 使用解构方式获取presenter
const addProductOrder = useAddProductOrder();
const vm = {
  state: addProductOrder.state as unknown as ExtendedAddProductOrderState,
  computed: addProductOrder.computed,
  actions: addProductOrder.actions as unknown as ExtendedAddProductOrderActions
};

// 获取当前模式
const mode = computed(() => props.mode);

// 初始化房间和会话信息
const initializeRoomAndSession = () => {
  // 如果已经初始化过，则不再重复初始化
  if (isRoomInitialized.value) {
    console.log(`[${instanceId}] 房间信息已初始化，跳过`);
    return;
  }

  // 设置房间信息
  if (props.roomId && props.roomName) {
    vm.state.selectedRoom = {
      id: props.roomId,
      name: props.roomName,
      areaVO:
        props.areaId && props.areaName
          ? {
              id: props.areaId,
              name: props.areaName
            }
          : undefined
    };

    if (props.areaId) {
      vm.state.selectedArea = props.areaId;
    }
  }

  // 设置会话ID
  if (props.sessionId) {
    vm.state.currentSessionId = props.sessionId;
  }

  // 设置已有商品（数据回写）
  if (props.outOrderProducts && props.outOrderProducts.length > 0) {
    console.log(`[${instanceId}] 页面已有商品数据，进行回写:`, props.outOrderProducts.length);
    // 清空现有商品，避免重复添加
    vm.state.cartItems = [];
    // 使用转换方法添加传入的商品到购物车
    vm.state.cartItems = props.outOrderProducts.map((product: any) => convertToCartItem(product));
  }

  console.log(`[${instanceId}] 房间信息初始化完成，购物车商品数量:`, vm.state.cartItems.length);

  isRoomInitialized.value = true;
};

// 加载商品数据
const loadProductData = () => {
  if (isDataInitialized.value) {
    console.log(`[${instanceId}] 数据已初始化，跳过加载`);
    return;
  }

  console.log(`[${instanceId}] 开始加载商品数据`);
  // 加载商品分类
  if (categories.value.length === 0) {
    fetchCategories();
  }

  // 加载商品列表
  if (products.value.length === 0) {
    fetchProducts();
  }

  isDataInitialized.value = true;
};

// 使用一个独立的状态来追踪是否已初始化
let hasInitialized = false;

// 监听props变化
watch(
  () => props.modelValue,
  (val, oldVal) => {
    if (val === oldVal) {
      console.log(`[${instanceId}] modelValue未变化，忽略`, val);
      return;
    }

    console.log(`[${instanceId}] modelValue变化:`, oldVal, '->', val);
    visible.value = val;

    if (val) {
      // 当弹窗打开时，初始化数据
      if (!hasInitialized) {
        hasInitialized = true;
        console.log(`[${instanceId}] 弹窗首次打开，初始化数据`);
        nextTick(() => {
          if (!isRoomInitialized.value) {
            initializeRoomAndSession();
          }
          if (!isDataInitialized.value) {
            loadProductData();
          }
        });
      } else {
        console.log(`[${instanceId}] 弹窗再次打开，跳过初始化`);
      }
    } else {
      // 弹窗关闭时，重置处理状态
      isConfirmProcessing.value = false;
      // 取消所有未完成的请求
      cancelAllRequests();
    }
  },
  { immediate: true }
);

// 监听visible变化，实现双向绑定，使用函数形式避免副作用
watch(visible, (val, oldVal) => {
  if (val === oldVal) return;
  console.log(`[${instanceId}] visible变化:`, oldVal, '->', val);
  emit('update:modelValue', val);
});

// 组件挂载时初始化
onMounted(() => {
  console.log(`[${instanceId}] 组件已挂载, modelValue=`, props.modelValue);
  if (props.modelValue && !isRoomInitialized.value && !hasInitialized) {
    hasInitialized = true;
    nextTick(() => {
      initializeRoomAndSession();
      loadProductData();
      initializeOrderType();
    });
  }
});

// 组件卸载前取消所有请求
onBeforeUnmount(() => {
  cancelAllRequests();
});

// 确认按钮处理 - 添加防抖处理
const handleConfirm = async () => {
  // 防止重复点击
  if (isConfirmProcessing.value) {
    return;
  }

  try {
    console.log(`[${instanceId}] 确认按钮点击, 模式: ${mode.value}`);
    isConfirmProcessing.value = true;

    // 确保购物车有商品
    if (vm.state.cartItems.length === 0) {
      console.warn(`[${instanceId}] 购物车为空，无法确认`);
      ElMessage.warning('请先添加商品');
      isConfirmProcessing.value = false;
      return;
    }

    // 确保已选择房间
    if (!vm.state.selectedRoom) {
      console.warn(`[${instanceId}] 未选择送达包厢，无法确认`);
      ElMessage.warning('请先选择送达包厢');
      isConfirmProcessing.value = false;
      return;
    }

    // 详细记录每个购物车商品的数据
    console.log(`[${instanceId}] 购物车商品详情:`);
    vm.state.cartItems.forEach((item, index) => {
      console.log(`[${instanceId}] 商品[${index}]: ID=${item.id}, 名称=${item.name}, 数量=${item.quantity}, 单价=${item.currentPrice}`);
    });

    // 根据当前模式选择不同的处理逻辑
    if (mode.value === 'gift') {
      // 赠送商品模式
      await handleGiftProducts();
    } else {
      // 普通点单模式 - 保持原有逻辑
      console.log(`[${instanceId}] 发送确认事件，商品数量:`, vm.state.cartItems.length);
      emit('confirm', vm.state.cartItems);

      // 关闭弹窗
      visible.value = false;
    }
  } catch (error) {
    console.error(`[${instanceId}] 确认处理失败:`, error);
    ElMessage.error('操作失败，请稍后重试');
  } finally {
    isConfirmProcessing.value = false;
  }
};

// 将购物车项转换为订单商品
const convertCartItemsToOrderProducts = (): OrderProductVO[] => {
  return vm.state.cartItems.map(item => ({
    id: '', // 新建时为空
    venueId: props.venueId,
    roomId: props.roomId,
    sessionId: props.sessionId,
    orderNo: '',
    productId: item.id,
    productName: item.name,
    flavors: item.flavors || '',
    unit: item.unit || '份',
    quantity: item.quantity,
    payPrice: 0, // 赠送商品支付价格为0
    originalPrice: item.currentPrice, // 使用当前价格作为原价
    payAmount: 0, // 赠送商品支付总金额为0
    originalAmount: item.currentPrice * item.quantity, // 原始总金额
    discountRate: 0, // 赠送相当于100%折扣
    reduceAmount: 0,
    freeAmount: item.currentPrice * item.quantity, // 全额免单
    mark: '',
    inPackageTag: '',
    src: '',
    ctime: 0,
    utime: 0,
    state: 0,
    version: 0
  }));
};

// 处理赠送商品逻辑
const handleGiftProducts = async () => {
  try {
    console.log(`[${instanceId}] 处理赠送商品逻辑`);

    // 验证必要参数
    if (!props.sessionId) {
      throw new Error('缺少场次ID');
    }
    if (!props.roomId) {
      throw new Error('缺少房间ID');
    }

    // 计算总金额
    const totalAmount = vm.computed.totalAmountInFen.value;

    // 构建订单商品列表
    const orderProductVOs = convertCartItemsToOrderProducts();

    // 构建赠送请求
    const giftParams = {
      memberId: props.memberId || '',
      orderProductVOs,
      originalAmount: totalAmount,
      payAmount: 0, // 赠送商品支付总金额为0
      roomId: props.roomId,
      sessionId: props.sessionId,
      operator: props.operator
    };

    console.log(`[${instanceId}] 赠送商品请求参数:`, giftParams);

    // 调用赠送API
    // @ts-ignore
    const response = await ProductApi.giftProduct(giftParams);

    if (response.code === 0) {
      const operationVO = response.data as SessionOperationVOVoOrderVO;
      console.log(`[${instanceId}] 赠送商品成功:`, operationVO.data);
      ElMessage.success('赠送商品成功');

      // 打印出品单和结账单
      

      // 发送赠送成功事件
      emit('gift-success', operationVO.data);

      // 关闭弹窗
      visible.value = false;
    } else {
      throw new Error(response.message || '赠送商品失败');
    }
  } catch (error: any) {
    console.error(`[${instanceId}] 赠送商品失败:`, error);
    ElMessage.error(error.message || '赠送商品失败，请稍后重试');
    throw error;
  }
};

// 取消按钮处理
const handleCancel = () => {
  console.log(`[${instanceId}] 取消按钮点击`);
  visible.value = false;
  emit('cancel');
};

// 初始化订单类型
const initializeOrderType = () => {
  // 根据当前场景设置订单类型
  if (vm.state.selectedRoom) {
    vm.state.orderType = 'room'; // 默认为包厢点单
  }
};

// 处理对象跨行
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  // 如果是详情行，则合并所有列
  if (row.isDetail) {
    if (columnIndex === 0) {
      return {
        colspan: 4, // 合并所有4列（商品名称、数量、单价、金额）
        rowspan: 1
      };
    } else {
      return {
        colspan: 0,
        rowspan: 0
      };
    }
  }
};

// 类型定义：扩展购物车项目
interface ExtendedCartItem extends ICartItem {
  isDetail?: boolean;
  parentId?: string;
  packageProducts?: any[];
  isOptionalGroup?: boolean; // 是否是可选组
  optionalProducts?: any[]; // 可选组中的商品列表
  optionType?: string; // 可选组类型
  optionCount?: number; // 可选组商品数量
  selectedQuantity?: number; // 已选择数量
  isFree?: boolean; // 是否是免费商品
  details?: string; // 详情文本
}

// 处理购物车数据
const processedCartItems = ref<ExtendedCartItem[]>([]);

// 监听购物车数据变化，添加明细行
watch(
  () => vm.state.cartItems,
  newItems => {
    const processed: ExtendedCartItem[] = [];

    // 遍历购物车中的每个项目，过滤掉数量为0或负数的商品
    newItems
      .filter(item => item.quantity > 0) // 只处理数量大于0的商品
      .forEach(item => {
        // 添加原始项目
        processed.push(item as ExtendedCartItem);

        // 如果是套餐，则添加一个明细行
        if (item.isPackage) {
          processed.push({
            ...item, // 先复制所有属性以确保类型兼容性
            id: `detail-${item.id}`,
            name: item.name,
            currentPrice: 0,
            quantity: 0,
            unit: item.unit, // 确保包含必需属性
            isPackage: false,
            isDetail: true,
            parentId: item.id
          });
        }

        // 如果是可选组商品，也添加一个明细行
        if ((item as ExtendedCartItem).isOptionalGroup) {
          const optionalGroup = item as ExtendedCartItem;
          processed.push({
            ...optionalGroup,
            id: `detail-${optionalGroup.id}`,
            name: optionalGroup.name,
            currentPrice: 0,
            quantity: 0,
            unit: optionalGroup.unit || '组',
            isOptionalGroup: false, // 明细行不是可选组
            isDetail: true,
            parentId: optionalGroup.id
          });
        }
      });

    processedCartItems.value = processed;
    console.log(`[${instanceId}] 处理后的购物车商品数量:`, processed.length);
  },
  { immediate: true, deep: true }
);

// 监听购物车变化，自动清理无效商品
watch(
  () => vm.state.cartItems.length,
  (newLength, oldLength) => {
    console.log(`[${instanceId}] 购物车商品数量变化: ${oldLength} -> ${newLength}`);

    // 检查是否有数量为0或负数的商品需要清理
    const invalidItems = vm.state.cartItems.filter(item => item.quantity <= 0);
    if (invalidItems.length > 0) {
      console.log(`[${instanceId}] 发现 ${invalidItems.length} 个无效商品，自动清理`);
      cleanupCartItems();
    }
  }
);

// 根据父项ID查找对应的购物车项目
const findParentItem = (parentId: string): ICartItem | undefined => {
  return vm.state.cartItems.find(item => item.id === parentId);
};

// 清理购物车中数量为0的商品
const cleanupCartItems = () => {
  const originalLength = vm.state.cartItems.length;
  vm.state.cartItems = vm.state.cartItems.filter(item => item.quantity > 0);
  const newLength = vm.state.cartItems.length;

  if (originalLength !== newLength) {
    console.log(`[${instanceId}] 清理购物车，移除了 ${originalLength - newLength} 个数量为0的商品`);
  }
};

// 创建防抖的数量变更处理函数
const debouncedQuantityChange = debounce((newValue: number, item: ICartItem) => {
  handleQuantityChange(newValue, item);
}, 100); // 100ms防抖

// 处理数量变更
const handleQuantityChange = (newValue: number, item: ICartItem) => {
  console.log(`[${instanceId}] 数量变更:`, item.name, `${item.quantity} -> ${newValue}`);

  // 防止无效输入
  if (typeof newValue !== 'number' || isNaN(newValue)) {
    console.warn(`[${instanceId}] 无效的数量值:`, newValue);
    return;
  }

  // 当数量为0时，从购物车中移除该商品
  if (newValue === 0) {
    console.log(`[${instanceId}] 商品数量为0，移除商品:`, item.name);
    // 直接从购物车数组中移除该商品
    const index = vm.state.cartItems.findIndex(cartItem => cartItem.id === item.id);
    if (index !== -1) {
      vm.state.cartItems.splice(index, 1);
      console.log(`[${instanceId}] 已移除商品:`, item.name, '剩余商品数量:', vm.state.cartItems.length);
    }
    return;
  }

  // 当数量小于0时，设置为0并移除
  if (newValue < 0) {
    console.log(`[${instanceId}] 商品数量为负数，设置为0并移除:`, item.name);
    // 先设置为0，然后移除
    const cartItem = vm.state.cartItems.find(cartItem => cartItem.id === item.id);
    if (cartItem) {
      cartItem.quantity = 0;
      // 延迟移除，让用户看到数量变为0的过程
      setTimeout(() => {
        const index = vm.state.cartItems.findIndex(cartItem => cartItem.id === item.id);
        if (index !== -1) {
          vm.state.cartItems.splice(index, 1);
          console.log(`[${instanceId}] 已移除负数商品:`, item.name);
        }
      }, 100);
    }
    return;
  }

  // 确保newValue是正整数
  const validQuantity = Math.max(0, Math.floor(newValue));

  // 查找购物车中对应的商品并更新数量
  const cartItem = vm.state.cartItems.find(cartItem => cartItem.id === item.id);
  if (cartItem) {
    // 避免重复更新相同的数量
    if (cartItem.quantity === validQuantity) {
      console.log(`[${instanceId}] 数量未变化，跳过更新:`, cartItem.name, validQuantity);
      return;
    }

    cartItem.quantity = validQuantity;
    console.log(`[${instanceId}] 更新商品数量:`, cartItem.name, validQuantity);

    // 如果是可选组商品，更新其details属性以重新生成明细文本
    if ((cartItem as ExtendedCartItem).isOptionalGroup) {
      updateOptionalGroupDetails(cartItem as ExtendedCartItem);
    }
  } else {
    console.warn(`[${instanceId}] 未找到要更新的商品:`, item.name);
  }

  // 清理购物车中可能存在的数量为0的商品
  cleanupCartItems();
};

// 添加更新可选组明细的函数
const updateOptionalGroupDetails = (item: ExtendedCartItem) => {
  if (!item.isOptionalGroup || !item.optionalProducts) return;

  // 获取已选择的商品
  const selectedProducts = item.optionalProducts.filter(product => (product.quantity || 0) > 0);

  if (selectedProducts.length === 0) {
    item.details = '未选择任何商品';
    return;
  }

  // 生成明细文本
  const detailItems = selectedProducts.map(product => `${product.name}×${product.quantity}`);
  item.details = detailItems.join('，');

  // 更新已选数量
  item.selectedQuantity = selectedProducts.reduce((sum, product) => sum + (product.quantity || 0), 0);

  console.log(`[${instanceId}] 更新可选组明细:`, item.name, item.details);
};

// 购物车行类名
const cartItemRowClassName = ({ row }: { row: ExtendedCartItem }) => {
  if (row.isDetail) {
    return 'is-detail-row';
  }
  if (row.isPackage) {
    return 'is-package';
  }
  return '';
};

// 获取套餐明细显示文本
const getPackageDetailsText = (parentId: string): string => {
  const parent = findParentItem(parentId);
  return parent?.packageDetail?.detailString || '';
};
</script>

<style scoped>
:deep(.el-table .cell) {
  padding: 0 !important;
}

:deep(.cart-table .el-table__header tr th.el-table__cell) {
  background-color: #f3f3f3 !important;
}

/* 购物车卡片样式 */
:deep(.cart-card) {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

:deep(.cart-card .el-card__header) {
  padding: 16px !important;
  border-bottom: 1px solid #ebeef5 !important;
  flex-shrink: 0 !important;
}

:deep(.cart-card .el-card__body) {
  flex: 1 !important;
  padding: 0 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 表格样式 */
:deep(.cart-table) {
  flex: 1 !important;
}

:deep(.cart-table .el-table__inner-wrapper) {
  height: 100% !important;
}

:deep(.cart-table .el-table__body-wrapper) {
  overflow-y: auto !important;
}

:deep(.cart-table .el-table__header-wrapper) {
  flex-shrink: 0 !important;
}

:deep(.cart-table .el-table__empty-block) {
  height: 100% !important;
}

/* 确保小计区域固定在底部 */
:deep(.cart-card) .p-3 {
  flex-shrink: 0 !important;
  background: #fff !important;
  border-top: 1px solid #ebeef5 !important;
}

/* 修复商品表格布局 */
:deep(.product-table) {
  height: 100% !important;
}

:deep(.product-table .el-table__inner-wrapper) {
  height: 100% !important;
}

:deep(.product-table .el-table__body-wrapper) {
  overflow-y: auto !important;
  flex: 1 !important;
}

/* 修复左侧面板内容区域 */
:deep(.left-panel .panel-content) {
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}
</style>
