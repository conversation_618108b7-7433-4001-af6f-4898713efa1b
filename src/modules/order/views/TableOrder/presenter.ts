import { computed, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { IOrderViewModel, IOrderState, IOrderComputed, IOrderActions } from './viewmodel';
import { OrderTabType } from './viewmodel';
import { OrderConverter } from './converter';
import { OrderInteractor } from './interactor';
import type { SessionVO } from '@/types/projectobj';
import { ElMessage } from 'element-plus';
import { DialogManager } from '@/utils/dialog';
import { useVenueStore } from '@/stores/venueStore';

export class OrderPresenter implements IOrderViewModel {
  private router = useRouter();
  private interactor = new OrderInteractor();
  private tableListRef = ref();
  private venueStore = useVenueStore();

  // 保存所有订单数据，避免重复请求
  private allOrders = ref<SessionVO[]>([]);
  private lastFetchTime = ref<number>(0);
  private isInitialLoad = ref<boolean>(true);
  private isDataLoading = ref<boolean>(false); // 新增标记，防止重复请求

  // 状态
  public state: IOrderState = reactive({
    searchForm: {
      orderNo: '',
      roomId: '',
      pageNum: 1,
      pageSize: 100, // 修改默认pageSize为100，与组件保持一致
      startTime: this.calculateStartTime(new Date(Date.now() - 24 * 60 * 60 * 1000)),
      endTime: this.calculateEndTime(new Date(Date.now()))
    },
    dateRange: [new Date(Date.now() - 24 * 60 * 60 * 1000), new Date(Date.now())],
    activeTab: OrderTabType.ALL,
    loading: false
  });

  // 计算属性
  public computed: IOrderComputed = {
    payStatusOptions: computed(() => {
      return this.interactor.getPayStatusOptions();
    }),
    roomList: computed(() => {
      return this.interactor.getRoomList();
    })
  };

  // 计算开始时间戳（基于营业开始时间）
  private calculateStartTime(date: Date): number {
    // 获取营业开始时间
    const openTime = this.venueStore.businessHours.openTime || '00:00';
    const [hours, minutes] = openTime.split(':').map(Number);

    console.log('businessHours 营业开始时间:', openTime);
    // 设置日期的时分秒
    const startDate = new Date(date);
    startDate.setHours(hours, minutes, 0, 0);

    return Math.floor(startDate.getTime() / 1000);
  }

  // 计算结束时间戳（基于营业开始时间的下一天）
  private calculateEndTime(date: Date): number {
    // 获取营业开始时间
    const openTime = this.venueStore.businessHours.openTime || '00:00';
    const [hours, minutes] = openTime.split(':').map(Number);

    // 设置为下一天的营业开始时间（减1秒）
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);
    endDate.setHours(hours, minutes, 0, 0);

    return Math.floor(endDate.getTime() / 1000) - 1;
  }

  // 动作
  public actions: IOrderActions = {
    // 提交表单
    onSubmit: () => {
      // 尝试本地搜索
      this.filterOrdersAndUpdateTable();
    },

    // 重置表单
    handleReset: () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const today = new Date();

      Object.assign(this.state.searchForm, {
        orderNo: '',
        roomId: '',
        pageNum: 1,
        pageSize: 100, // 修改默认pageSize为100，与组件保持一致
        startTime: this.calculateStartTime(yesterday),
        endTime: this.calculateEndTime(today)
      });

      this.state.dateRange = [yesterday, today];

      // 重置后需要重新请求数据
      this.fetchOrderData();
    },

    // 切换标签 (本地过滤，不发起API请求)
    handleTabChange: (tab: OrderTabType) => {
      this.state.activeTab = tab;

      // 更新搜索条件
      const oldPayStatus = this.state.searchForm.payStatus;
      this.state.searchForm = OrderConverter.updateSearchFormByTab(tab, this.state.searchForm);

      // 添加日志，帮助调试
      console.log('标签切换:', {
        tab,
        oldPayStatus,
        newPayStatus: this.state.searchForm.payStatus,
        totalOrders: this.allOrders.value.length
      });

      // 使用本地数据过滤
      this.filterOrdersAndUpdateTable();
    },

    // 日期范围变更
    handleDateRangeChange: (val: Date[] | null) => {
      if (!val) return;
      const [start, end] = val;

      // 保存旧的时间范围
      const oldStartTime = this.state.searchForm.startTime;
      const oldEndTime = this.state.searchForm.endTime;

      // 更新时间范围（根据营业时间计算）
      this.state.searchForm.startTime = this.calculateStartTime(start);
      this.state.searchForm.endTime = this.calculateEndTime(end);

      // 只有时间范围变化时才重新发起请求
      if (oldStartTime !== this.state.searchForm.startTime || oldEndTime !== this.state.searchForm.endTime) {
        // 日期变更需要重新拉取数据
        this.fetchOrderData();
      }
    },

    // 查看订单详情
    handleViewDetails: (record: SessionVO) => {
      if (!record?.id) {
        ElMessage.warning('无效的订单ID');
        return;
      }

      this.router.push({
        name: 'order-room-detail',
        query: { orderId: record.sessionId }
      });
    },

    // 处理退单操作
    handleRefund: (record: SessionVO) => {
      if (!record?.id || !record?.roomId) {
        ElMessage.warning('请先选择有效的订单');
        return;
      }

      // 使用DialogManager替代路由跳转
      DialogManager.open('RoomRefundDialog', {
        sessionId: record.sessionId,
        roomId: record.roomId
      })
        .then(result => {
          // 处理退单结果
          if (result && result.success) {
            console.log('退单成功，退款金额:', (result.totalAmount / 100).toFixed(2));
            // 刷新列表数据
            this.fetchOrderData();
          }
        })
        .catch(error => {
          console.error('退单操作取消或发生错误:', error);
        });
    }
  };

  // 设置表格引用
  setTableListRef(ref: any) {
    this.tableListRef.value = ref;

    // 首次设置引用后，加载初始数据
    if (this.isInitialLoad.value && !this.isDataLoading.value) {
      this.isInitialLoad.value = false;
      this.fetchOrderData(); // 只在首次设置ref且没有加载中才触发请求
    }
  }

  // 过滤订单并更新表格
  private filterOrdersAndUpdateTable() {
    const { orderNo, payStatus } = this.state.searchForm;
    let filtered = [...this.allOrders.value];

    console.log('开始筛选订单:', {
      payStatus,
      orderNo,
      总订单数: this.allOrders.value.length
    });

    // 根据支付状态筛选
    if (payStatus) {
      const before = filtered.length;
      filtered = filtered.filter(order => order.payStatus?.toUpperCase() === payStatus.toUpperCase());
      console.log(`支付状态筛选: ${payStatus}, 筛选前: ${before}, 筛选后: ${filtered.length}`);
    }

    // 根据搜索关键词筛选(优先匹配包厢名，如果匹配成功则不再匹配订单号)
    if (orderNo && orderNo.trim() !== '') {
      const keyword = orderNo.toLowerCase().trim();
      const before = filtered.length;

      // 首先尝试精确匹配包厢名
      const roomNameMatches = filtered.filter(order => order.roomVO?.name && order.roomVO.name.toLowerCase() === keyword);

      // 如果有精确匹配的包厢名，直接使用这些结果
      if (roomNameMatches.length > 0) {
        filtered = roomNameMatches;
        console.log(`关键词精确匹配包厢名: ${keyword}, 匹配结果: ${filtered.length}`);
      } else {
        // 尝试模糊匹配包厢名
        const roomNamePartialMatches = filtered.filter(order => order.roomVO?.name && order.roomVO.name.toLowerCase().includes(keyword));

        // 如果有包厢名模糊匹配结果，使用这些结果
        if (roomNamePartialMatches.length > 0) {
          filtered = roomNamePartialMatches;
          console.log(`关键词模糊匹配包厢名: ${keyword}, 匹配结果: ${filtered.length}`);
        } else {
          // 如果包厢名没有匹配结果，再尝试匹配订单号
          filtered = filtered.filter(order => order.sessionId && order.sessionId.toLowerCase().includes(keyword));
          console.log(`关键词匹配订单号: ${keyword}, 匹配结果: ${filtered.length}`);
        }
      }

      console.log(`关键词筛选: ${keyword}, 筛选前: ${before}, 筛选后: ${filtered.length}`);
    }

    // 更新表格数据 - 始终使用本地过滤结果而不是重新请求API
    if (this.tableListRef.value) {
      this.tableListRef.value.updateLocalData(filtered);
      console.log(`筛选完成，更新表格数据，显示记录数: ${filtered.length}`);
    }
  }

  // 获取订单数据
  private async fetchOrderData() {
    // 防止重复请求
    if (this.isDataLoading.value) {
      return;
    }

    this.state.loading = true;
    this.isDataLoading.value = true;

    try {
      const response = await this.interactor.getOrderList(this.state.searchForm);
      if (response.code === 0) {
        // 确保response.data是正确的格式
        // 根据API返回的数据结构，response.data可能是PageVOArrayVoOrderVO
        if (response.data && response.data.data) {
          // 保存所有订单数据
          this.allOrders.value = response.data.data || [];
          this.lastFetchTime.value = Date.now();

          // 更新表格数据 - 直接传递完整的响应对象
          if (this.tableListRef.value) {
            this.tableListRef.value.updateData({ data: response.data });
          }
        } else if (Array.isArray(response.data)) {
          // 兼容API可能直接返回数组的情况
          this.allOrders.value = response.data;
          this.lastFetchTime.value = Date.now();

          if (this.tableListRef.value) {
            this.tableListRef.value.updateData({
              data: {
                data: response.data,
                total: response.data.length
              }
            });
          }
        }
      }
    } catch (error) {
      console.error('获取订单数据失败:', error);
      ElMessage.error('获取订单数据失败');
    } finally {
      this.state.loading = false;
      this.isDataLoading.value = false;
    }
  }
}

// 导出组合式函数
export function useOrder(): IOrderViewModel & { setTableListRef: (ref: any) => void } {
  return new OrderPresenter();
}
