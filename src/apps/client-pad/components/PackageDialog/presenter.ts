/**
 * 套餐选择侧边栏组件的主控器
 */
import { ref, computed, reactive } from 'vue';
import { IPackageDialogViewModel, IPackageDialogActions, IProductItem, IOptionalGroup } from './viewmodel';
import { PackageDialogConverter } from './converter';
import { PackageDialogInteractor } from './interactor';

/**
 * 套餐选择侧边栏组件的组合式函数
 * @param emit 事件发射器，用于向父组件传递事件
 * @returns 套餐选择侧边栏组件的视图模型和操作
 */
export function usePackageDialog(emit?: any) {
  // 原始套餐数据
  const packageData = ref<any>(null);

  // 状态
  const state = reactive<IPackageDialogViewModel>({
    visible: false,
    packageId: '',
    packageName: '',
    packageImageUrl: '',
    packageQuantity: 1,
    fixedProducts: [],
    optionalGroups: [],
    selectedItemsText: '',
    totalPrice: 0,
    canConfirm: false
  });

  // 更新已选商品文本
  const updateSelectedItemsText = () => {
    state.selectedItemsText = PackageDialogConverter.generateSelectedItemsText(state.fixedProducts, state.optionalGroups);
  };

  // 更新总价
  const updateTotalPrice = () => {
    if (!packageData.value) return;

    state.totalPrice = PackageDialogConverter.calculateTotalPrice(packageData.value, state.packageQuantity);
  };

  // 检查是否可以确认
  const checkCanConfirm = () => {
    state.canConfirm = PackageDialogConverter.checkCanConfirm(state.optionalGroups);
  };

  // 操作
  const actions: IPackageDialogActions = {
    // 打开套餐选择对话框
    openDialog(product: any) {
      console.log('Opening dialog with product:', product);

      // 解析数据结构
      const productData = { ...product };

      // 判断是否有packageDetail字段
      if (productData.packageDetail) {
        console.log('Using packageDetail data structure');

        // 如果optionalGroups是字符串，尝试解析成对象
        if (typeof productData.packageDetail.optionalGroups === 'string') {
          try {
            console.log('Parsing optionalGroups string to JSON');
            productData.packageDetail.optionalGroups = JSON.parse(productData.packageDetail.optionalGroups);
            console.log('Parsed optionalGroups:', productData.packageDetail.optionalGroups);
          } catch (error) {
            console.error('Failed to parse optionalGroups:', error);
            productData.packageDetail.optionalGroups = [];
          }
        }

        // 如果packageProducts是字符串，尝试解析成对象
        if (typeof productData.packageDetail.packageProducts === 'string') {
          try {
            console.log('Parsing packageProducts string to JSON');
            productData.packageDetail.packageProducts = JSON.parse(productData.packageDetail.packageProducts);
            console.log('Parsed packageProducts:', productData.packageDetail.packageProducts);
          } catch (error) {
            console.error('Failed to parse packageProducts:', error);
            productData.packageDetail.packageProducts = [];
          }
        }

        // 保存处理后的数据
        packageData.value = productData;
      } else {
        // 没有packageDetail字段，直接使用原始数据
        packageData.value = productData;
      }

      // 重置状态
      state.visible = true;
      state.packageId = product.id || '';
      state.packageName = product.name || '';
      state.packageImageUrl = product.imageUrl || '';
      state.packageQuantity = 1;

      // 转换固定商品和可选组
      state.fixedProducts = PackageDialogConverter.convertToFixedProducts(packageData.value);
      state.optionalGroups = PackageDialogConverter.convertToOptionalGroups(packageData.value);

      // 更新UI状态
      updateSelectedItemsText();
      updateTotalPrice();
      checkCanConfirm();

      console.log('Dialog state initialized:', {
        fixedProducts: state.fixedProducts.length,
        optionalGroups: state.optionalGroups.length,
        canConfirm: state.canConfirm
      });
    },

    // 关闭套餐选择对话框
    closeDialog() {
      state.visible = false;
    },

    // 增加可选商品数量
    increaseProduct(groupIndex: number, productId: string) {
      const group = state.optionalGroups[groupIndex];
      if (!group) return;

      const product = group.products.find(p => p.id === productId);
      if (!product) return;

      // 检查限制
      if ((group.optionType === 'by_count' || group.optionType === 'by_plan') && group.selectedCount >= group.optionCount) {
        // 如果已达到限制，则不能再增加
        return;
      }

      // 增加数量
      product.quantity += 1;
      group.selectedCount += 1;

      // 更新状态
      updateSelectedItemsText();
      checkCanConfirm();
    },

    // 减少可选商品数量
    decreaseProduct(groupIndex: number, productId: string) {
      const group = state.optionalGroups[groupIndex];
      if (!group) return;

      const product = group.products.find(p => p.id === productId);
      if (!product || product.quantity <= 0) return;

      // 减少数量
      product.quantity -= 1;
      group.selectedCount -= 1;

      // 更新状态
      updateSelectedItemsText();
      checkCanConfirm();
    },

    // 增加套餐数量
    increasePackageQuantity() {
      state.packageQuantity += 1;
      updateTotalPrice();
    },

    // 减少套餐数量
    decreasePackageQuantity() {
      if (state.packageQuantity > 1) {
        state.packageQuantity -= 1;
        updateTotalPrice();
      }
    },

    // 确认选择
    async confirmSelection() {
      if (!state.canConfirm || !packageData.value) return;

      // 构建完整的套餐数据，确保包含所有必要字段
      const completePackageData = {
        ...packageData.value,
        id: state.packageId,
        name: state.packageName,
        imageUrl: state.packageImageUrl,
        image: state.packageImageUrl, // 兼容两种图片字段
        price: state.totalPrice,
        currentPrice: state.totalPrice, // 添加currentPrice字段以保持一致
        unit: packageData.value.unit || '套'
      };

      console.log('Complete package data:', completePackageData);

      // 调用交互器创建订单项
      const packageSelections = await PackageDialogInteractor.createOrderItem(
        completePackageData,
        state.fixedProducts,
        state.optionalGroups,
        state.packageQuantity
      );

      // 触发确认事件，将数据传递给父组件
      if (emit) {
        console.log('Emitting confirm event with data:', packageSelections);
        emit('confirm', packageSelections);
      } else {
        console.warn('No emit function provided, cannot send data to parent component');
      }

      // 关闭对话框
      state.visible = false;
    }
  };

  return {
    state,
    actions
  };
}
