<template>
  <el-dialog v-model="visible" title="系统初始化设置" :close-on-click-modal="true" :close-on-press-escape="true" :show-close="true" width="500px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="场馆ID" prop="venueId">
        <el-input v-model="form.venueId" placeholder="请输入场馆ID" clearable>
          <template #append>
            <el-button @click="refreshVenueId">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <!-- 可以在这里添加其他初始化设置项 -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ref, defineEmits, defineProps } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { useVenueStore } from '@/stores/venueStore';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const venueStore = useVenueStore();

const emit = defineEmits(['update:modelValue', 'confirmed']);

const visible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val)
});

const formRef = ref<FormInstance>();
const form = ref({
  venueId: venueStore.venueId || ''
});

const rules = {
  venueId: [
    { required: true, message: '请输入场馆ID', trigger: 'blur' },
    { min: 1, message: '场馆ID不能为空', trigger: 'blur' }
  ]
};

// 刷新场馆ID
const refreshVenueId = () => {
  form.value.venueId = '';
};

// 确认设置
const handleConfirm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(valid => {
    if (valid) {
      localStorage.setItem('venueId', form.value.venueId.trim());
      emit('confirmed', form.value);
      visible.value = false;
      ElMessage.success('设置成功');
    }
  });
};
</script>
