import { ref, reactive, computed } from 'vue';
import type { IEmployeeShiftViewModel, IEmployeeShiftState, IEmployeeShiftComputed, IEmployeeShiftActions, IBillInfo } from './viewmodel';
import { EmployeeShiftConverter } from './converter';
import { getShiftReportDaily } from '../../api/employeeShift';
import { ElMessage } from 'element-plus';
import { now10 } from '@/utils/dateUtils';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { EmployeeShiftInteractor } from './interactor';
import { useVenueStore } from '@/stores/venueStore';
import { DialogManager } from '@/utils/dialog';
// 支付类型数据接口
interface PayTypeData {
  alipay: number;
  bank: number;
  cash: number;
  koubei: number;
  meituan: number;
  other: number;
  ticket: number;
  wechat: number;
  leshua: number; // 扫码支付项
  [key: string]: number; // 添加索引签名
}

// 会员卡支付项目接口
interface MemberPaymentItem {
  name: string; // 支付方式名称
  value: number; // 金额
}

export class EmployeeShiftPresenter implements IEmployeeShiftViewModel {
  // 内部状态
  private _state = reactive<IEmployeeShiftState>({
    date: now10(), // 当前时间戳（10位）
    employeeId: '',
    employeeName: '',
    venueId: '',
    loading: false,
    shiftReportData: null,
    aggregatedShiftReport: null,
    shiftId: '', // 交班单ID，新增
    handNo: '' // 交班单号，新增
  });

  constructor(shiftId?: string, handNo?: string) {
    // 初始化时获取用户信息和场馆信息
    const userStore = useUserStore();
    const venueStore = useVenueStore();

    // 设置员工ID和名称
    if (userStore.userInfo?.employee?.id) {
      this._state.employeeId = userStore.userInfo.employee.id;
      this._state.employeeName = userStore.userInfo.employee.name || '';
    }

    // 设置场馆ID
    if (venueStore.venueId) {
      this._state.venueId = venueStore.venueId;
    }

    // 如果传入了交班单ID，则设置
    if (shiftId) {
      this._state.shiftId = shiftId;
    }

    // 如果传入了交班单号，则设置
    if (handNo) {
      this._state.handNo = handNo;
    }
  }

  // 暴露只读状态
  public get state(): IEmployeeShiftState {
    return this._state;
  }

  // 计算属性
  public computed: IEmployeeShiftComputed = {
    // 交班时间范围显示
    shiftTimeRange: computed(() => {
      if (!this._state.aggregatedShiftReport) {
        return '';
      }
      return EmployeeShiftConverter.formatShiftTimeRange(this._state.aggregatedShiftReport.startTime, this._state.aggregatedShiftReport.endTime);
    }),

    // 财务数据计算属性
    totalIncome: computed(() => {
      console.log('[presenter totalIncome] this._state.aggregatedShiftReport:', this._state.aggregatedShiftReport);
      return this._state.aggregatedShiftReport?.businessOverview.totalFee || 0;
    }),

    cashIncome: computed(() => {
      return this._state.aggregatedShiftReport?.payTypeData.cash || 0;
    }),

    onlineIncome: computed(() => {
      if (!this._state.aggregatedShiftReport) {
        return 0;
      }
      return EmployeeShiftConverter.calculateOnlineIncome(this._state.aggregatedShiftReport.payTypeData);
    }),

    bankCardIncome: computed(() => {
      return this._state.aggregatedShiftReport?.payTypeData.bank || 0;
    }),

    otherIncome: computed(() => {
      return this._state.aggregatedShiftReport?.payTypeData.other || 0;
    }),

    // 折扣数据计算属性
    totalDiscount: computed(() => {
      if (!this._state.aggregatedShiftReport) {
        return 0;
      }
      return EmployeeShiftConverter.calculateTotalDiscount(this._state.aggregatedShiftReport.businessOverview);
    }),

    // 业务数据计算属性
    tableCount: computed(() => {
      return this._state.aggregatedShiftReport?.businessData.openCount || 0;
    }),

    orderCount: computed(() => {
      return (this._state.aggregatedShiftReport?.businessData.billCount || 0) + (this._state.aggregatedShiftReport?.businessData.orderUnpaidCount || 0);
    }),

    paidBillCount: computed(() => {
      return this._state.aggregatedShiftReport?.businessData.billCount || 0;
    }),

    unpaidOrderCount: computed(() => {
      return this._state.aggregatedShiftReport?.businessData.orderUnpaidCount || 0;
    }),
    // 是否有数据
    hasData: computed(() => {
      return !!this._state.aggregatedShiftReport;
    }),

    // 支付方式柱状图相关数据 - 新增
    maxNormalPayment: computed(() => {
      // 获取最大支付额作为100%宽度基准
      const payTypeData = this._state.aggregatedShiftReport?.payTypeData || ({} as PayTypeData);
      const max =
        Math.max(
          payTypeData.cash || 0,
          payTypeData.bank || 0,
          payTypeData.wechat || 0,
          payTypeData.alipay || 0,
          payTypeData.meituan || 0,
          payTypeData.koubei || 0,
          payTypeData.ticket || 0,
          payTypeData.leshua || 0 // 扫码支付项
        ) || 1; // 确保至少为1，避免除以0或undefined
      console.log('[presenter maxNormalPayment] max:', max);
      return max;
    }),

    totalNormalPayment: computed(() => {
      const payTypeData = this._state.aggregatedShiftReport?.payTypeData || ({} as PayTypeData);
      console.log('[presenter totalNormalPayment] payTypeData:', payTypeData);
      const sumPay =
        (payTypeData.cash || 0) +
        (payTypeData.bank || 0) +
        (payTypeData.wechat || 0) +
        (payTypeData.alipay || 0) +
        (payTypeData.meituan || 0) +
        (payTypeData.koubei || 0) +
        (payTypeData.ticket || 0) +
        (payTypeData.leshua || 0); // 确保包含扫码支付
      console.log('[presenter totalNormalPayment] sumPay:', sumPay);
      return sumPay;
    }),

    normalPaymentItems: computed(() => {
      const payTypeData = this._state.aggregatedShiftReport?.payTypeData || ({} as PayTypeData);

      // 按照指定顺序(cash、leshua、wechat、bank、alipay、other)返回支付方式数据
      const orderedItems: Record<string, number> = {
        cash: payTypeData.cash || 0,
        leshua: payTypeData.leshua || 0, // 扫码支付项
        wechat: payTypeData.wechat || 0,
        alipay: payTypeData.alipay || 0,
        bank: payTypeData.bank || 0,
        other: payTypeData.other || 0
      };

      return orderedItems;
    }),

    // 会员支付相关数据 - 更新为使用真实数据
    maxMemberPayment: computed(() => {
      if (!this._state.aggregatedShiftReport?.memberCardPayData) {
        return 1; // 默认最小值为1，避免除以0
      }

      // 取会员卡各项支付中的最大值
      const memberCardPayData = this._state.aggregatedShiftReport.memberCardPayData;
      const max =
        Math.max(
          memberCardPayData.principalAmount || 0,
          memberCardPayData.roomBonusAmount || 0,
          memberCardPayData.goodsBonusAmount || 0,
          memberCardPayData.commonBonusAmount || 0
        ) || 1; // 确保至少为1

      console.log('[presenter maxMemberPayment] max:', max);
      return max;
    }),

    totalMemberPayment: computed(() => {
      if (!this._state.aggregatedShiftReport?.memberCardPayData) {
        return 0;
      }

      // 计算会员卡支付总额
      return EmployeeShiftConverter.calculateTotalMemberPayment(this._state.aggregatedShiftReport.memberCardPayData);
    }),

    memberPaymentItems: computed(() => {
      if (!this._state.aggregatedShiftReport?.memberCardPayData) {
        return [
          { name: '本金支付', value: 0 },
          { name: '通用额度', value: 0 },
          { name: '包厢额度', value: 0 },
          { name: '商品额度', value: 0 }
        ];
      }

      // 使用真实的会员卡支付数据
      const memberCardPayData = this._state.aggregatedShiftReport.memberCardPayData;
      return [
        { name: '本金支付', value: memberCardPayData.principalAmount || 0 },
        { name: '通用额度', value: memberCardPayData.commonBonusAmount || 0 },
        { name: '包厢额度', value: memberCardPayData.roomBonusAmount || 0 },
        { name: '商品额度', value: memberCardPayData.goodsBonusAmount || 0 }
      ];
    }),

    // 会员充值相关数据 - 新增
    memberRechargeAmount: computed(() => {
      return this._state.aggregatedShiftReport?.memberCardRechargeData?.rechargeAmount || 0;
    })
  };

  // 动作实现
  public actions: IEmployeeShiftActions = {
    // 查询交班报告
    queryShiftReport: async () => {
      // 如果有交班单ID，则优先使用交班单ID查询
      if (this._state.shiftId) {
        // 使用交班单ID和交班单号进行查询
        console.log('通过ID和交班单号查询:', this._state.shiftId, this._state.handNo);
        await this.queryShiftReportById(this._state.shiftId, this._state.handNo);
        return;
      }

      if (!this._state.employeeId || !this._state.venueId) {
        ElMessage.warning('无法获取员工或场馆信息');
        return;
      }

      this._state.loading = true;
      try {
        console.log('查询参数:', {
          date: this._state.date,
          employeeId: this._state.employeeId,
          venueId: this._state.venueId
        });

        const response = await getShiftReportDaily({
          date: this._state.date,
          employeeId: this._state.employeeId,
          venueId: this._state.venueId
        });

        console.log('API返回数据:', response);

        // 确保我们有正确的响应结构
        if (response && Array.isArray(response) && response.length > 0) {
          // console.log('获取到交班数据:', response);
          this._state.shiftReportData = response;
          const aggregatedData = EmployeeShiftConverter.aggregateShiftReports(response);
          console.log('聚合后的数据:', aggregatedData);
          this._state.aggregatedShiftReport = aggregatedData;

          // 如果聚合后的数据为null，可能是聚合出错
          if (!this._state.aggregatedShiftReport) {
            console.warn('聚合交班数据失败，原始数据:', response.data);
            ElMessage.warning('处理交班数据时出错');
          }
        } else {
          console.warn('API返回的数据不符合预期:', response);
          this._state.shiftReportData = null;
          this._state.aggregatedShiftReport = null;
          ElMessage.info('没有查询到交班数据');
        }
      } catch (error) {
        console.error('查询交班报告失败:', error);
        ElMessage.error('查询交班报告失败');
        this._state.shiftReportData = null;
        this._state.aggregatedShiftReport = null;
      } finally {
        this._state.loading = false;
      }
    },

    // 更改日期
    changeDate: (date: number) => {
      this._state.date = date;
    },

    // 更改员工
    changeEmployee: (employeeId: string, employeeName: string) => {
      this._state.employeeId = employeeId;
      this._state.employeeName = employeeName;
    },

    // 更改场馆
    changeVenue: (venueId: string) => {
      this._state.venueId = venueId;
    },

    // 打印交班报告
    printShiftReport: () => {
      if (!this._state.aggregatedShiftReport) {
        ElMessage.warning('没有可打印的交班数据');
        return;
      }

      // 这里可以实现打印逻辑，例如调用打印服务或生成PDF
      console.log('打印交班报告:', this._state.aggregatedShiftReport);
      ElMessage.success('正在打印交班报告');
    },

    // 导出交班报告
    exportShiftReport: () => {
      if (!this._state.aggregatedShiftReport) {
        ElMessage.warning('没有可导出的交班数据');
        return;
      }

      // 这里可以实现导出逻辑，例如生成Excel或CSV文件
      console.log('导出交班报告:', this._state.aggregatedShiftReport);
      ElMessage.success('正在导出交班报告');
    },

    // 提交交班上报
    submitHandover: async () => {
      // 检查是否有交班数据
      if (!this._state.shiftReportData || !this._state.aggregatedShiftReport) {
        console.log('[shift] 没有可上报的交班数据:', this._state.shiftReportData, this._state.aggregatedShiftReport);
        ElMessage.warning('没有可上报的交班数据');
        return;
      }

      try {
        this._state.loading = true;
        const response = await EmployeeShiftInteractor.submitShiftHandover(
          this._state.date,
          this._state.employeeId,
          this._state.venueId,
          this._state.shiftReportData
        );

        console.log('交班上报结果:', response);

        if (response && response.code === 0) {
          ElMessage.success('交班上报成功');
        } else {
          ElMessage.error(`交班上报失败: ${response?.message || '未知错误'}`);
        }
      } catch (error) {
        console.error('交班上报错误:', error);
        ElMessage.error('交班上报失败，请稍后重试');
      } finally {
        this._state.loading = false;
      }
    },

    // 查看账单详情
    checkBill: (bill: IBillInfo) => {
      console.log('[EmployeeShiftPresenter] 查看账单:', bill);

      // 保存当前会话ID，供打印功能使用
      this.setCurrentSessionId(bill.sessionId);

      // 使用 DialogManager 打开账单详情对话框
      DialogManager.open(
        'BillPayDialog',
        {
          sessionId: bill.sessionId,
          billId: bill.id // 传入账单ID
        },
        {
          print: (billIds: string[]) => {
            // 处理打印事件，调用打印方法
            this.actions.printBills(billIds);
          },
          error: (error: Error) => {
            console.error('[EmployeeShiftPresenter] 对话框错误:', error);
            ElMessage.error(error.message || '操作失败');
          },
          close: () => {
            console.log('[EmployeeShiftPresenter] 对话框关闭');
          }
        }
      );
    },

    // 打印账单
    printBills: async (billIds: string[]) => {
      console.log('[EmployeeShiftPresenter] 开始打印账单:', billIds);

      if (!billIds || billIds.length === 0) {
        ElMessage.warning('没有可打印的账单');
        return;
      }

      try {
        // 获取当前会话ID（从最近打开的对话框中获取）
        // 这里需要从账单信息中获取sessionId，暂时使用空字符串
        // 在实际使用中，应该从账单详情对话框传递正确的sessionId
        const sessionId = this.getCurrentSessionId();

        if (!sessionId) {
          ElMessage.error('无法获取会话ID，打印失败');
          return;
        }

        // 调用interactor的打印方法
        await EmployeeShiftInteractor.printCheckoutBills(billIds, sessionId);

      } catch (error) {
        console.error('[EmployeeShiftPresenter] 打印账单失败:', error);
        ElMessage.error(error instanceof Error ? error.message : '打印失败');
      }
    }
  };

  // 当前会话ID管理
  private currentSessionId: string | null = null;

  /**
   * 设置当前会话ID
   * @param sessionId 会话ID
   */
  public setCurrentSessionId(sessionId: string): void {
    this.currentSessionId = sessionId;
  }

  /**
   * 获取当前会话ID
   * @returns 当前会话ID
   */
  private getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  // 通过交班单ID查询交班报告
  private async queryShiftReportById(shiftId: string, handNo: string): Promise<void> {
    if (!shiftId) {
      ElMessage.warning('交班单ID不能为空');
      return;
    }

    // 确保handNo有值，即使是空字符串
    const safeHandNo = handNo || '';

    this._state.loading = true;
    try {
      console.log('通过ID查询交班报告:', shiftId, '交班单号:', safeHandNo);

      const response = await EmployeeShiftInteractor.getShiftReportById(shiftId, safeHandNo);

      console.log('通过ID查询API返回数据:', response);

      // 确保我们有正确的响应结构
      if (response && Array.isArray(response) && response.length > 0) {
        console.log('获取到交班数据:', response);
        this._state.shiftReportData = response;
        const aggregatedData = EmployeeShiftConverter.aggregateShiftReports(response);
        console.log('聚合后的数据:', aggregatedData);
        this._state.aggregatedShiftReport = aggregatedData;

        // 如果聚合后的数据为null，可能是聚合出错
        if (!this._state.aggregatedShiftReport) {
          console.warn('聚合交班数据失败，原始数据:', response);
          ElMessage.warning('处理交班数据时出错');
        }
      } else {
        console.warn('API返回的数据不符合预期:', response);
        this._state.shiftReportData = null;
        this._state.aggregatedShiftReport = null;
        ElMessage.info('没有查询到交班数据');
      }
    } catch (error) {
      console.error('查询交班报告失败:', error);
      ElMessage.error('查询交班报告失败');
      this._state.shiftReportData = null;
      this._state.aggregatedShiftReport = null;
    } finally {
      this._state.loading = false;
    }
  }
}

// 导出组合式API函数
export function useEmployeeShift(shiftId?: string, handNo?: string): IEmployeeShiftViewModel {
  return new EmployeeShiftPresenter(shiftId, handNo);
}
