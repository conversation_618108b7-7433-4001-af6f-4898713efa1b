<template>
  <div class="product-order">
    <!-- 左侧分类栏 -->
    <div class="category-sidebar">
      <div class="header">
        <div class="btn-back" @click="router.back()">
          <el-icon class="text-[32px]"><ArrowLeftBold /></el-icon>
        </div>
        <h1 class="room-name">{{ state.roomName || '未选择包厢' }}</h1>
      </div>

      <div class="category-list">
        <div
          v-for="category in state.categories"
          :key="category.id"
          class="category-item"
          :class="{ active: category.isSelected }"
          @click="actions.selectCategory(category.id)">
          {{ category.name }}
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="right-content">
      <!-- 顶部导航栏 -->
      <div class="top-navbar">
        <NotificationButton @openNotificationCenter="showNotificationCenter" class="!w-[100px] !h-[100px] !text-[64px]" />

        <div class="search-container">
          <el-input v-model="searchText" placeholder="商品搜索" prefix-icon="el-icon-search" clearable @input="debouncedSearch"></el-input>
        </div>

        <div class="action-buttons">
          <div class="order-button" @click="navigateToOrders">
            <el-button class="btn-order">
              <OrderList class="text-[64px]" />
              <span>全部订单</span>
            </el-button>
          </div>

          <div class="order-button" @click="navigateToCart">
            <el-button class="btn-order">
              <el-badge
                :value="computed.totalItems.value > 0 ? computed.totalItems.value : ''"
                :hidden="computed.totalItems.value <= 0"
                class="cart-badge mr-[8px]">
                <Cart class="text-[64px]" />
              </el-badge>

              <span class="ml-[8px] border-l-[1px] border-l-gray-200 pl-[8px]">立即下单</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="product-list-container">
        <el-empty v-if="state.products.length === 0 && !state.loading" description="暂无商品"></el-empty>

        <el-skeleton v-if="state.loading" :rows="10" animated />

        <div v-else class="product-list">
          <div v-for="product in state.products" :key="product.id" class="product-item">
            <div class="product-image">
              <img :src="getProductImageUrl(product.imageUrl)" :alt="product.name" @error="e => handleImageError(e)" />
            </div>

            <div class="product-info flex flex-col justify-between">
              <div>
                <h3 class="product-name">{{ product.name }}</h3>

                <!-- 状态标签 -->
                <div v-if="product.status === 'sold_out'" class="product-tag sold-out">沽清</div>
                <div v-if="product.promotionTag" class="product-tag promotion">{{ product.promotionTag }}</div>
                <div v-if="product.isPromotion" class="product-tag promotion">推广</div>
              </div>
              <div class="flex items-center justify-between">
                <div class="product-price">
                  <span class="price-symbol">¥</span>
                  <span class="price-value">{{ (product.price / 100).toFixed(2) }}</span>
                  <span class="price-unit">/{{ product.unit }}</span>
                </div>

                <div class="product-actions">
                  <template v-if="product.status === 'sold_out'">
                    <div class="sold-out-btn">已沽清</div>
                  </template>
                  <template v-else-if="product.isPackage && hasOptionalGroups(product)">
                    <el-button class="btn-add select-spec" round @click="handleAddToCart(product)"> 选规格 </el-button>
                  </template>
                  <template v-else>
                    <div class="quantity-control">
                      <template v-if="getCartItem(product.id)">
                        <div class="with-quantity">
                          <div class="minus-btn" @click="handleDecrease(product.id)"></div>
                          <div class="quantity-display">{{ getCartItemQuantity(product.id) }}</div>
                          <div class="plus-btn" @click="handleAddToCart(product)"></div>
                        </div>
                      </template>
                      <template v-else>
                        <div class="add-btn" @click="handleAddToCart(product)"></div>
                      </template>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 套餐弹窗 -->
    <package-dialog ref="packageDialogRef" @confirm="handlePackageConfirm" />

    <!-- 购物车侧边栏 -->
    <cart-drawer
      ref="cartDrawerRef"
      :product-order-actions="{ state, ...actions }"
      :room-id="roomId"
      :session-id="sessionId"
      @order-confirmed="handleOrderConfirmed" />

    <!-- 订单侧边栏 -->
    <order-drawer ref="orderDrawerRef" :room-id="roomId" :session-id="sessionId" />

    <!-- 通知中心组件 -->
    <NotificationCenter ref="notificationCenterRef" />
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { useProductOrder } from './presenter';
import { ProductOrderConverter } from './converter';
import { ref, watch, nextTick, computed as vueComputed } from 'vue';
import { handleImageError, getProductImageUrl } from '@/utils/imageUtils';
import Cart from '@/assets/v3/cart.vue';
import { ArrowLeftBold } from '@element-plus/icons-vue';
import PackageDialog from '@/apps/client-pad/components/PackageDialog/index.vue';
import CartDrawer from '@/apps/client-pad/components/CartDrawer/index.vue';
import OrderDrawer from '@/apps/client-pad/components/OrderDrawer/index.vue';
import OrderList from '@/assets/v3/order-list.vue';
import NotificationButton from '@/apps/client-pad/components/NotificationButton/index.vue';
import NotificationCenter from '@/apps/client-pad/components/NotificationCenter/index.vue';

// 路由
const route = useRoute();
const router = useRouter();

// 获取路由参数
const roomId = (route.query.roomId as string) || '';
const roomName = (route.query.roomName as string) || '';
const sessionId = (route.query.sessionId as string) || '';

// 使用ViewModel
const { state, computed, actions } = useProductOrder({
  roomId,
  roomName,
  sessionId
});

// 套餐弹窗引用
const packageDialogRef = ref<InstanceType<typeof PackageDialog> | null>(null);

// 购物车抽屉引用
const cartDrawerRef = ref<InstanceType<typeof CartDrawer> | null>(null);

// 订单抽屉引用 - 使用any类型临时解决TypeScript类型检查问题
const orderDrawerRef = ref<any>(null);

// 通知中心引用
const notificationCenterRef = ref<any>(null);

// 搜索功能
const searchText = ref('');

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null;

// 带防抖的搜索方法
const debouncedSearch = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，300ms后执行搜索
  searchTimer = setTimeout(() => {
    handleSearch();
  }, 300);
};

// 监听清空操作
watch(searchText, newValue => {
  if (newValue === '') {
    // 当搜索框被清空时，立即执行搜索（不使用防抖）
    handleSearch();
  }
});

// 处理搜索
const handleSearch = () => {
  // 直接调用actions中的searchProducts方法
  actions.searchProducts(searchText.value);
};

// 跳转到订单页面
const navigateToOrders = () => {
  // 使用可选链来安全访问
  if (orderDrawerRef.value?.open) {
    orderDrawerRef.value.open();
  } else {
    console.error('OrderDrawer组件未正确加载或不包含open方法');
  }
};

// 打开购物车侧边栏
const navigateToCart = () => {
  if (cartDrawerRef.value) {
    cartDrawerRef.value.open();
  }
};

// 处理订单确认事件
const handleOrderConfirmed = () => {
  console.log('订单已确认');
  // 可以跳转到订单页面或其他操作
};

// 显示通知中心
const showNotificationCenter = () => {
  console.log('ProductOrder: 点击通知图标，notificationCenterRef:', notificationCenterRef.value);
  if (notificationCenterRef.value) {
    console.log('ProductOrder: 主动调用showDrawer方法');
    // 确保传递参数表明这是用户手动点击，显式传入true
    notificationCenterRef.value.showDrawer(true);
  } else {
    console.error('ProductOrder: 消息中心引用为空，无法调用showDrawer方法');
  }
};

// 立即检查包引用
console.log('ProductOrder component initializing');
nextTick(() => {
  console.log('PackageDialog reference check:', packageDialogRef.value);
});

// 检查商品是否有可选组
const hasOptionalGroups = (product: any): boolean => {
  return ProductOrderConverter.hasOptionalGroups(product);
};

// 获取购物车中的商品项
const getCartItem = (id: string) => {
  return state.cartItems.find(item => item.id === id);
};

// 获取购物车中的商品数量
const getCartItemQuantity = (id: string) => {
  const item = getCartItem(id);
  return item ? item.quantity : 0;
};

// 处理减少商品数量的逻辑
const handleDecrease = (id: string) => {
  const item = getCartItem(id);
  if (item) {
    actions.decreaseQuantity(item);
  }
};

// 重写addToCart方法，处理套餐情况
const handleAddToCart = (product: any) => {
  // 如果是套餐并且有可选项，则打开套餐选择对话框
  if (product.isPackage && hasOptionalGroups(product)) {
    console.log('Opening package dialog for:', product);

    // 检查并记录产品结构
    console.log(
      'Product structure:',
      JSON.stringify({
        id: product.id,
        name: product.name,
        packageDetail: {
          optionalGroups: product.packageDetail?.optionalGroups,
          productVOList: product.packageDetail?.productVOList?.length || 0
        }
      })
    );

    // 打开套餐选择对话框
    if (packageDialogRef.value) {
      console.log('Package dialog ref exists, opening...');
      packageDialogRef.value.open(product);
    } else {
      console.error('Package dialog ref is null or undefined');
    }
  } else {
    // 普通商品，直接添加到购物车
    actions.addToCart(product);
  }
};

// 处理套餐确认事件
const handlePackageConfirm = (packageSelections: any) => {
  console.log('Received package selections:', packageSelections);
  // 调用actions中的handlePackageConfirm方法将套餐添加到购物车
  actions.handlePackageConfirm(packageSelections);
};

// 在组件挂载后执行
watch(
  () => packageDialogRef.value,
  val => {
    if (val) {
      console.log('PackageDialog reference loaded:', val);
      console.log('PackageDialog reference check:', val);
    }
  },
  { immediate: true }
);

// 辅助函数：将分转换为元并格式化
const fenToYuanFormatted = (priceInFen: number) => {
  return (priceInFen / 100).toFixed(2);
};
</script>

<style scoped>
.product-order {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 左侧分类栏样式 */
.category-sidebar {
  width: 240px;
  height: 100%;
  background-color: #fff;
  border-right: 2px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.header {
  padding: 20px;
  display: flex;
  align-items: center;
}

.btn-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.room-name {
  font-size: 40px;
  font-weight: bold;
  margin-left: 8px;
}

.category-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 针对Webkit浏览器（Chrome/Safari）隐藏滚动条 */
.category-list::-webkit-scrollbar {
  display: none;
}
.category-item {
  height: 90px;
  margin: 0 30px 30px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 32px;
  cursor: pointer;
}

.category-item.active {
  background-color: #e23939;
  color: white;
}

.category-item:not(.active) {
  color: #999;
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 顶部导航栏样式 */
.top-navbar {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
}

.search-container {
  flex: 1;
  margin-right: 30px;
  margin-left: 30px;
}

.search-container :deep(.el-input__inner) {
  height: 100px;
  border-radius: 16px !important;
  font-size: 24px;
}

:deep(.el-input__wrapper) {
  border-radius: 16px !important;
}

.action-buttons {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.order-button {
  margin-right: 20px;
}

.btn-order,
.btn-cart {
  height: 100px;
  font-size: 32px;
  border-radius: 16px !important;
  white-space: nowrap;
  color: #000;
}

.btn-cart {
  background-color: #000;
  border-color: #000;
}

/* 商品列表样式 */
.product-list-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f3f3f3;
}

.product-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
}

.product-item {
  width: 100%;
  height: 200px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  position: relative;
}

.product-image {
  width: 180px;
  height: 180px;
  margin: 10px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
}

.product-info {
  flex: 1;
  padding: 14px 20px;
}

.product-name {
  font-size: 32px;
  font-weight: 400;
  margin-bottom: 10px;
}

.product-tag {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 10px;
  margin-right: 10px;
}

.product-tag.sold-out {
  background-color: rgba(105, 104, 104, 0.1);
  color: #666;
}

.product-tag.promotion {
  background-color: rgba(226, 57, 57, 0.1);
  color: #e23939;
}

.product-tag.promotion-ad {
  background-color: rgba(56, 107, 201, 0.1);
  color: #386bc9;
}

.product-price {
  font-weight: bold;
}

.price-symbol {
  font-size: 24px;
  color: #ff3333;
  font-family: 'PingFangSC-Semibold', sans-serif;
}

.price-value {
  font-size: 36px;
  color: #ff3333;
  font-family: 'PingFangSC-Semibold', sans-serif;
}

.price-unit {
  font-size: 24px;
  color: #ff3333;
  font-family: 'PingFangSC-Semibold', sans-serif;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.with-quantity {
  display: flex;
  align-items: center;
  background-color: #f3f3f3;
  border-radius: 80px;
  padding: 0;
  width: 184px;
  height: 72px;
  position: relative;
}

.minus-btn,
.plus-btn {
  width: 72px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 24px;
  font-weight: bold;
  color: #666;
  position: relative;
}

.minus-btn::before,
.plus-btn::before,
.plus-btn::after {
  content: '';
  position: absolute;
  background-color: #666;
  border-radius: 1px;
}

/* 横线 */
.minus-btn::before,
.plus-btn::before {
  width: 24px;
  height: 4px;
}

/* 加号的竖线 */
.plus-btn::after {
  width: 4px;
  height: 24px;
}

.quantity-display {
  flex: 1;
  font-size: 32px;
  font-weight: bold;
  color: #000;
  text-align: center;
  font-family: 'MiSans-Demibold', sans-serif;
}

.add-btn {
  width: 72px;
  height: 72px;
  background-color: #f3f3f3;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.add-btn::before,
.add-btn::after {
  content: '';
  position: absolute;
  background-color: #666;
  border-radius: 1px;
}

/* 横线 */
.add-btn::before {
  width: 24px;
  height: 4px;
}

/* 竖线 */
.add-btn::after {
  width: 4px;
  height: 24px;
}

.btn-add {
  width: 120px;
}

.btn-add.select-spec {
  width: 140px;
  height: 72px;
  border-radius: 80px !important;
  background-color: #f3f3f3;
  border: none;
  font-size: 28px;
  color: #666;
}

/* 购物车样式 */
.cart-summary {
  margin-right: 20px;
  display: flex;
  align-items: center;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #eee;
}

.cart-info {
  display: flex;
  align-items: center;
}

.cart-badge {
  display: flex;
  align-items: center;
}

.cart-badge :deep(.el-badge__content) {
  border: none;
  background-color: #e23939;
}

.cart-amount {
  margin-left: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #000;
}

.sold-out-btn {
  background-color: rgba(105, 104, 104, 0.1);
  color: #666;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: not-allowed;
}

:deep(.top-navbar .icon) {
  font-size: 48px !important;
}
</style>
