<template>
  <div class="semicircle-progress-container" ref="containerRef">
    <div class="semicircle-progress" :style="{ width: computedWidth + 'px', height: computedHeight + 'px' }">
      <!-- 进度条标题 -->
      <div class="progress-title">
        <slot name="title">{{ title }}</slot>
      </div>

      <!-- 中心内容 -->
      <div class="progress-center-content">
        <slot name="center-content"></slot>
      </div>

      <!-- SVG半圆形进度条 -->
      <svg
        :width="computedWidth"
        :height="computedArcHeight + computedStrokeOffset"
        :viewBox="`0 0 ${computedWidth} ${computedArcHeight + computedStrokeOffset}`"
        class="progress-arc">
        <!-- 背景弧线 -->
        <path :d="arcPath" :stroke="bgColor" :stroke-width="arcWidth" fill="none" stroke-linecap="round" />

        <!-- 进度弧线 -->
        <path
          :d="arcPath"
          :stroke="fillColor"
          :stroke-width="arcWidth1"
          fill="none"
          stroke-linecap="round"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="dashOffset" />
      </svg>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SemicircleProgressBar',
  props: {
    // 组件宽度
    width: {
      type: Number,
      default: 300
    },
    // 组件高度
    height: {
      type: Number,
      default: 260
    },
    // 圆弧宽度
    arcWidth: {
      type: Number,
      default: 8
    },
    // 圆弧宽度
    arcWidth1: {
      type: Number,
      default: 9
    },
    // 当前值
    currentValue: {
      type: Number,
      default: 0
    },
    // 目标值
    targetValue: {
      type: Number,
      default: 100
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 背景颜色
    bgColor: {
      type: String,
      default: '#f3f4f6' // gray-100
    },
    // 填充颜色
    fillColor: {
      type: String,
      default: '#3b82f6' // blue-500
    },
    // 是否响应式 (自动根据容器调整大小)
    responsive: {
      type: Boolean,
      default: true
    },
    // 圆弧偏移量
    strokeOffset: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      containerWidth: 0
    };
  },
  computed: {
    // 计算实际使用的宽度
    computedWidth() {
      if (this.responsive && this.containerWidth > 0) {
        // 使用容器宽度，但设置上限
        return Math.min(this.containerWidth, this.width);
      }
      return this.width;
    },
    // 计算高度，保持宽高比
    computedHeight() {
      const ratio = this.height / this.width;
      return Math.round(this.computedWidth * ratio);
    },
    // 半圆的高度
    computedArcHeight() {
      return this.computedWidth / 2;
    },
    // 计算偏移量
    computedStrokeOffset() {
      return Math.max(this.arcWidth, this.arcWidth1);
    },
    // 计算圆弧路径
    arcPath() {
      const width = this.computedWidth;
      const height = this.computedArcHeight;

      // 确保弧线不被裁切，考虑描边宽度
      const maxStrokeWidth = this.computedStrokeOffset;

      // 调整半径以适应描边宽度，使圆弧更大
      const radius = width / 2 - maxStrokeWidth;

      // 圆心位置
      const centerX = width / 2;
      // 将圆心下移，使半圆更完整
      const centerY = height + maxStrokeWidth;

      // 计算起点和终点，考虑描边宽度
      const startX = maxStrokeWidth;
      const endX = width - maxStrokeWidth;
      // 调整起点和终点的Y坐标，使弧形更圆滑
      const arcY = centerY - radius * Math.sin((Math.PI / 180) * 0);

      // 半圆弧线从左到右，使用圆心坐标
      return `M ${startX}, ${arcY} A ${radius} ${radius} 0 0 1 ${endX} ${arcY}`;
    },
    // 计算进度百分比
    progressPercentage() {
      // 如果目标值为0，或者当前值大于目标值，则显示100%
      if (this.targetValue === 0 || this.currentValue >= this.targetValue) {
        return 100;
      }
      // 否则计算百分比
      return (this.currentValue / this.targetValue) * 100;
    },
    // 计算圆弧总长度（用于stroke-dasharray）
    circumference() {
      // 与arcPath中使用相同的半径计算
      const maxStrokeWidth = this.computedStrokeOffset;
      const radius = this.computedWidth / 2 - maxStrokeWidth;
      // 半圆周长 = πr
      return Math.PI * radius;
    },
    // 计算stroke-dashoffset（控制进度显示）
    dashOffset() {
      // 计算应该隐藏的部分长度
      // 0% 进度 = 整个圆弧被隐藏 (dashOffset = circumference)
      // 100% 进度 = 整个圆弧显示 (dashOffset = 0)
      return this.circumference * (1 - this.progressPercentage / 100);
    }
  },
  mounted() {
    // 初始化容器宽度
    this.updateContainerWidth();

    // 监听窗口大小变化
    window.addEventListener('resize', this.updateContainerWidth);
  },
  beforeUnmount() {
    // 移除事件监听器
    window.removeEventListener('resize', this.updateContainerWidth);
  },
  methods: {
    // 更新容器宽度
    updateContainerWidth() {
      if (this.$refs.containerRef) {
        this.containerWidth = this.$refs.containerRef.offsetWidth;
      }
    }
  }
};
</script>

<style scoped>
.semicircle-progress-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  width: 100%;
}

.semicircle-progress {
  position: relative;
}

.progress-title {
  position: absolute;
  left: 0;
  right: 0;
  top: 32%;
  text-align: center;
  color: #6b7280; /* gray-500 */
  font-size: 16px; /* text-lg */
}

.progress-center-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-arc {
  position: absolute;
  top: 0;
  left: 0;
}
</style>
