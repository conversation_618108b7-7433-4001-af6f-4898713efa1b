<template>
  <AppDialog
    :uiType="DialogUIType.DEFAULT"
    v-model="dialogVisible"
    title="赠送商品"
    :close-on-click-modal="false"
    @close="handleCancel"
    class="h-[420px] !w-[620px]">
    <!-- 包厢信息 -->
    <div class="m-[24px] flex" v-if="roomName">
      <span class="text-gray-600 text-[20px] mb-[12px] w-[132px]">赠送包厢:</span>
      <span class="text-[20px] font-semibold">{{ roomName }}</span>
    </div>

    <!-- 赠送人选择 -->
    <div class="items-center mx-[24px] mt-[24px] flex">
      <span class="text-gray-600 text-[20px] w-[120px]">赠送人:</span>
      <div class="flex items-center">
        <el-select
          v-model="form.giftBy"
          placeholder="请选择赠送人"
          class="customer-select"
          :loading="salespersonLoading"
          @change="handleSalespersonChange"
          filterable
          remote
          :remote-method="searchSalesperson"
          clearable>
          <el-option v-for="item in salespersonOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </div>

    <!-- 当月可赠额度 -->
    <div class="m-[24px] flex items-center hidden">
      <span class="text-gray-500 text-sm w-[132px]">当月可赠额度:</span>
      <span class="text-red-500 font-semibold">-</span>
    </div>

    <template #footer>
      <div class="flex justify-center">
        <button class="btn-black" :loading="loading" @click="handleConfirm">确认</button>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { DialogUIType } from '@/types/dialog';
import { useUserStore } from '@/stores/userStore';
import { useEmployeeList } from '@/composables/useEmployeeList';

interface Props {
  visible: boolean;
  roomId?: string;
  roomName?: string;
  sessionId?: string;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: { giftBy: string; giftByName: string }): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  roomId: '',
  roomName: '',
  sessionId: ''
});

const emit = defineEmits<Emits>();
const userStore = useUserStore();

// 使用员工列表hooks
const {
  employees: salespersonOptions,
  loading: salespersonLoading,
  loadEmployees: loadSalespersonData,
  searchEmployees,
  selectEmployee,
  selectedEmployee
} = useEmployeeList({
  autoLoad: true,
  reviewStatus: 1 // 只显示已审核的员工
});

const loading = ref(false);
const maxAmount = ref(3000); // 默认值，实际应从API获取

const dialogVisible = computed({
  get: () => props.visible,
  set: val => emit('update:visible', val)
});

const form = ref({
  giftBy: '',
  giftByName: ''
});

// 获取当前用户信息作为默认值
const currentUser = computed(() => {
  const employee = userStore.userInfo.employee;
  return {
    id: employee?.id || userStore.userInfo.unionId || '',
    name: employee?.name || userStore.userInfo.name || ''
  };
});

// 销售员选择变更处理
const handleSalespersonChange = (value: string) => {
  const employee = salespersonOptions.value.find((e: any) => e.id === value);
  if (employee) {
    selectEmployee(employee);
    form.value.giftBy = employee.id;
    form.value.giftByName = employee.name;
  } else {
    form.value.giftBy = '';
    form.value.giftByName = '';
  }
};

// 搜索销售员
const searchSalesperson = async (query: string) => {
  if (query) {
    await searchEmployees(query);
  } else {
    // 如果搜索为空，重新加载所有员工
    await loadSalespersonData(true);
  }
};

// 初始化默认赠送人为当前用户
const initializeDefaultGiftBy = () => {
  const current = currentUser.value;
  if (current.id) {
    form.value.giftBy = current.id;
    form.value.giftByName = current.name;
  }
};

const handleConfirm = async () => {
  // 基本验证
  if (!form.value.giftBy) {
    ElMessage.warning('请选择赠送人');
    return;
  }

  if (!props.roomId || !props.sessionId) {
    ElMessage.warning('缺少必要参数');
    return;
  }

  loading.value = true;
  try {
    // 直接发送确认事件，由父组件处理后续逻辑
    emit('confirm', {
      giftBy: form.value.giftBy,
      giftByName: form.value.giftByName
    });
  } catch (error) {
    console.error('赠送商品确认失败:', error);
    ElMessage({
      message: '操作失败，请稍后重试',
      type: 'error',
      duration: 1500
    });
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  // 重置表单为默认值
  initializeDefaultGiftBy();
  emit('cancel');
};

// 监听对话框显示状态，初始化默认值
watch(
  () => props.visible,
  visible => {
    if (visible) {
      initializeDefaultGiftBy();
      // 确保员工数据已加载
      loadSalespersonData();
    }
  }
);
</script>
