// product相关类型定义
import { BatchWithdrawItem } from './other';
import { OrderVO, ProductionOrderDataVO } from './order';
import { PayBillVO } from './member';
import { PricePlanUnionVO } from './price';
import { UnifiedStorageItemDto } from './storage';

export type AddPointsExchangeReqDto = {
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 所需积分 */
    requiredPoints?: number;
  };

export type AddProductBindingReqDto = {
    /** 描述 */
    description?: string;
    /** 产品绑定名称 */
    name?: string;
    /** 产品列表 */
    productList?: string;
    /** 门店id */
    venueId?: string;
  };

export type AddProductDisplayCategoryReqDto = {
    /** 是否显示二级分类 */
    isDisplaySecond?: boolean;
    /** 分类名称 */
    name?: string;
    /** 关联的商品类型 */
    productTypes?: string;
  };

export type AddProductMultipleBuyFreeReqDto = {
    /** 购买数量 */
    buyCount?: number;
    /** 购买商品列表 */
    buyProducts?: string;
    /** 赠送商品示例 */
    exampleGiftProduct?: string;
    /** 赠送策略 */
    giftPolicy?: string;
    /** 是否可以重复购买 */
    isCanRepeatBuy?: boolean;
    /** 策略名称 */
    name?: string;
    /** 适用时间段 */
    timeSlots?: string;
    /** 门店id */
    venueId?: string;
  };

export type AddProductOutTypeReqDto = {
    /** 适用区域，JSON格式 */
    area?: string;
    /** 出库类型名称 */
    name?: string;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 适用商品类型，JSON格式 */
    productTypes?: string;
    /** 门店ID */
    venueId?: string;
    /** 出库仓库，JSON格式 */
    warehouse?: string;
  };

export type AddProductPackageReqDto = {
    /** 区域价格 */
    areaPrices?: string;
    /** 最低消费后可用 */
    availableAfterMinimumConsumption?: boolean;
    /** 条形码 */
    barcode?: string;
    /** 是否计算业绩 */
    calculatePerformance?: boolean;
    /** 类别 */
    category?: string;
    /** 消费赠送优惠券 */
    consumptionGiftCoupon?: string;
    /** 是否计入最低消费 */
    countInMinimumConsumption?: boolean;
    /** 当前价格 */
    currentPrice?: number;
    /** 部署区域 */
    deploymentAreas?: string;
    /** 部署渠道 */
    deploymentChannels?: string;
    /** 部署包厢类型 */
    deploymentRoomTypes?: string;
    /** 描述 */
    description?: string;
    /** 免费酒水模式 */
    freeDrinkMode?: boolean;
    /** 图片 */
    image?: string;
    /** 是否支持会员折扣 */
    isMemberDiscountable?: boolean;
    /** 是否上架 */
    isOnShelf?: boolean;
    /** 是否支持商家折扣 */
    isOrderDiscountable?: boolean;
    /** 是否支持商家减免 */
    isOrderReduceable?: boolean;
    /** 是否促销 */
    isPromoted?: boolean;
    /** 会员卡支付限制 */
    memberCardPaymentLimit?: string;
    /** 名称 */
    name?: string;
    /** 可选组 */
    optionalGroups?: string;
    /** 订单数量限制 */
    orderQuantityLimit?: number;
    /** 套餐产品 */
    packageProducts?: string;
    /** 系列 */
    series?: string;
    /** 上架时段 */
    shelfTimeSlots?: string;
    /** 员工赠送 */
    staffGift?: boolean;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 门店id */
    venueId?: string;
  };

export type AddProductPackageTypeReqDto = {
    /** 分销渠道 */
    distributionChannels?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 套餐类型名称 */
    name?: string;
    /** 是否支持积分 */
    supportPoints?: boolean;
    venueId?: string;
  };

export type AddProductReqDto = {
    /** 支持重复购买 */
    allowRepeatBuy?: boolean;
    /** 支持员工赠送 */
    allowStaffGift?: boolean;
    /** 支持存酒 */
    allowWineStorage?: boolean;
    /** 不同区域价格 */
    areaPrices?: string;
    /** 辅助公式 */
    auxiliaryFormula?: string;
    /** 条形码 */
    barcode?: string;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 计算库存 */
    calculateInventory?: boolean;
    /** 商品分类 */
    category?: string;
    /** 计入低消 */
    countToMinCharge?: boolean;
    /** 计算业绩 */
    countToPerformance?: boolean;
    /** 当前价格 */
    currentPrice?: number;
    /** 送达超时时间 */
    deliveryTimeout?: number;
    /** 商品介绍 */
    description?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 投放结束时间 */
    endTime?: string;
    /** 外送价格 */
    externalDeliveryPrice?: number;
    /** 商品口味 */
    flavors?: string;
    /** 消费赠券 */
    giftVoucher?: string;
    /** 商品图片 */
    image?: string;
    /** 辅料配方 */
    ingredients?: string;
    /** 指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否上架展示 */
    isDisplayed?: boolean;
    /** 是否支持会员折扣 */
    isMemberDiscountable?: boolean;
    /** 是否支持商家折扣 */
    isOrderDiscountable?: boolean;
    /** 是否支持商家减免 */
    isOrderReduceable?: boolean;
    /** 推广 */
    isPromotion?: boolean;
    /** 是否为实价产品 */
    isRealPriceProduct?: boolean;
    /** 指定投放包厢类型 */
    isRoomTypeSpecified?: boolean;
    /** 是否洁清 */
    isSoldOut?: boolean;
    /** 低库存数 */
    lowStockThreshold?: number;
    /** 会员卡结账限制 */
    memberCardLimits?: string;
    /** 会员卡支付限制 */
    memberCardPaymentRestrictions?: string;
    /** 最小销售数量 */
    minimumSaleQuantity?: number;
    /** 产品名称 */
    name?: string;
    /** 支付标签 */
    payMark?: string;
    /** 原价 */
    price?: number;
    /** 推荐搭配 */
    recommendCombos?: string;
    /** 指定的投放区域 */
    selectedAreas?: string;
    /** 指定的投放包厢类型 */
    selectedRoomTypes?: string;
    /** 投放开始时间 */
    startTime?: string;
    /** 是否支持外送 */
    supportsExternalDelivery?: boolean;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 产品类型 */
    type?: string;
    /** 单位 */
    unit?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddProductSalesTemplateReqDto = {
    /** 名称 */
    name?: string;
  };

export type AddProductStatisticsCategoryReqDto = {
    /** 统计分类名称 */
    name?: string;
    /** 绑定商品类型 */
    productIds?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddProductStorageItem = {
    /** 到期时间 */
    expireTime: number;
    /** 是否仅线下存酒 */
    offlineOnly?: boolean;
    /** 产品ID */
    productId: string;
    /** 产品名称 */
    productName: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品类型 */
    productType?: string;
    /** 产品单位 */
    productUnit: string;
    /** 数量 */
    quantity: number;
    /** 备注 */
    remark?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 存放包厢ID */
    storageRoomId?: string;
  };

export type AddProductTimeSlotReqDto = {
    /** 星期几 */
    days?: string;
    /** 名称 */
    name?: string;
    /** 时间范围 */
    timerange?: string;
    /** 类型 */
    type?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddProductTypeReqDto = {
    /** 自定义存储配置 */
    customStorageConfig?: string;
    /** 配送超时时间 */
    deliveryTimeout?: number;
    /** 分销渠道列表 */
    distributionChannels?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 是否计入酒水分析 */
    isIncludedInDrinkAnalysis?: boolean;
    /** 是否启用后厨监控 */
    isKitchenMonitoring?: boolean;
    /** 产品类型名称 */
    name?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddProductUnitReqDto = {
    /** 单位名称 */
    name?: string;
    /** 所属门店ID，为空则是全局通用单位 */
    venueId?: string;
  };

export type AddProductWithdrawReqDto = {
    /** 批量操作时间 */
    batchTime?: number;
    /** 客户ID */
    customerId: string;
    /** 客户姓名 */
    customerName: string;
    /** 送达包厢ID */
    deliveryRoomId?: string;
    /** 送达包厢名称 */
    deliveryRoomName?: string;
    /** 是否批量操作的一部分 */
    isBatch?: number;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 关联订单号 */
    orderNumber?: string;
    /** 父订单号 */
    parentOrderNo?: string;
    /** 产品ID */
    productId: string;
    /** 产品名称 */
    productName: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 取用数量 */
    quantity: number;
    /** 备注 */
    remark?: string;
    /** 存储记录ID */
    storageId: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 关联的存酒单号 */
    storageOrderNo: string;
    /** 门店ID */
    venueId: string;
    /** 取用时间 */
    withdrawTime: number;
  };

export type AddRecipeReqDto = {
    /** 配料ID */
    ingredientId?: string;
    /** 产品ID */
    productId?: string;
    /** 配料数量 */
    quantity?: number;
  };

export type BatchAddProductWithdrawReqDto = {
    /** 送达包厢ID */
    deliveryRoomId?: string;
    /** 送达包厢名称 */
    deliveryRoomName?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 订单号 */
    orderNo?: string;
    /** 备注 */
    remark?: string;
    /** 场所ID */
    venueId: string;
    /** 提取项列表 */
    withdrawItems: BatchWithdrawItem[];
    /** 提取时间 */
    withdrawTime: number;
  };

export type CreateProductOutPrintRecordReqDto = {
    /** 员工姓名 */
    employeeName?: string;
    /** 订单号数组 */
    orderNos?: string[];
    /** 会话ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteProductBindingReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteProductDisplayCategoryReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteProductMultipleBuyFreeReqDto = {
    /** 唯一id */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type DeleteProductOutTypeReqDto = {
    /** 唯一id */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteProductPackageReqDto = {
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteProductPackageTypeReqDto = {
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteProductReqDto = {
    /** ID */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type DeleteProductSalesTemplateReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteProductStatisticsCategoryReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteProductTimeSlotReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteProductTypeReqDto = {
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteProductUnitReqDto = {
    /** ID */
    id?: string;
  };

export type ExtendedProductInfo = {
    /** 口味，可选 */
    flavors?: string;
    /** 支付价/现价 */
    payPrice?: number;
    /** 单价 */
    price?: number;
    /** 商品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 套餐子商品 */
    subProducts?: PackageSubProductInfo[];
    /** 小计 */
    totalAmount?: number;
    /** 单位 */
    unit?: string;
  };

export type getApiPrintRecordProductOutSessionParams = {
    /** 会话ID */
    sessionId: string;
  };

export type getApiProductStorageDetailIdParams = {
    /** 商品存储ID（明细ID） */
    id: string;
  };

export type GiftProductInfo = {
    /** 原价 */
    price?: number;
    /** 商品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 备注，例如"赠送" */
    remark?: string;
    /** 单位 */
    unit?: string;
  };

export type OperateProductStorageReqDto = {
    /** 续存时需要提供新的到期时间 */
    expireTime?: number;
    /** ID */
    id: string;
    /** 当操作类型为addItems时，存酒明细项 */
    items?: AddProductStorageItem[];
    /** 当操作类型为update时，更新的是否仅线下存酒 */
    offlineOnly?: boolean;
    /** extend: 续存, discard: 报废, cancel: 撤销, addItems: 添加商品, update: 更新存酒记录 */
    operationType: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 当操作类型为addItems时，存酒单号 */
    orderNo?: string;
    /** 当操作类型为update时，更新的产品名称 */
    productName?: string;
    /** 当操作类型为update时，更新的产品规格 */
    productSpec?: string;
    /** 当操作类型为update时，更新的产品单位 */
    productUnit?: string;
    /** 当操作类型为update时，更新的数量 */
    quantity?: number;
    /** 备注 */
    remark?: string;
    /** 当操作类型为update时，更新的存放位置 */
    storageLocation?: string;
    /** 当操作类型为update时，更新的存放包厢ID */
    storageRoomId?: string;
  };

export type OrderProductVO = {
    /** 商品或套餐分类ID */
    categoryId?: string;
    /** 创建时间 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** 口味 */
    flavors?: string;
    /** 是否-可免单 */
    freeable?: boolean;
    /** 是否-可赠送 */
    giftable?: boolean;
    /** ID */
    id?: string;
    /** 套内商品标签 */
    inPackageTag?: string;
    /** 是否已打折 */
    isDiscounted?: boolean;
    /** 是否-已免单 */
    isFree?: boolean;
    /** 是否畅饮 */
    isFreeDrinking?: boolean;
    /** 是否-已赠送 */
    isGift?: boolean;
    /** 是否多商品赠送 */
    isMultiProductGift?: boolean;
    /** 是否是补差价订单 */
    isPriceDiff?: boolean;
    /** 产品显示备注 */
    mark?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 是否-会员折扣 */
    memberDiscountable?: boolean;
    /** 会员ID */
    memberId?: string;
    /** 真实原价-会员价格-白金-钻石 */
    memberPrice?: number;
    /** 订单ID */
    orderNo?: string;
    /** 点单时商品折扣-下单时-只写一次 */
    orderProductDiscount?: number;
    /** 订单 */
    orderVO?: OrderVO;
    /** 原价 */
    originalPrice?: number;
    /** 退款对应的原始OrderProduct.Id */
    pId?: string;
    /** 套餐ID */
    packageId?: string;
    /** 套餐商品选择信息，json格式:[{"id": "xxx1", "count": 2}, {"id": "xxx2", "count": 3}] */
    packageProductInfo?: string;
    /** 套餐内选择的商品 */
    packageProducts?: ProductVO[];
    /** 总金额 - 回写 */
    payAmount?: number;
    /** 对应收款单 */
    payBillVO?: PayBillVO;
    /** 支付时商品折扣 - 回写 */
    payProductDiscount?: number;
    /** 支付时商品减免 - 回写 */
    payProductDiscountAmount?: number;
    /** 是否-商品折扣 */
    productDiscountable?: boolean;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    refundCount?: number;
    refundFee?: number;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 套餐来源 */
    src?: string;
    /** 状态 */
    state?: number;
    /** 扩展字段 */
    statusInOrder?: string;
    /** 单位 */
    unit?: string;
    /** 扩展字段2 */
    unitPrice?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type PackageSubProductInfo = {
    /** 商品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 单位 */
    unit?: string;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoOrderProductVO = {
    data?: OrderProductVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductVO = {
    data?: ProductVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PayRecordVO = {
    /** BShowQR支付方式的BQROneCode */
    bQROneCode?: string;
    /** 账单日期-冗余-用于统计 */
    billDate?: number;
    /** payBill.BillId */
    billId?: string;
    /** 创建时间 */
    ctime?: number;
    /** 支付员工ID-冗余-用于统计 */
    employeeId?: string;
    /** 完成时间 */
    finishTime?: number;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员卡-通用赠金（都可以用的赠金） */
    memberCommonBonusAmount?: number;
    /** 会员卡-用于商品的赠金 */
    memberGoodsBonusAmount?: number;
    /** 会员ID */
    memberId?: string;
    /** 会员卡-用于房费的赠金 */
    memberRoomBonusAmount?: number;
    /** 微信小程序支付的openid */
    openid?: string;
    /** 支付单ID */
    payId?: string;
    /** 只有三方支付时此处才有值 */
    payPid?: string;
    /** 支付来源-乐刷等第三方支付方式-微信/支付宝 */
    paySource?: string;
    /** 支付类型-微信 支付宝 找零 挂账 */
    payType?: string;
    /** 会员卡的本金 */
    principalAmount?: number;
    /** 商品名称 */
    productName?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 状态 success/refund */
    status?: string;
    /** 第三方支付单号 */
    thirdOrderId?: string;
    /** 总金额-实际支付金额 */
    totalFee?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店id */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type PointsExchangeVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 唯一ID */
    id?: string;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 所需积分 */
    requiredPoints?: number;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PricePlanUnionProductVO = {
    /** 商品数量 */
    count?: number;
    /** 商品id */
    id?: string;
    /** 套餐id */
    packageId?: string;
    /** 商品类型 package、standard(商品) */
    type?: string;
  };

export type ProductBindingVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 产品绑定名称 */
    name?: string;
    /** 产品列表 */
    productList?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店id */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductDisplayCategoryVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 是否显示二级分类 */
    isDisplaySecond?: boolean;
    /** 分类名称 */
    name?: string;
    /** 关联的商品类型 */
    productTypes?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductItem = {
    /** 商品数量 */
    count?: number;
    /** 商品ID */
    id?: string;
    /** 套餐ID (如果商品属于套餐) */
    packageId?: string;
    /** 商品类型: "standard" (标准商品), "package" (套餐) */
    type?: string;
  };

export type ProductMultipleBuyFreeVO = {
    /** 购买数量 */
    buyCount?: number;
    /** 购买商品列表 */
    buyProducts?: string;
    /** 创建时间 */
    ctime?: number;
    /** 赠送商品示例 */
    exampleGiftProduct?: string;
    /** 赠送策略 */
    giftPolicy?: string;
    /** 唯一id */
    id?: string;
    /** 是否可以重复购买 */
    isCanRepeatBuy?: boolean;
    /** 策略名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 适用时间段 */
    timeSlots?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type ProductOrPackageRVO = {
    productPackageVOs?: ProductPackageVO[];
    productVOs?: ProductVO[];
  };

export type ProductOutPrintRecordVO = {
    /** 创建时间 */
    createTime?: number;
    /** 打印设备名称 */
    deviceName?: string;
    /** 错误信息 */
    errorMsg?: string;
    /** 打印记录ID */
    id?: string;
    /** 操作员ID */
    operatorId?: string;
    /** 操作员姓名 */
    operatorName?: string;
    /** 打印单号 */
    printNo?: string;
    /** 打印时间 */
    printTime?: number;
    /** 打印类型 */
    printType?: string;
    /** 出品单数据 */
    productionOrderData?: ProductionOrderDataVO;
    /** 出品单号 */
    productionOrderNo?: string;
    /** 备注 */
    remark?: string;
    /** 场次ID */
    sessionId?: string;
    /** 打印状态 */
    status?: number;
    /** 更新时间 */
    updateTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ProductOutPrintTaskVO = {
    /** 打印数据 */
    data?: ProductionOrderDataVO;
    /** 打印机IP */
    printerIp?: string;
  };

export type ProductOutTypeVO = {
    /** 适用区域，JSON格式 */
    area?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 出库类型名称 */
    name?: string;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 适用商品类型，JSON格式 */
    productTypes?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
    /** 出库仓库，JSON格式 */
    warehouse?: string;
  };

export type ProductPackageTypeVO = {
    /** 创建时间 */
    ctime?: number;
    /** 分销渠道 */
    distributionChannels?: string;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 套餐类型名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 是否支持积分 */
    supportPoints?: boolean;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductPackageVO = {
    /** 区域价格 */
    areaPrices?: string;
    /** 最低消费后可用 */
    availableAfterMinimumConsumption?: boolean;
    /** 条形码 */
    barcode?: string;
    /** 是否计算业绩 */
    calculatePerformance?: boolean;
    /** 类别 */
    category?: string;
    /** 消费赠送优惠券 */
    consumptionGiftCoupon?: string;
    /** 是否计入最低消费 */
    countInMinimumConsumption?: boolean;
    /** 创建时间戳 */
    ctime?: number;
    /** 当前价格 */
    currentPrice?: number;
    /** 部署区域 */
    deploymentAreas?: string;
    /** 部署渠道 */
    deploymentChannels?: string;
    /** 部署包厢类型 */
    deploymentRoomTypes?: string;
    /** 描述 */
    description?: string;
    /** 免费酒水模式 */
    freeDrinkMode?: boolean;
    /** ID */
    id?: string;
    /** 图片 */
    image?: string;
    /** 是否支持会员折扣 */
    isMemberDiscountable?: boolean;
    /** 是否上架 */
    isOnShelf?: boolean;
    /** 是否支持商家折扣 */
    isOrderDiscountable?: boolean;
    /** 是否支持商家减免 */
    isOrderReduceable?: boolean;
    /** 是否促销 */
    isPromoted?: boolean;
    /** 会员卡支付限制 */
    memberCardPaymentLimit?: string;
    /** 名称 */
    name?: string;
    /** 可选组 */
    optionalGroups?: string;
    /** 可选组 */
    optionalGroupsPricePlanUnionVOs?: PricePlanUnionVO[];
    /** 订单数量限制 */
    orderQuantityLimit?: number;
    /** 套餐产品 */
    packageProducts?: string;
    /** 套餐产品 */
    packageProductsPricePlanUnionProductVOs?: PricePlanUnionProductVO[];
    /** 套餐产品 */
    productVOList?: ProductVO[];
    /** 系列 */
    series?: string;
    /** 上架时段 */
    shelfTimeSlots?: string;
    /** 员工赠送 */
    staffGift?: boolean;
    /** 状态值 */
    state?: number;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductPlan = {
    /** 计费类型: "paid" (收费), "free" (赠品) */
    billType?: string;
    /** 可选组名称 */
    name?: string;
    /** 可选商品数量 (仅在 optionType 为 "by_plan" 或 "by_count" 时使用) */
    optionCount?: number;
    /** 选项类型: "none" (无选项), "by_plan" (按方案选择), "by_count" (按数量选择) */
    optionType?: string;
    /** 商品列表 */
    products?: ProductItem[];
  };

export type ProductSalesTemplateVO = {
    /** 创建时间 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type ProductStatisticsCategoryVO = {
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 统计分类名称 */
    name?: string;
    /** 绑定商品类型 */
    productIds?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductStorageDetailWithHistoryVO = {
    /** 批量操作时间 */
    batchTime?: number;
    /** 创建时间 */
    ctime?: number;
    /** 客户ID */
    customerId?: string;
    /** 客户姓名 */
    customerName?: string;
    /** 到期剩余天数 */
    daysToExpire?: number;
    /** 到期时间 */
    expireTime?: number;
    /** ID */
    id?: string;
    /** 是否批量操作的一部分 */
    isBatch?: number;
    /** 是否已过期 */
    isExpired?: boolean;
    /** 是否即将到期 */
    isExpiringSoon?: boolean;
    /** 最后操作时间 */
    lastOperationTime?: number;
    /** 会员卡号 */
    memberCardNo?: string;
    /** 操作历史记录 */
    operationHistory?: ProductStorageOperationVO[];
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 存酒单号 */
    orderNo?: string;
    /** 父订单号 */
    parentOrderNo?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品类型 */
    productType?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 数量 */
    quantity?: number;
    /** 剩余数量 */
    remainingQty?: number;
    /** 剩余比例(0-100) */
    remainingRatio?: number;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 状态码(stored/partial/withdrawn/discarded) */
    statusCode?: string;
    /** 状态名称(已存/部分支取/已取完/已报废) */
    statusName?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 存放包厢ID */
    storageRoomId?: string;
    /** 存放包厢名称 */
    storageRoomName?: string;
    /** 存入时间 */
    storageTime?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type ProductStorageItemStatVO = {
    /** 现存数量 */
    currentQuantity?: number;
    /** 报废数量 */
    discardedQuantity?: number;
    /** 商品ID */
    productId?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品类型 */
    productType?: string;
    /** 剩余数量 */
    remainingQuantity?: number;
    /** 存放仓库 */
    storageLocation?: string;
    /** 存放类型(存酒、小吃等) */
    storageType?: string;
    /** 存酒总数量 */
    totalQuantity?: number;
    /** 单位 */
    unit?: string;
  };

export type ProductStorageOperationVO = {
    /** 操作后剩余数量 */
    balanceQty?: number;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 操作数量 */
    quantity?: number;
    /** 备注 */
    remark?: string;
    /** 操作时间 */
    time?: number;
    /** 操作类型 (storage/withdraw/refill/scrap) */
    type?: string;
    /** 操作类型名称 (存酒/取酒/续存/报废) */
    typeName?: string;
  };

export type ProductTimeSlotVO = {
    /** 创建时间 */
    ctime?: number;
    /** 星期几 */
    days?: string;
    /** ID */
    id?: string;
    /** 名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 时间范围 */
    timerange?: string;
    /** 类型 */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductTypeAndPackageVO = {
    /** 套餐类型 */
    productPackageTypeVOs?: ProductPackageTypeVO[];
    /** 商品类型 */
    productTypeVOs?: ProductTypeVO[];
    /** 所属门店ID */
    venueId?: string;
  };

export type ProductTypeVO = {
    /** 创建时间 */
    ctime?: number;
    /** 自定义存储配置 */
    customStorageConfig?: string;
    /** 配送超时时间 */
    deliveryTimeout?: number;
    /** 分销渠道列表 */
    distributionChannels?: string;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 是否计入酒水分析 */
    isIncludedInDrinkAnalysis?: boolean;
    /** 是否启用后厨监控 */
    isKitchenMonitoring?: boolean;
    /** 产品类型名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 更新时间 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type ProductUnitVO = {
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 是否系统内置单位 */
    isSystem?: boolean;
    /** 单位名称 */
    name?: string;
    /** 更新时间 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
  };

export type ProductVO = {
    /** 支持重复购买 */
    allowRepeatBuy?: boolean;
    /** 支持员工赠送 */
    allowStaffGift?: boolean;
    /** 支持存酒 */
    allowWineStorage?: boolean;
    /** 不同区域价格 */
    areaPrices?: string;
    /** 辅助公式 */
    auxiliaryFormula?: string;
    /** 条形码 */
    barcode?: string;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 计算库存 */
    calculateInventory?: boolean;
    /** 商品分类 */
    category?: string;
    /** 计入低消 */
    countToMinCharge?: boolean;
    /** 计算业绩 */
    countToPerformance?: boolean;
    /** 创建时间 */
    ctime?: number;
    /** Price                         int64  `json:"price"`                         // 原价 */
    currentPrice?: number;
    /** 送达超时时间 */
    deliveryTimeout?: number;
    /** 商品介绍 */
    description?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 投放结束时间 */
    endTime?: string;
    /** 外送价格 */
    externalDeliveryPrice?: number;
    /** 商品口味 */
    flavors?: string;
    /** 消费赠券 */
    giftVoucher?: string;
    /** ID */
    id?: string;
    /** 商品图片 */
    image?: string;
    /** 辅料配方 */
    ingredients?: string;
    /** 指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否上架展示 */
    isDisplayed?: boolean;
    /** 是否支持会员折扣 */
    isMemberDiscountable?: boolean;
    /** 是否支持商家折扣 */
    isOrderDiscountable?: boolean;
    /** 是否支持商家减免 */
    isOrderReduceable?: boolean;
    /** 推广 */
    isPromotion?: boolean;
    /** 是否为实价产品 */
    isRealPriceProduct?: boolean;
    /** 指定投放包厢类型 */
    isRoomTypeSpecified?: boolean;
    /** 是否洁清 */
    isSoldOut?: boolean;
    /** 低库存数 */
    lowStockThreshold?: number;
    /** 会员卡结账限制 */
    memberCardLimits?: string;
    /** 会员卡支付限制 */
    memberCardPaymentRestrictions?: string;
    /** 最小销售数量 */
    minimumSaleQuantity?: number;
    /** 产品名称 */
    name?: string;
    /** 支付标签 */
    payMark?: string;
    /** 推荐搭配 */
    recommendCombos?: string;
    /** 指定的投放区域 */
    selectedAreas?: string;
    /** 指定的投放包厢类型 */
    selectedRoomTypes?: string;
    /** 洁清时间 */
    soldOutTime?: number;
    /** 投放开始时间 */
    startTime?: string;
    /** 状态 */
    state?: number;
    /** 是否支持外送 */
    supportsExternalDelivery?: boolean;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 产品类型 */
    type?: string;
    /** 单位 */
    unit?: string;
    /** 更新时间 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type ProductWithdrawVO = {
    /** 批量操作时间 */
    batchTime?: number;
    /** 创建时间 */
    ctime?: number;
    /** 创建时间字符串 */
    ctimeStr?: string;
    /** 客户ID */
    customerId?: string;
    /** 客户姓名 */
    customerName?: string;
    /** 送达包厢ID */
    deliveryRoomId?: string;
    /** 送达包厢名称 */
    deliveryRoomName?: string;
    /** ID */
    id?: string;
    /** 是否批量操作 */
    isBatch?: number;
    /** 最后操作时间 */
    lastOperationTime?: number;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 取酒单号 */
    orderNo?: string;
    /** 关联订单号 */
    orderNumber?: string;
    /** 父订单号 */
    parentOrderNo?: string;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 数量 */
    quantity?: number;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 状态码 */
    statusCode?: string;
    /** 状态名称 */
    statusName?: string;
    /** 关联存储记录ID */
    storageId?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 关联存酒单号 */
    storageOrderNo?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
    /** 取用时间 */
    withdrawTime?: number;
    /** 取用时间字符串 */
    withdrawTimeStr?: string;
  };

export type QueryPointsExchangeReqDto = {
    /** 唯一ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 所需积分 */
    requiredPoints?: number;
  };

export type QueryProductBindingReqDto = {
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 产品绑定名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 产品列表 */
    productList?: string;
    /** 门店id */
    venueId?: string;
  };

export type QueryProductDisplayCategoryReqDto = {
    /** ID */
    id?: string;
    /** 是否显示二级分类 */
    isDisplaySecond?: boolean;
    /** 分类名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 关联的商品类型 */
    productTypes?: string;
  };

export type QueryProductMultipleBuyFreeReqDto = {
    /** 购买数量 */
    buyCount?: number;
    /** 购买商品列表 */
    buyProducts?: string;
    /** 赠送商品示例 */
    exampleGiftProduct?: string;
    /** 赠送策略 */
    giftPolicy?: string;
    /** 唯一id */
    id?: string;
    /** 是否可以重复购买 */
    isCanRepeatBuy?: boolean;
    /** 策略名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 适用时间段 */
    timeSlots?: string;
    /** 门店id */
    venueId?: string;
  };

export type QueryProductOutTypeReqDto = {
    /** 适用区域，JSON格式 */
    area?: string;
    /** 唯一id */
    id?: string;
    /** 出库类型名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 适用商品类型，JSON格式 */
    productTypes?: string;
    /** 门店ID */
    venueId?: string;
    /** 出库仓库，JSON格式 */
    warehouse?: string;
  };

export type QueryProductPackageReqDto = {
    /** 最低消费后可用 */
    availableAfterMinimumConsumption?: boolean;
    /** 条形码 */
    barcode?: string;
    /** 是否计算业绩 */
    calculatePerformance?: boolean;
    /** 类别 */
    category?: string;
    /** 是否计入最低消费 */
    countInMinimumConsumption?: boolean;
    /** 当前价格 */
    currentPrice?: number;
    /** 免费酒水模式 */
    freeDrinkMode?: boolean;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 是否上架 */
    isOnShelf?: boolean;
    /** 是否促销 */
    isPromoted?: boolean;
    /** 名称 */
    name?: string;
    /** 订单数量限制 */
    orderQuantityLimit?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 系列 */
    series?: string;
    /** 员工赠送 */
    staffGift?: boolean;
    /** 是否支持折扣 */
    supportsDiscount?: boolean;
    /** 门店id */
    venueId?: string;
  };

export type QueryProductPackageTypeReqDto = {
    /** 分销渠道 */
    distributionChannels?: string;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 套餐类型名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 是否支持积分 */
    supportPoints?: boolean;
    /** 门店id */
    venueId?: string;
  };

export type QueryProductReqDto = {
    /** 支持重复购买 */
    allowRepeatBuy?: boolean;
    /** 支持员工赠送 */
    allowStaffGift?: boolean;
    /** 支持存酒 */
    allowWineStorage?: boolean;
    /** 不同区域价格 */
    areaPrices?: string;
    /** 辅助公式 */
    auxiliaryFormula?: string;
    /** 条形码 */
    barcode?: string;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 计算库存 */
    calculateInventory?: boolean;
    /** 商品分类 */
    category?: string;
    /** 计入低消 */
    countToMinCharge?: boolean;
    /** 计算业绩 */
    countToPerformance?: boolean;
    /** 当前价格 */
    currentPrice?: number;
    /** 送达超时时间 */
    deliveryTimeout?: number;
    /** 商品介绍 */
    description?: string;
    /** 支持折扣 */
    discounts?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 投放结束时间 */
    endTime?: string;
    /** 外送价格 */
    externalDeliveryPrice?: number;
    /** 商品口味 */
    flavors?: string;
    /** 消费赠券 */
    giftVoucher?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 商品图片 */
    image?: string;
    /** 辅料配方 */
    ingredients?: string;
    /** 指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否上架展示 */
    isDisplayed?: boolean;
    /** 推广 */
    isPromotion?: boolean;
    /** 是否为实价产品 */
    isRealPriceProduct?: boolean;
    /** 指定投放包厢类型 */
    isRoomTypeSpecified?: boolean;
    /** 是否洁清 */
    isSoldOut?: boolean;
    /** 低库存数 */
    lowStockThreshold?: number;
    /** 会员卡结账限制 */
    memberCardLimits?: string;
    /** 会员卡支付限制 */
    memberCardPaymentRestrictions?: string;
    /** 最小销售数量 */
    minimumSaleQuantity?: number;
    /** 产品名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 支付标签 */
    payMark?: string;
    /** 支付价格 */
    payPrice?: number;
    /** 套餐类型ID */
    productPackageTypeId?: string;
    /** 产品类型ID */
    productTypeId?: string;
    /** 推荐搭配 */
    recommendCombos?: string;
    /** 指定的投放区域 */
    selectedAreas?: string;
    /** 指定的投放包厢类型 */
    selectedRoomTypes?: string;
    /** 投放开始时间 */
    startTime?: string;
    /** 是否支持外送 */
    supportsExternalDelivery?: boolean;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 产品类型 */
    type?: string;
    /** 单位 */
    unit?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryProductSalesTemplateReqDto = {
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryProductStatisticsCategoryReqDto = {
    /** ID */
    id?: string;
    /** 统计分类名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 绑定商品类型 */
    productIds?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryProductStorageReqDto = {
    /** 客户ID */
    customerId?: string;
    /** 客户姓名 */
    customerName?: string;
    /** 到期时间截止 */
    expireTimeEnd?: number;
    /** 到期时间起始 */
    expireTimeStart?: number;
    /** ID */
    id?: string;
    /** 是否仅线下存酒 */
    offlineOnly?: boolean;
    /** 只查询有剩余的记录 */
    onlyRemaining?: boolean;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 存酒单号 */
    orderNo?: string;
    /** 页码 */
    pageNum?: number;
    /** 页大小 */
    pageSize?: number;
    /** 父订单号 */
    parentOrderNo?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品类型 */
    productType?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 包厢ID */
    roomId?: string;
    /** 搜索文本(模糊搜索客户名或手机号) */
    searchText?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 存放包厢ID */
    storageRoomId?: number;
    /** 存入时间截止 */
    storageTimeEnd?: number;
    /** 存入时间起始 */
    storageTimeStart?: number;
    /** 场馆ID */
    venueId?: string;
  };

export type QueryProductTimeSlotReqDto = {
    /** 星期几 */
    days?: string;
    /** ID */
    id?: string;
    /** 名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 时间范围 */
    timerange?: string;
    /** 类型 */
    type?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryProductTypeReqDto = {
    /** 自定义存储配置 */
    customStorageConfig?: string;
    /** 配送超时时间 */
    deliveryTimeout?: number;
    /** 分销渠道列表 */
    distributionChannels?: string;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 是否计入酒水分析 */
    isIncludedInDrinkAnalysis?: boolean;
    /** 是否启用后厨监控 */
    isKitchenMonitoring?: boolean;
    /** 产品类型名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryProductUnitReqDto = {
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 单位名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryProductWithdrawReqDto = {
    /** 客户ID */
    customerId?: string;
    /** 客户姓名 */
    customerName?: string;
    /** ID */
    id?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 取酒单号 */
    orderNo?: string;
    /** 关联订单号 */
    orderNumber?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 父订单号 */
    parentOrderNo?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 搜索文本(模糊搜索客户名或手机号) */
    searchText?: string;
    /** 存储记录ID */
    storageId?: string;
    /** 关联的存酒单号 */
    storageOrderNo?: string;
    /** 门店ID */
    venueId: string;
    /** 取用时间结束 */
    withdrawTimeEnd?: number;
    /** 取用时间开始 */
    withdrawTimeStart?: number;
  };

export type QueryRecipeReqDto = {
    /** ID */
    id?: string;
    /** 配料ID */
    ingredientId?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 产品ID */
    productId?: string;
    /** 配料数量 */
    quantity?: number;
  };

export type RecipeVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 配料ID */
    ingredientId?: string;
    /** 产品ID */
    productId?: string;
    /** 配料数量 */
    quantity?: number;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type ResultArrayVoderpltvvErpManagentApiVoOrderProductVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderProductVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductBindingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductBindingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductDisplayCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductDisplayCategoryVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductMultipleBuyFreeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductMultipleBuyFreeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductOutPrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductOutPrintRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductOutPrintTaskVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductOutPrintTaskVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductOutTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductOutTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductPackageTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductPackageTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductPackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductPackageVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductSalesTemplateVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductSalesTemplateVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductStatisticsCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStatisticsCategoryVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductTimeSlotVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductTimeSlotVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductUnitVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductUnitVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductWithdrawVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductWithdrawVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoOrderProductVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderProductVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoOrderProductVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoOrderProductVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductBindingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductBindingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductDisplayCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductDisplayCategoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductMultipleBuyFreeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductMultipleBuyFreeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductOrPackageRVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductOrPackageRVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductOutTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductOutTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductPackageTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductPackageTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductPackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductPackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductSalesTemplateVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductSalesTemplateVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductStatisticsCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStatisticsCategoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductStorageDetailWithHistoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStorageDetailWithHistoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductTimeSlotVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductTimeSlotVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductTypeAndPackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductTypeAndPackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductUnitVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductUnitVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductWithdrawVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductWithdrawVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type UnifiedProductStorageReqDto = {
    /** 客户ID */
    customerId?: string;
    /** 客户姓名 */
    customerName?: string;
    /** 员工ID */
    employeeId?: string;
    /** 到期时间 (单商品存酒时使用) */
    expireTime?: number;
    /** 其他系统必要字段 */
    grantIp?: string;
    /** 存酒商品信息 - 支持多商品 */
    items?: UnifiedStorageItemDto[];
    /** MAC地址 */
    mac?: string;
    /** 会员卡号 */
    memberCardNo?: string;
    /** 是否仅线下存酒 */
    offlineOnly?: boolean;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 单商品存酒时的额外属性 */
    productId?: string;
    /** 产品名称 (单商品存酒时使用) */
    productName?: string;
    /** 产品规格 (单商品存酒时使用) */
    productSpec?: string;
    /** 产品类型 (单商品存酒时使用) */
    productType?: string;
    /** 产品单位 (单商品存酒时使用) */
    productUnit?: string;
    /** 数量 (单商品存酒时使用) */
    quantity?: number;
    /** 备注 */
    remark?: string;
    /** 存放位置 (单商品存酒时使用) */
    storageLocation?: string;
    /** 寄存包厢ID */
    storageRoomId?: string;
    /** 寄存包厢名称 */
    storageRoomName?: string;
    /** 存酒信息 */
    storageTime?: number;
    /** 基本信息 */
    venueId?: string;
  };

export type UpdatePointsExchangeReqDto = {
    /** 唯一ID */
    id?: string;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 所需积分 */
    requiredPoints?: number;
  };

export type UpdateProductBindingReqDto = {
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 产品绑定名称 */
    name?: string;
    /** 产品列表 */
    productList?: string;
    /** 门店id */
    venueId?: string;
  };

export type UpdateProductDisplayCategoryReqDto = {
    /** ID */
    id?: string;
    /** 是否显示二级分类 */
    isDisplaySecond?: boolean;
    /** 分类名称 */
    name?: string;
    /** 关联的商品类型 */
    productTypes?: string;
  };

export type UpdateProductMultipleBuyFreeReqDto = {
    /** 购买数量 */
    buyCount?: number;
    /** 购买商品列表 */
    buyProducts?: string;
    /** 赠送商品示例 */
    exampleGiftProduct?: string;
    /** 赠送策略 */
    giftPolicy?: string;
    /** 唯一id */
    id?: string;
    /** 是否可以重复购买 */
    isCanRepeatBuy?: boolean;
    /** 策略名称 */
    name?: string;
    /** 适用时间段 */
    timeSlots?: string;
    /** 门店id */
    venueId?: string;
  };

export type UpdateProductOutTypeReqDto = {
    /** 适用区域，JSON格式 */
    area?: string;
    /** 唯一id */
    id?: string;
    /** 出库类型名称 */
    name?: string;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 适用商品类型，JSON格式 */
    productTypes?: string;
    /** 门店ID */
    venueId?: string;
    /** 出库仓库，JSON格式 */
    warehouse?: string;
  };

export type UpdateProductPackageReqDto = {
    /** 区域价格 */
    areaPrices?: string;
    /** 最低消费后可用 */
    availableAfterMinimumConsumption?: boolean;
    /** 条形码 */
    barcode?: string;
    /** 是否计算业绩 */
    calculatePerformance?: boolean;
    /** 类别 */
    category?: string;
    /** 消费赠送优惠券 */
    consumptionGiftCoupon?: string;
    /** 是否计入最低消费 */
    countInMinimumConsumption?: boolean;
    /** 当前价格 */
    currentPrice?: number;
    /** 部署区域 */
    deploymentAreas?: string;
    /** 部署渠道 */
    deploymentChannels?: string;
    /** 部署包厢类型 */
    deploymentRoomTypes?: string;
    /** 描述 */
    description?: string;
    /** 免费酒水模式 */
    freeDrinkMode?: boolean;
    /** ID */
    id?: string;
    /** 图片 */
    image?: string;
    /** 是否支持会员折扣 */
    isMemberDiscountable?: boolean;
    /** 是否上架 */
    isOnShelf?: boolean;
    /** 是否支持商家折扣 */
    isOrderDiscountable?: boolean;
    /** 是否支持商家减免 */
    isOrderReduceable?: boolean;
    /** 是否促销 */
    isPromoted?: boolean;
    /** 会员卡支付限制 */
    memberCardPaymentLimit?: string;
    /** 名称 */
    name?: string;
    /** 可选组 */
    optionalGroups?: string;
    /** 订单数量限制 */
    orderQuantityLimit?: number;
    /** 套餐产品 */
    packageProducts?: string;
    /** 系列 */
    series?: string;
    /** 上架时段 */
    shelfTimeSlots?: string;
    /** 员工赠送 */
    staffGift?: boolean;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 门店id */
    venueId?: string;
  };

export type UpdateProductPackageTypeReqDto = {
    /** 分销渠道 */
    distributionChannels?: string;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 套餐类型名称 */
    name?: string;
    /** 是否支持积分 */
    supportPoints?: boolean;
    venueId?: string;
  };

export type UpdateProductReqDto = {
    /** 支持重复购买 */
    allowRepeatBuy?: boolean;
    /** 支持员工赠送 */
    allowStaffGift?: boolean;
    /** 支持存酒 */
    allowWineStorage?: boolean;
    /** 不同区域价格 */
    areaPrices?: string;
    /** 辅助公式 */
    auxiliaryFormula?: string;
    /** 条形码 */
    barcode?: string;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 计算库存 */
    calculateInventory?: boolean;
    /** 商品分类 */
    category?: string;
    /** 计入低消 */
    countToMinCharge?: boolean;
    /** 计算业绩 */
    countToPerformance?: boolean;
    /** 当前价格 */
    currentPrice?: number;
    /** 送达超时时间 */
    deliveryTimeout?: number;
    /** 商品介绍 */
    description?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 投放结束时间 */
    endTime?: string;
    /** 外送价格 */
    externalDeliveryPrice?: number;
    /** 商品口味 */
    flavors?: string;
    /** 消费赠券 */
    giftVoucher?: string;
    /** ID */
    id?: string;
    /** 商品图片 */
    image?: string;
    /** 辅料配方 */
    ingredients?: string;
    /** 指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否上架展示 */
    isDisplayed?: boolean;
    /** 是否支持会员折扣 */
    isMemberDiscountable?: boolean;
    /** 是否支持商家折扣 */
    isOrderDiscountable?: boolean;
    /** 是否支持商家减免 */
    isOrderReduceable?: boolean;
    /** 推广 */
    isPromotion?: boolean;
    /** 是否为实价产品 */
    isRealPriceProduct?: boolean;
    /** 指定投放包厢类型 */
    isRoomTypeSpecified?: boolean;
    /** 是否洁清 */
    isSoldOut?: boolean;
    /** 低库存数 */
    lowStockThreshold?: number;
    /** 会员卡结账限制 */
    memberCardLimits?: string;
    /** 会员卡支付限制 */
    memberCardPaymentRestrictions?: string;
    /** 最小销售数量 */
    minimumSaleQuantity?: number;
    /** 产品名称 */
    name?: string;
    /** 支付标签 */
    payMark?: string;
    /** 原价 */
    price?: number;
    /** 推荐搭配 */
    recommendCombos?: string;
    /** 指定的投放区域 */
    selectedAreas?: string;
    /** 指定的投放包厢类型 */
    selectedRoomTypes?: string;
    /** 投放开始时间 */
    startTime?: string;
    /** 是否支持外送 */
    supportsExternalDelivery?: boolean;
    /** 时段价格 */
    timeSlotPrices?: string;
    /** 产品类型 */
    type?: string;
    /** 单位 */
    unit?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateProductSalesTemplateReqDto = {
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
  };

export type UpdateProductSoldOutReqDto = {
    /** ID */
    id?: string;
    /** 是否洁清 */
    soldOut?: boolean;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateProductStatisticsCategoryReqDto = {
    /** ID */
    id?: string;
    /** 统计分类名称 */
    name?: string;
    /** 绑定商品类型 */
    productIds?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateProductTimeSlotReqDto = {
    /** 星期几 */
    days?: string;
    /** ID */
    id?: string;
    /** 名称 */
    name?: string;
    /** 时间范围 */
    timerange?: string;
    /** 类型 */
    type?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateProductTypeReqDto = {
    /** 自定义存储配置 */
    customStorageConfig?: string;
    /** 配送超时时间 */
    deliveryTimeout?: number;
    /** 分销渠道列表 */
    distributionChannels?: string;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 是否计入酒水分析 */
    isIncludedInDrinkAnalysis?: boolean;
    /** 是否启用后厨监控 */
    isKitchenMonitoring?: boolean;
    /** 产品类型名称 */
    name?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateProductUnitReqDto = {
    /** ID */
    id?: string;
    /** 单位名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateRecipeReqDto = {
    /** ID */
    id?: string;
    /** 配料ID */
    ingredientId?: string;
    /** 产品ID */
    productId?: string;
    /** 配料数量 */
    quantity?: number;
  };

export type V3QueryProductReqDto = {
    /** ID列表 */
    ids?: string[];
    /** 所属门店ID */
    venueId?: string;
  };

export type WithdrawableItemVO = {
    /** 距离过期还有多少天 */
    daysToExpire?: number;
    /** 到期时间 */
    expireTime?: number;
    /** 过期状态：normal(正常), soon(即将过期), expired(已过期) */
    expiringStatus?: string;
    /** 存酒明细ID */
    id?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 存酒明细单号 */
    orderNo?: string;
    /** 存酒单号 */
    parentOrderNo?: string;
    /** 商品ID */
    productId?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品规格 */
    productSpec?: string;
    /** 商品单位 */
    productUnit?: string;
    /** 存入数量 */
    quantity?: number;
    /** 剩余数量 */
    remainingQty?: number;
    /** 存放位置 */
    storageLocation?: string;
    /** 存入时间 */
    storageTime?: number;
  };
