<template>
  <div class="employee-shift-container h-full p-[16px] flex flex-col bg-gray-100">
    <!-- 当没有数据时显示空状态 -->
    <el-empty v-if="!vm.state.loading && !vm.computed.hasData.value" description="暂无交班数据" class="flex-grow flex items-center justify-center" />

    <!-- 加载状态 -->
    <div v-if="vm.state.loading" class="flex-grow flex items-center justify-center">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 主要内容区域 -->
    <div v-if="!vm.state.loading && vm.computed.hasData.value" class="flex flex-grow overflow-auto flex-row gap-4">
      <!-- 左侧栏 -->
      <div class="w-[492px] flex flex-col gap-4">
        <!-- 营业概况卡片 -->
        <div class="bg-white rounded-lg shadow-sm p-5 flex-grow-0">
          <h3 class="text-gray-700 text-lg font-medium mb-4">营业概况</h3>

          <!-- 半圆形进度图和实收数据 -->
          <div class="relative flex justify-center mb-[24px]">
            <semicircle-progress-bar
              :width="360"
              :height="180"
              :current-value="vm.computed.totalIncome.value"
              :target-value="vm.state.aggregatedShiftReport?.businessOverview?.shouldFee || 0"
              title="营业实收">
              <template #center-content>
                <!--  -->
                <div class="flex items-center mt-[48px] big-text">
                  <price-display :amount-in-fen="vm.computed.totalIncome.value" class="text-gray-500 text-lg mt-4" />
                </div>
                <div class="flex items-center mt-[16px]">
                  <span class="w-[52px] text-[14px]">应收:</span>
                  <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.shouldFee || 0" class="price-display-right-align" />
                </div>
              </template>
            </semicircle-progress-bar>
          </div>

          <!-- 营业数据明细 -->
          <div class="mt-4 grid grid-cols-2 gap-x-4 gap-y-3">
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">营业应收:</span>
              <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.shouldFee || 0" class="price-display-right-align value-width" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">商家优惠:</span>
              <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.merchantDiscount || 0" class="price-display-right-align" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">营业实收:</span>
              <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.totalFee || 0" class="price-display-right-align value-width" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">员工赠送:</span>
              <price-display
                :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.employeeGift || 0"
                class="price-display-right-align value-width" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">包厢收入:</span>
              <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.roomFee || 0" class="price-display-right-align value-width" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">会员优惠:</span>
              <price-display
                :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.memberDiscount || 0"
                class="price-display-right-align value-width" />
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">商品收入:</span>
              <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.productFee || 0" class="price-display-right-align value-width" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 label-width">抹零:</span>
              <price-display :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.zeroFee || 0" class="price-display-right-align value-width" />
            </div>
          </div>

          <!-- 待结总额 -->
          <div class="mt-4 pt-4 border-t border-gray-200 flex justify-between">
            <span class="text-gray-500 label-width">待结总额:</span>
            <price-display
              :amount-in-fen="vm.state.aggregatedShiftReport?.businessOverview?.unpaidFee || 0"
              class="price-display-danger price-display-right-align value-width" />
          </div>
        </div>

        <!-- 营业数据统计卡片 -->
        <div class="bg-white rounded-lg shadow-sm p-5 flex-grow-0 h-[292px]">
          <h3 class="text-gray-700 text-lg font-medium mb-4">营业数据</h3>

          <div class="grid grid-cols-3 gap-4">
            <div class="flex flex-col items-center">
              <div class="text-3xl font-bold">{{ vm.computed.tableCount.value }}</div>
              <div class="text-gray-500 text-sm">总开台数</div>
            </div>
            <div class="flex flex-col items-center">
              <div class="text-3xl font-bold">{{ vm.computed.paidBillCount.value }}</div>
              <div class="text-gray-500 text-sm">已结账单数</div>
            </div>
            <div class="flex flex-col items-center">
              <div class="text-3xl font-bold">{{ vm.computed.unpaidOrderCount.value }}</div>
              <div class="text-gray-500 text-sm">待结订单数</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间栏 -->
      <div class="w-[500px] h-full flex flex-col justify-between overflow-hidden gap-[24px]">
        <!-- 普通支付统计卡片 -->
        <div class="flex flex-1 flex-col justify-between">
          <div class="h-[50%] bg-white rounded-lg shadow-sm p-[24px] flex-grow-0">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-gray-700 text-lg font-medium">普通支付:</h3>
              <div class="font-medium">
                <price-display :amount-in-fen="vm.computed.totalNormalPayment.value" class="price-display-right-align" />
              </div>
            </div>

            <!-- 支付方式柱状图 - 使用垂直柱状图 -->
            <div class="flex justify-between items-end h-[200px] mt-[12px]">
              <!-- 动态生成普通支付类型图表 -->
              <div v-for="(value, key) in vm.computed.normalPaymentItems.value" :key="`normal-${key}`" class="flex flex-col items-center">
                <div class="text-gray-700 mb-2">{{ convertToYuan(value).toFixed(2) }}</div>
                <div
                  class="w-[4px]"
                  :class="getPaymentBarColorClass(key)"
                  :style="`height: ${getPaymentBarHeight(value, vm.computed.maxNormalPayment.value)}px`"
                  style="border-top-left-radius: 0.125rem; border-top-right-radius: 0.125rem"></div>
                <div class="text-gray-700 mt-2" style="white-space: nowrap">{{ getPaymentName(key) }}</div>
              </div>
            </div>
          </div>

          <!-- 会员支付统计卡片 -->
          <div class="bg-white h-[45%] rounded-lg shadow-sm p-[24px] flex-grow-0">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-gray-700 text-lg font-medium">会员支付:</h3>
              <div class="font-medium">
                <price-display :amount-in-fen="vm.computed.totalMemberPayment.value" class="price-display-right-align" />
              </div>
            </div>

            <!-- 会员支付方式柱状图 - 使用垂直柱状图 -->
            <div class="flex justify-between items-end h-[200px] mt-[12px] pb-[24px]">
              <!-- 动态生成会员支付类型图表 -->
              <div v-for="(item, index) in vm.computed.memberPaymentItems.value" :key="`member-${index}`" class="flex flex-col items-center">
                <div class="text-gray-700 mb-2">{{ convertToYuan(item.value).toFixed(2) }}</div>
                <div
                  class="w-[4px] bg-yellow-500 rounded-t-sm"
                  :style="`height: ${getPaymentBarHeight(item.value, vm.computed.maxMemberPayment.value)}px`"></div>
                <div class="text-gray-700 mt-2">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部卡片区域 -->
        <div class="grid gap-[1px] h-[292px]">
          <!-- 会员充值卡片 -->
          <div class="bg-white rounded-lg shadow-sm p-[24px]">
            <h3 class="text-gray-700 text-lg font-medium mb-3">会员充值</h3>

            <div class="flex justify-between items-center">
              <span class="text-gray-500 w-[100px]">充值金额:</span>
              <price-display :amount-in-fen="vm.computed.memberRechargeAmount.value" class="price-display-right-align" />
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-500 w-[100px]">预收款:</span>
              <price-display :amount-in-fen="0" class="price-display-right-align" />
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500 w-[100px]">预付总额:</span>
              <price-display :amount-in-fen="0" class="price-display-right-align" />
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧账单列表区域 -->
      <div class="w-[708px] bg-white rounded-lg shadow-sm p-5 flex-grow overflow-auto">
        <h3 class="text-gray-700 text-lg font-medium mb-4">账单列表</h3>

        <el-table class="table-no-padding bill-table-clickable" :data="billList" stripe type="small" @row-click="(row) => vm.actions.checkBill({ id: row.id, sessionId: row.sessionId, billNo: row.billNo, totalAmount: row.totalAmount, createTime: row.createTime })">
          <el-table-column prop="id" label="单号" align="center">
            <template #default="scope">
              <div class="flex flex-col items-center">
                <span class="text-[14px] font-medium">{{ getDisplayBillId(scope.row) }}</span>
                <template v-if="scope.row.refundBills && scope.row.refundBills.length > 0">
                  <div class="text-[12px] text-gray-500 mt-1">
                    <div v-for="refundBill in scope.row.refundBills" :key="refundBill.id">
                      {{ refundBill.isBack ? `【还】${formatBillId(refundBill.id)}` : `【退】${formatBillId(refundBill.id)}` }}
                    </div>
                  </div>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="包厢" align="center">
            <template #default="scope">
              <span class="text-[14px]">{{ scope.row.room }}</span>
            </template>
          </el-table-column>
          <el-table-column label="日期" align="center">
            <template #default="scope">
              <div class="flex flex-col text-[14px]">
                <span>{{ scope.row.date }}</span>
                <span>{{ scope.row.time }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="实收金额" align="right">
            <template #default="scope">
              <div class="flex flex-col">
                <price-display :amount-in-fen="scope.row.amount" class="price-display-right-align price-display-small" />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template #default="scope">
              <el-button type="default" size="small" @click.stop="vm.actions.checkBill({ id: scope.row.id, sessionId: scope.row.sessionId, billNo: scope.row.billNo, totalAmount: scope.row.totalAmount, createTime: scope.row.createTime })"> 查看 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, watch, defineEmits } from 'vue';
import { useEmployeeShift } from './presenter';
import type { IEmployeeShiftViewModel } from './viewmodel';
import { now10 } from '@/utils/dateUtils';
import { convertToYuan } from '@/utils/priceUtils';
import { useDeviceStore } from '@/stores/deviceStore';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import SemicircleProgressBar from '@/components/common/SemicircleProgressBar.vue';
import DialogManager from '@/utils/dialog';
import { ElMessage } from 'element-plus';
// 定义组件接收的props
const props = defineProps({
  shiftId: {
    type: String,
    default: ''
  },
  handNo: {
    type: String,
    default: ''
  },
  readOnly: {
    type: Boolean,
    default: false
  }
});

// 获取ViewModel，传入shiftId和handNo
const vm: IEmployeeShiftViewModel = useEmployeeShift(props.shiftId, props.handNo);

// 数字格式化函数
const formatNumber = (num: number) => {
  if (num === 0) return 0;
  if (num === null || num === undefined) return 0;
  return num.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
};

// 获取支付方式名称
const getPaymentName = (key: string) => {
  const nameMap: Record<string, string> = {
    cash: '现金',
    bank: '银行卡',
    wechat: '微信',
    alipay: '支付宝',
    meituan: '美团',
    koubei: '口碑',
    ticket: '票券',
    leshua: '扫码支付',
    other: '其他'
  };
  return nameMap[key] || key;
};

// 获取支付方式颜色类
const getPaymentBarColorClass = (key: string) => {
  const colorMap: Record<string, string> = {
    cash: 'bg-blue-500',
    bank: 'bg-blue-500',
    wechat: 'bg-green-500',
    alipay: 'bg-blue-400',
    leshua: 'bg-green-400',
    meituan: 'bg-green-400',
    koubei: 'bg-red-400',
    ticket: 'bg-orange-400',
    other: 'bg-gray-400'
  };
  return colorMap[key] || 'bg-gray-400';
};

// 柱状图高度计算函数
const getPaymentBarHeight = (value: number, maxValue: number) => {
  // 确保maxValue有值，如果没有就使用计算属性的值或提供默认值
  if (!maxValue && maxValue !== 0) {
    maxValue = 1; // 绝对保底值
  }

  const maxHeight = 80; // 最大高度像素
  const minHeight = 10; // 最小高度像素
  if (value === 0) return 0;
  const height = (value / maxValue) * maxHeight;
  return Math.max(minHeight, height); // 确保有最小高度
};

// 账单列表
const billList = computed(() => {
  if (!vm.state.aggregatedShiftReport?.payBillVOs) return [];
  console.log('[billList] vm.state.aggregatedShiftReport.payBillVOs:', vm.state.aggregatedShiftReport.payBillVOs);

  // 1. 创建billId到bill的映射
  const billMap = new Map();

  // 2. 首先处理所有非退款账单，添加到映射
  const nonRefundBills = vm.state.aggregatedShiftReport.payBillVOs.filter(bill => bill.direction !== 'refund');
  nonRefundBills.forEach(bill => {
    billMap.set(bill.billId, {
      id: bill.billId,
      room: bill.roomName,
      sessionId: bill.sessionId,
      timestamp: bill.finishTime || bill.ctime,
      amount: bill.totalFee,
      isRefunded: false,
      roomId: bill.roomId,
      status: bill.status,
      refundAmount: 0, // 初始化退款金额为0
      refundBills: [], // 添加关联的退款账单列表
      direction: bill.direction,
      isBack: bill.isBack
    });
  });

  // 3. 处理退款账单，合并到原账单
  const refundBills = vm.state.aggregatedShiftReport.payBillVOs.filter(bill => bill.direction === 'refund');
  refundBills.forEach(refundBill => {
    // 通过billPid找到原始账单
    const originalBill = billMap.get(refundBill.billPid);
    if (originalBill) {
      // 累加退款金额
      originalBill.refundAmount += refundBill.totalFee || 0;
      // 标记为已退款
      originalBill.isRefunded = true;
      // 将退款账单加入到关联列表中
      originalBill.refundBills.push({
        id: refundBill.billId,
        pid: refundBill.billPid,
        direction: refundBill.direction,
        isBack: refundBill.isBack,
        amount: refundBill.totalFee
      });
    } else {
      // 如果找不到原始账单，则作为独立的退款账单添加
      billMap.set(refundBill.billId, {
        id: refundBill.billId,
        pid: refundBill.billPid,
        room: refundBill.roomName,
        sessionId: refundBill.sessionId,
        timestamp: refundBill.finishTime || refundBill.ctime,
        amount: refundBill.totalFee,
        isRefunded: false,
        roomId: refundBill.roomId,
        status: refundBill.status,
        refundAmount: 0,
        refundBills: [],
        direction: refundBill.direction,
        isBack: refundBill.isBack
      });
    }
  });

  // 4. 转换为数组并格式化日期
  return Array.from(billMap.values()).map(bill => {
    const date = new Date(bill.timestamp * 1000);
    return {
      ...bill,
      date: `${date.getFullYear().toString()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`,
      time: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`,
      // 显示的金额 = 原金额 - 退款金额
      amount: bill.amount - bill.refundAmount
    };
  });
});







// 处理交班上报
const handleSubmitHandover = async () => {
  if (!vm.computed.hasData.value) {
    return;
  }

  try {
    await vm.actions.submitHandover();
  } catch (error) {
    console.error('交班上报错误:', error);
  }
};

// 组件加载完成后自动查询
// 立即发起请求
vm.actions.queryShiftReport();

// 监听aggregatedShiftReport变化，发送交班时间范围
watch(
  () => vm.state.aggregatedShiftReport,
  newReport => {
    if (newReport && newReport.payBillVOs && newReport.payBillVOs.length > 0) {
      // 计算最小和最大账单日期
      let minTime = Number.MAX_SAFE_INTEGER;
      let maxTime = 0;

      // 遍历所有账单获取最早和最晚的时间戳
      newReport.payBillVOs.forEach(bill => {
        const billTime = bill.finishTime || bill.ctime;
        if (billTime && billTime > 0) {
          minTime = Math.min(minTime, billTime);
          maxTime = Math.max(maxTime, billTime);
        }
      });

      // 确保找到了有效的时间范围
      if (minTime !== Number.MAX_SAFE_INTEGER && maxTime > 0) {
        // 发送事件到父组件，传递时间范围
        emit('update:shiftTimeRange', {
          startTime: minTime,
          endTime: maxTime
        });
      }
    }
  },
  { immediate: false, deep: true }
);

// 定义emit
const emit = defineEmits<{
  (e: 'update:shiftTimeRange', range: { startTime: number; endTime: number }): void;
}>();

// 暴露方法给父组件
defineExpose({
  submitHandover: handleSubmitHandover,
  printShiftReport: () => vm.actions.printShiftReport(),
  get hasData() {
    return vm.computed.hasData.value;
  },
  vm // 暴露整个vm对象
});

// 格式化单号的函数
const formatBillId = (billId: string) => {
  if (!billId) return '';
  const shortId = billId.length > 6 ? `${billId.substring(0, 2)}**${billId.substring(billId.length - 4)}` : billId;
  return shortId;
};

// 获取账单显示的完整ID，包含退款标记
const getDisplayBillId = (bill: any) => {
  const shortId = formatBillId(bill.id);

  // 根据账单类型添加前缀
  if (bill.direction === 'refund') {
    // 如果是退款且账单还原
    if (bill.isBack) {
      return `【还】${shortId}`;
    }
    // 如果是普通退款
    return `【退】${shortId}`;
  }

  // 普通账单
  return shortId;
};

// 获取账单关联ID的显示内容
const getRelatedBillIds = (bill: any) => {
  if (!bill.refundBills || bill.refundBills.length === 0) {
    return '';
  }

  return bill.refundBills
    .map((refundBill: any) => {
      const shortId = formatBillId(refundBill.id);
      if (refundBill.isBack) {
        return `【还】${shortId}`;
      }
      return `【退】${shortId}`;
    })
    .join('\n');
};
</script>

<style scoped>
:deep(.el-table th) {
  color: #606266;
  font-weight: 500;
}

.label-width {
  width: 100px;
}

.value-width {
  width: 120px;
}

:deep(.big-text .price-unit) {
  font-size: 24px;
}

:deep(.big-text .price-integer) {
  font-size: 56px;
}

:deep(.big-text .price-decimal) {
  font-size: 24px;
}

:deep(.el-table .el-table__header-wrapper) {
  background-color: #f5f5f5;
}

/* 账单表格行可点击样式 */
:deep(.bill-table-clickable .el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

:deep(.bill-table-clickable .el-table__row:hover) {
  background-color: #f0f9ff !important;
}
</style>
