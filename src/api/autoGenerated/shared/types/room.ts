// room相关类型定义
import { SessionOrderDataVO, OrderRoomPlanVO, V3AddOrderOpenContinuePayReqDto } from './order';
import { AreaVO, HolidayVO } from './other';
import { BookingVO } from './booking';
import { OrderPricePlanVO, BuyoutPricePlanVO, PricePlanVO, TimePricePlanVO } from './price';
import { SessionVO } from './member';

export type AddRoomExceptionReqDto = {
    /** 异常描述 */
    exceptionDescription?: string;
    /** 异常时间 */
    exceptionTime?: number;
    /** 异常类型 */
    exceptionType?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人名称 */
    operatorName?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
  };

export type AddRoomFaultReqDto = {
    /** 故障描述 */
    faultDescription?: string;
    /** 故障时间 */
    faultTime?: number;
    /** 故障类型 */
    faultType?: string;
    /** 房间ID */
    roomId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddRoomGreetingReqDto = {
    /** 适用房型 */
    applicableRoomTypes?: string;
    /** 问候内容 */
    content?: string;
  };

export type AddRoomReqDto = {
    /** 区域ID */
    areaId?: string;
    /** 关闭时间 */
    closeTime?: number;
    /** 颜色 */
    color?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 显示项目 */
    displayItems?: string;
    /** 高消费警报阈值 */
    highConsumptionAlert?: number;
    /** 室内照片 */
    interiorPhoto?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间名称 */
    name?: string;
    /** 开放时间 */
    openTime?: number;
    /** 价格方案ID */
    pricePlanId?: string;
    /** 二维码 */
    qrCode?: string;
    /** 场次ID */
    sessionId?: string;
    /** 房间状态 */
    status?: string;
    /** 主题ID */
    themeId?: string;
    /** 房间类型ID */
    typeId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddRoomThemeReqDto = {
    /** 主题描述 */
    description?: string;
    /** 主题图片URL */
    imageUrl?: string;
    /** 是否显示该主题 */
    isDisplayed?: boolean;
    /** 房间主题名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddRoomTypeReqDto = {
    /** 消费模式 */
    consumptionMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 高消费警报金额 */
    highConsumptionAlert?: number;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间类型名称 */
    name?: string;
    /** 房间类型照片 */
    photo?: string;
    /** 备注 */
    remark?: string;
    /** 买钟基础价格方案 */
    timeChargeBasePlan?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type CloseRoomReqDto = {
    /** 异常描述 */
    exceptionDescription?: string;
    /** 关闭原因 */
    reason?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
  };

export type CreateRoomExtensionPrintRecordReqDto = {
    /** 订单编号数组，用于获取订单详情来构建续房单数据 */
    orderNos?: string[];
    /** 续房单号/场次ID */
    sessionId: string;
    /** 门店ID */
    venueId: string;
  };

export type DeleteRoomExceptionReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteRoomFaultReqDto = {
    /** ID */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type DeleteRoomGreetingReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteRoomReqDto = {
    /** ID */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type DeleteRoomThemeReqDto = {
    /** ID */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type DeleteRoomTypeReqDto = {
    /** ID */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type getApiPrintRecordRoomExtensionSessionParams = {
    /** 会话ID */
    sessionId: string;
    /** 门店ID */
    venueId: string;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoRoomVO = {
    data?: RoomVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type QueryRoomExceptionReqDto = {
    /** 异常描述 */
    exceptionDescription?: string;
    /** 异常时间 */
    exceptionTime?: number;
    /** 异常类型 */
    exceptionType?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 操作人ID */
    operatorId?: string;
    /** 操作人名称 */
    operatorName?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页条数 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
  };

export type QueryRoomFaultReqDto = {
    /** 故障描述 */
    faultDescription?: string;
    /** 故障时间 */
    faultTime?: number;
    /** 故障类型 */
    faultType?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryRoomGreetingReqDto = {
    /** 适用房型 */
    applicableRoomTypes?: string;
    /** 问候内容 */
    content?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryRoomReqDto = {
    /** 区域ID */
    areaId?: string;
    /** 结束时间大于 */
    closeTimeGt?: number;
    /** 消费模式 */
    consumptionMode?: string;
    /** ID */
    id?: string;
    /** ID */
    ids?: string[];
    /** 是否删除 */
    isDeleted?: boolean;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 价格方案ID */
    pricePlanId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 场次ID不为空 */
    sessionIdNotEmpty?: boolean;
    /** 房间状态 */
    status?: string;
    /** 房间状态列表 */
    statusIn?: string[];
    /** 主题ID */
    themeId?: string;
    /** 房间类型ID */
    typeId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryRoomStageReqDto = {
    /** 区域ID */
    areaId?: string;
    /** 预约ID */
    bookingId?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 房间类型ID */
    typeId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryRoomThemeReqDto = {
    /** 主题描述 */
    description?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 主题图片URL */
    imageUrl?: string;
    /** 是否显示该主题 */
    isDisplayed?: boolean;
    /** 房间主题名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryRoomTypeReqDto = {
    /** 消费模式 */
    consumptionMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 高消费警报金额 */
    highConsumptionAlert?: number;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间类型名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间类型照片 */
    photo?: string;
    /** 备注 */
    remark?: string;
    /** 买钟基础价格方案 */
    timeChargeBasePlan?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoRoomTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoRoomVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRoomExceptionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomExceptionVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRoomExtensionPrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomExtensionPrintRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRoomFaultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomFaultVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRoomGreetingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomGreetingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRoomStageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomStageVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRoomThemeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomThemeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoRoomTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoRoomVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoRoomVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoRoomVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRoomExceptionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomExceptionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRoomExtensionPrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomExtensionPrintRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRoomFaultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomFaultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRoomGreetingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomGreetingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRoomThemeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoomThemeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type RoomExceptionVO = {
    /** 创建时间 */
    ctime?: number;
    /** 异常描述 */
    exceptionDescription?: string;
    /** 异常时间 */
    exceptionTime?: number;
    /** 异常类型 */
    exceptionType?: string;
    /** ID */
    id?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人名称 */
    operatorName?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
    /** 版本 */
    version?: number;
  };

export type RoomExtensionPrintRecordVO = {
    /** 创建时间 */
    createTime?: number;
    /** 打印设备名称 */
    deviceName?: string;
    /** 错误信息 */
    errorMsg?: string;
    /** 打印记录ID */
    id?: string;
    /** 操作员ID */
    operatorId?: string;
    /** 操作员姓名 */
    operatorName?: string;
    /** 打印单号 */
    printNo?: string;
    /** 打印时间 */
    printTime?: number;
    /** 打印类型 */
    printType?: string;
    /** 备注 */
    remark?: string;
    /** 场次ID */
    sessionId?: string;
    /** 续房单特有字段（与开台单数据结构完全一致） */
    sessionOrderData?: SessionOrderDataVO;
    /** 打印状态 */
    status?: number;
    /** 更新时间 */
    updateTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type RoomFaultVO = {
    /** 创建时间 */
    ctime?: number;
    /** 故障描述 */
    faultDescription?: string;
    /** 故障时间 */
    faultTime?: number;
    /** 故障类型 */
    faultType?: string;
    /** ID */
    id?: string;
    /** 房间ID */
    roomId?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type RoomGreetingVO = {
    /** 适用房型 */
    applicableRoomTypes?: string;
    /** 问候内容 */
    content?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type RoomInfo = {
    /** 房间名称 */
    name?: string;
  };

export type RoomOperationVO = {
    /** 创建时间 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 订单类型 opening:开台/续台、attach:联房、merge:并房 */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type RoomPackageInfo = {
    /** 现价 */
    actualPrice?: number;
    /** 时长（分钟） */
    duration?: number;
    /** 原价 */
    originalPrice?: number;
    /** 方案名称 */
    planName?: string;
  };

export type RoomStageVO = {
    /** 区域信息 */
    areaVO?: AreaVO;
    /** 预定信息 */
    bookingVOs?: BookingVO[];
    /** 价格计划信息-原始 */
    orderPricePlanVOs?: OrderPricePlanVO[];
    /** 价格计划信息-拆分 */
    orderRoomPlanVOs?: OrderRoomPlanVO[];
    roomOperationVO?: RoomOperationVO;
    /** 主题信息 */
    roomThemeVO?: RoomThemeVO;
    /** 房间类型信息 */
    roomTypeVO?: RoomTypeVO;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 场次信息 */
    sessionVO?: SessionVO;
    /** 场次联合ID 联房时用-联房的主房间ID */
    unionRoomId?: string;
    unionRoomName?: string;
  };

export type RoomThemeVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 主题描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 主题图片URL */
    imageUrl?: string;
    /** 是否显示该主题 */
    isDisplayed?: boolean;
    /** 房间主题名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type RoomType = {
    /** 房间类型ID */
    id?: string;
    /** 房间类型名称 */
    name?: string;
  };

export type RoomTypeConfig = {
    /** 房间类型列表 */
    roomTypes?: RoomType[];
  };

export type RoomTypeVO = {
    /** 消费模式 */
    consumptionMode?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 高消费警报金额 */
    highConsumptionAlert?: number;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间类型名称 */
    name?: string;
    /** 房间类型照片 */
    photo?: string;
    /** 备注 */
    remark?: string;
    /** 状态值 */
    state?: number;
    /** 买钟基础价格方案 */
    timeChargeBasePlan?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type RoomVO = {
    /** 区域ID */
    areaId?: string;
    /** 区域信息 */
    areaVO?: AreaVO;
    /** 基准买钟钟点价格 */
    baseTimePriceFee?: number;
    /** 预定信息 */
    bookingVOs?: BookingVO[];
    /** 买断价格计划信息 - 新版 */
    buyoutPricePlanVOs?: BuyoutPricePlanVO[];
    /** 颜色 */
    color?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 创建时间 */
    ctime?: number;
    /** 当前时间 */
    currentTime?: number;
    /** 设备IP */
    deviceIp?: string;
    /** 显示项目 */
    displayItems?: string;
    /** 高消费警报阈值 */
    highConsumptionAlert?: number;
    /** 节假日信息 */
    holidayVOs?: HolidayVO[];
    /** ID */
    id?: string;
    /** 室内照片 */
    interiorPhoto?: string;
    /** 是否删除 */
    isDeleted?: boolean;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间名称 */
    name?: string;
    /** 价格计划信息 - 旧版 */
    pricePlanVOs?: PricePlanVO[];
    /** 二维码 */
    qrCode?: string;
    /** 主题信息 */
    roomThemeVO?: RoomThemeVO;
    /** 房间类型信息 */
    roomTypeVO?: RoomTypeVO;
    /** 时序器IP */
    sequencerIp?: string;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 房间状态 :idle/in_use/fault/cleaning/guest */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 主题ID */
    themeId?: string;
    /** 计时价格计划信息 - 新版 */
    timePricePlanVOs?: TimePricePlanVO[];
    /** 房间类型ID */
    typeId?: string;
    /** 更新时间 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type UpdateRoomExceptionReqDto = {
    /** 异常描述 */
    exceptionDescription?: string;
    /** 异常时间 */
    exceptionTime?: number;
    /** 异常类型 */
    exceptionType?: string;
    /** ID */
    id?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人名称 */
    operatorName?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
  };

export type UpdateRoomFaultReqDto = {
    /** 故障描述 */
    faultDescription?: string;
    /** 故障时间 */
    faultTime?: number;
    /** 故障类型 */
    faultType?: string;
    /** ID */
    id?: string;
    /** 房间ID */
    roomId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateRoomGreetingReqDto = {
    /** 适用房型 */
    applicableRoomTypes?: string;
    /** 问候内容 */
    content?: string;
    /** ID */
    id?: string;
  };

export type UpdateRoomReqDto = {
    /** 区域ID */
    areaId?: string;
    /** 关闭时间 */
    closeTime?: number;
    /** 颜色 */
    color?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 设备IP */
    deviceIp?: string;
    /** 显示项目 */
    displayItems?: string;
    /** 高消费警报阈值 */
    highConsumptionAlert?: number;
    /** ID */
    id?: string;
    /** 室内照片 */
    interiorPhoto?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间名称 */
    name?: string;
    /** 开放时间 */
    openTime?: number;
    /** 价格方案ID */
    pricePlanId?: string;
    /** 二维码 */
    qrCode?: string;
    /** 时序器IP */
    sequencerIp?: string;
    /** 场次ID */
    sessionId?: string;
    /** 房间状态 */
    status?: string;
    /** 主题ID */
    themeId?: string;
    /** 房间类型ID */
    typeId?: string;
    /** 门店门店ID */
    venueId?: string;
  };

export type UpdateRoomThemeReqDto = {
    /** 主题描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 主题图片URL */
    imageUrl?: string;
    /** 是否显示该主题 */
    isDisplayed?: boolean;
    /** 房间主题名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateRoomTypeReqDto = {
    /** 消费模式 */
    consumptionMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 高消费警报金额 */
    highConsumptionAlert?: number;
    /** ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 房间类型名称 */
    name?: string;
    /** 房间类型照片 */
    photo?: string;
    /** 备注 */
    remark?: string;
    /** 买钟基础价格方案 */
    timeChargeBasePlan?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type V3LockRoomReqDto = {
    employeeId?: string;
    roomId?: string;
    sessionId?: string;
    venueId?: string;
  };

export type V3MergeRoomReqDto = {
    employeeId?: string;
    sessionVOOpeningA?: SessionVO;
    sessionVOOpeningB?: SessionVO;
    venueId?: string;
  };

export type V3QueryRoomFaultReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 房间ID */
    roomId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3QueryRoomInfoByQRCodeReqDto = {
    /** 房间ID */
    roomId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3QueryRoomStageReqDto = {
    /** 区域ID */
    areaId?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 房间类型ID */
    typeId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3SwapRoomReqDto = {
    employeeId?: string;
    sessionVOOpeningA?: SessionVO;
    sessionVOOpeningB?: SessionVO;
    venueId?: string;
  };

export type V3TransferRoomReqDto = {
    /** 是否替换原包房配送 */
    IsReplaceOriginal?: boolean;
    employeeId?: string;
    /** 开台信息 */
    orderOpen?: V3AddOrderOpenContinuePayReqDto;
    sessionVOIdle?: SessionVO;
    sessionVOOpening?: SessionVO;
    /** 按原房间套餐转台:bySrc, 按目标房间转台:byDst */
    transferType?: string;
    venueId?: string;
  };

export type V3UnlockRoomReqDto = {
    employeeId?: string;
    roomId?: string;
    sessionId?: string;
    venueId?: string;
  };
