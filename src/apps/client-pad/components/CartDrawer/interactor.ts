import { CartItem } from '@/apps/client-pad/views/ProductOrder/viewmodel';
import { postApiV3OrderAdditionalOrder } from '@/api/autoGenerated/v3Version/order';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { ElMessage } from 'element-plus';
import { CartDrawerConverter } from './converter';

/**
 * 购物车交互器类
 * 处理与业务数据的交互
 */
export class CartDrawerInteractor {
  /**
   * 提交订单
   * @param cartItems 购物车商品列表
   * @param roomId 房间ID
   * @param sessionId 会话ID
   * @param employeeId 员工ID
   * @returns 提交结果
   */
  public static async submitOrder(cartItems: CartItem[], roomId: string, sessionId: string, employeeId: string = ''): Promise<boolean> {
    try {
      console.log('调用立即下单API', { cartItems, roomId, sessionId, employeeId });

      // 获取设备门店信息
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;

      if (!venueId) {
        console.error('未找到门店ID');
        return false;
      }

      // 计算订单金额
      const totalAmount = CartDrawerConverter.calculateTotalAmount(cartItems);

      // 转换商品数据为API参数格式
      const orderProductVOs = cartItems.map(item => ({
        id: '',
        venueId: venueId,
        roomId: roomId,
        sessionId: sessionId,
        productName: item.name,
        flavors: '',
        quantity: item.quantity,
        unit: item.unit || '份',
        payPrice: item.currentPrice,
        originalPrice: item.currentPrice,
        payAmount: item.currentPrice * item.quantity,
        originalAmount: item.currentPrice * item.quantity,
        payStatus: 'unpaid',
        productId: item.id,
        inPackageTag: 'no',
        // 如果是套餐，添加套餐商品信息
        packageProductInfo:
          item.isPackage && item.packageDetail ? (typeof item.packageDetail === 'string' ? item.packageDetail : JSON.stringify(item.packageDetail)) : undefined
      }));

      // 构建请求参数
      const params = {
        sessionId,
        venueId,
        roomId,
        employeeId,
        payAmount: totalAmount,
        originalAmount: totalAmount,
        orderProductVOs
      };

      console.log('订单立结参数:', JSON.stringify(params, null, 2));

      // 调用订单立结API
      const response = await postApiV3OrderAdditionalOrder(params);

      if (response && response.code === 0) {
        console.log('下单成功:', response);
        return true;
      } else {
        console.error('下单失败:', response);
        const errorMsg = typeof response === 'object' && 'message' in response ? String(response.message) : '下单失败';
        ElMessage.error(errorMsg);
        return false;
      }
    } catch (error) {
      console.error('下单出错:', error);
      ElMessage.error('下单出错，请重试');
      return false;
    }
  }
}
