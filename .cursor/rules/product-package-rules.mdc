---
description: 
globs: 
alwaysApply: false
---
# 商品套餐逻辑规则

## 套餐基本原则

Thunder ERP系统中的商品套餐遵循以下基本原则：

1. **套餐模式**：商品套餐分为两种模式：
   - **固定套餐**：所有商品固定，不可更改
   - **自定义套餐**：包含固定商品和可选商品组

2. **套餐定价**：单一套餐的总价是固定的，不随内部商品选择变化而变化

3. **套餐结构**：套餐由以下部分组成：
   - **固定商品**：必选项，不可更改
   - **可选商品组**：用户可选择的商品组

## 可选商品组

### 类型定义

可选商品组分为两种类型：

1. **按数量选择 (by_count)**
   - 规则：从商品组中选择指定数量的商品
   - 示例：从10种小吃中选择3种

2. **按方案选择 (by_plan)**
   - 规则：从预设的几种方案中选择一种
   - 示例：主食可选"米饭方案"或"面食方案"

### 数据结构

```typescript
interface ProductPackage {
  id: string;
  name: string;
  price: number;
  mode: 'fixed' | 'customizable';
  fixedProducts: Product[];
  optionalGroups: OptionalGroup[];
}

interface OptionalGroup {
  id: string;
  name: string;
  type: 'by_count' | 'by_plan';
  // 按数量选择时的配置
  countConfig?: {
    totalCount: number;  // 需要选择的商品数量
    options: Product[];  // 可选的商品列表
  };
  // 按方案选择时的配置
  planConfig?: {
    plans: Plan[];      // 可选的方案列表
  };
  defaultSelection: string[] | string; // 默认选择的商品ID或方案ID
}

interface Plan {
  id: string;
  name: string;
  products: Product[];
}
```

## 默认项选择算法

系统为可选商品组提供默认选项，以简化用户操作：

### 按数量选择 (by_count) 的默认算法

1. **优先级规则**：
   - 首先根据商品的销量排序，选择销量最高的N个商品
   - 如果销量相同，则根据利润率排序，选择利润率最高的
   - 如果仍然相同，则按添加到系统的时间顺序选择最新的

2. **代码实现**：
   ```typescript
   function getDefaultByCount(options: Product[], count: number): string[] {
     // 按销量、利润率、时间排序
     const sortedProducts = options.sort((a, b) => {
       if (a.salesVolume !== b.salesVolume) {
         return b.salesVolume - a.salesVolume;
       }
       if (a.profitRate !== b.profitRate) {
         return b.profitRate - a.profitRate;
       }
       return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
     });
     
     // 返回前count个商品的ID
     return sortedProducts.slice(0, count).map(p => p.id);
   }
   ```

### 按方案选择 (by_plan) 的默认算法

1. **优先级规则**：
   - 首选标记为"推荐"的方案
   - 如果没有推荐方案，则选择平均销量最高的方案
   - 如果仍然无法确定，则选择方案列表中的第一个

2. **代码实现**：
   ```typescript
   function getDefaultByPlan(plans: Plan[]): string {
     // 查找推荐方案
     const recommendedPlan = plans.find(p => p.isRecommended);
     if (recommendedPlan) {
       return recommendedPlan.id;
     }
     
     // 计算每个方案的平均销量
     const planWithAvgSales = plans.map(plan => {
       const totalSales = plan.products.reduce((sum, p) => sum + p.salesVolume, 0);
       const avgSales = totalSales / plan.products.length;
       return { planId: plan.id, avgSales };
     });
     
     // 排序并返回销量最高的方案ID
     planWithAvgSales.sort((a, b) => b.avgSales - a.avgSales);
     return planWithAvgSales[0]?.planId || plans[0]?.id;
   }
   ```

## 套餐管理建议

1. 创建套餐时，系统应自动根据以上算法计算默认选项
2. 套餐价格应明确，与内部商品价格分开管理
3. 可选商品组的UI展示应根据类型有所区别
   - by_count类型应展示为多选框
   - by_plan类型应展示为单选方案列表
